<template>
  <view>
    <view
      v-if="productList?.length !== 0"
      class="product-group-title flex-center-between"
    >
      <view class="text-26 text-bold flex-1 text-1-row">
        {{ categoryName }}
      </view>
      <view class="flex-center" style="width: fit-content" @click="navigateToSearch">
        <view class="text-24" style="color: #005bac">
          查看全部
        </view>
        <v-icon
          size="22rpx"
          margin-left="10rpx"
          src="/static/icons/right-arrow.png"
        />
      </view>
    </view>
    <template v-for="(product, productIndex) in productList" :key="product.id">
      <view v-if="productIndex !== 0" class="divider" />
      <view class="product-item-container">
        <product-item
          :product="product"
          @select="() => $emit('select', product)"
        />
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import ProductItem from "./product-item.vue"

const props = defineProps<{
  categoryId: number
  categoryName: string
  productList: ProductCategoryItem[]
}>()

interface Emits {
  (event: "select", val: ProductCategoryItem): void
}
defineEmits<Emits>()

const { categoryId, categoryName } = toRefs(props)
const navigateToSearch = () => {
  uni.navigateTo({
    url: `/package-product/product/product-search?categoryid=${categoryId.value}&categoryname=${categoryName.value}`
  })
}
</script>

<style lang="scss" scoped>
.product-group-title {
  position: sticky;
  top: 0;
  padding: 0 18rpx 0 24rpx;
  height: 78rpx;
  box-sizing: border-box;
  background-color: white;
  z-index: 1;
}
.divider {
  margin: 0 24rpx;
}
.product-item-container {
  padding: 8rpx 24rpx;
}
</style>
