<template>
  <view class="v-popover">
    <view class="v-mask-container">
      <v-transition type="fade" :show="modelValue">
        <view
          class="v-popover-mask"
          :style="popoverStyle"
          @click="closeHandle"
        />
      </v-transition>
    </view>
    <view class="v-popover-container" :style="popoverStyle">
      <v-transition
        type="collapse"
        :show="modelValue"
        timing="ease-out"
        :custom-class="popoverClass"
      >
        <slot />
      </v-transition>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  modelValue?: boolean
  mode?: string,
  top?: string,
  bottom?: string
}>(), {
  modelValue: false,
  mode: "bottom",
  top: "0",
  bottom: "0"
})

interface Emits {
  (event: "update:model-value", val: boolean): void,
  (event: "close"): void
}

const emits = defineEmits<Emits>()

const mode = toRef(props, "mode")
const top = toRef(props, "top")
const bottom = toRef(props, "bottom")

const popoverStyle = computed(() => {
  switch (true) {
    case mode.value === "top":
      return `top: ${top.value}`
    case mode.value === "bottom":
      return `bottom: ${bottom.value}`
  }
})
const popoverClass = computed(() => {
  switch (true) {
    case mode.value === "top":
      return "flex-col flex--end"
    case mode.value === "bottom":
      return "flex-start--end"
  }
})

const modelValue = toRef(props, "modelValue")
/** 点击锁 */
let lockClickMask = false
let lockClickMaskTimer: number| undefined
watch(modelValue, (val) => {
  if (val) {
    lockClickMask = true
    clearTimeout(lockClickMaskTimer)
    lockClickMaskTimer = setTimeout(() => {
      lockClickMask = false
    }, 500)
  } else {
    clearTimeout(lockClickMaskTimer)
    lockClickMask = false
  }
})
const closeHandle = () => {
  if (lockClickMask) return
  emits("update:model-value", false)
  emits("close")
}
</script>

<style lang="scss" scoped>
.v-popover {
  .v-mask-container {
    position: fixed;
    z-index: 999;
  }
  .v-popover-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.35);
  }
  .v-popover-container {
    position: fixed;
    left: 0;
    right: 0;
    height: fit-content;
    z-index: 1000;
  }
}
</style>
