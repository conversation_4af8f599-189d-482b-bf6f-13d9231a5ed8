<template>
  <scroll-view
    v-if="pageHeight"
    scroll-y
    :style="{ height: `${pageHeight}px` }"
    enable-back-to-top
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="refresh"
    @scrolltolower="getRewardsPointsReceiveRecordList"
  >
    <template v-if="!refreshing">
      <v-loading-block v-if="loading" />
      <v-empty
        v-else-if="status === 'nomore' && exchangeRecordList.length === 0"
        src="/static/image/empty-coupon.png"
      >
        <view> 暂无积分兑换记录 </view>
      </v-empty>
      <template v-else>
        <view
          v-for="(record, recordIndex) in exchangeRecordList"
          :key="recordIndex"
        >
          <view class="month-gap">
            {{ `${record.month}月` }}
          </view>
          <view
            v-for="item in record.recordList"
            :key="item.rewardsPointsReceiveRecordId"
            class="v-block"
            @click="() => navigateToDetails(item.rewardsPointsReceiveRecordId)"
          >
            <view class="flex-center">
              <view class="image-container">
                <v-image
                  height="160rpx"
                  width="160rpx"
                  mode="aspectFit"
                  :src="item.couponViceIMG"
                />
              </view>
              <view class="info-list flex flex-col">
                <view class="item-name">
                  {{ item.couponName }}
                </view>
                <view class="item-cost">
                  {{ `${item.integrate}积分` }}
                </view>
                <view class="item-time">
                  {{ `积分兑换时间:${formateUtc(item.receiveTime)}` }}
                </view>
              </view>
              <view v-if="item.isUsed === 0" class="tag unused">
                未使用
              </view>
              <view v-else-if="item.isUsed === 1" class="tag used">
                已使用
              </view>
            </view>
          </view>
        </view>
        <v-loadmore
          v-if="!refreshing"
          :status="status"
          @click="getRewardsPointsReceiveRecordList"
        />
      </template>
    </template>
    <view class="page-padding-bottom" />
  </scroll-view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
import { str2num } from "@/utils/type-transfer"

defineProps<{
  pageHeight: number
}>()

const exchangeRecordList = ref<
{
  year: number
  month: number
  recordList: ExchangeRecord[]
}[]
>([])

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  initExchangeRecordList()
}
const initExchangeRecordList = async () => {
  loading.value = true
  pageIndex.value = 1
  exchangeRecordList.value = []
  status.value = "loadmore"
  await getRewardsPointsReceiveRecordList()
}
/** 分页数据 */
const pageIndex = ref(1)
const total = ref(0)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")

/** 获取积分记录列表 */
const getRewardsPointsReceiveRecordList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  const response = await $api.getRewardsPointsReceiveRecordList({
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    orderField: "receiveTime",
    sortType: "desc"
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      const list = response.data.result
      for (let i = 0; i < list.length; i += 1) {
        const item = list[i]
        const date = new Date(item.receiveTime)
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        const record = exchangeRecordList.value.find(
          (item) => item.year === year && item.month === month
        )
        if (record) {
          record.recordList.push(item)
        } else {
          exchangeRecordList.value.push({
            year,
            month,
            recordList: [item]
          })
        }
        total.value += 1
      }
      const totalCount = str2num(response.data.totalCount)
      status.value = total.value < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
  loading.value = false
}

const navigateToDetails = (id: string) => {
  uni.navigateTo({ url: `/package-points/points/points-exchange-record-details?id=${id}` })
}
defineExpose({ initExchangeRecordList })
</script>

<style lang="scss" scoped>
.month-gap {
  position: sticky;
  top: 0;
  padding: 24rpx;
  font-weight: bold;
  color: #666666;
  background-color: #f5f5f5;
}
.v-block {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  .image-container {
    height: 160rpx;
    width: 160rpx;
  }
  .info-list {
    flex: 1;
    align-self: stretch;
    margin-left: 20rpx;
    padding: 12rpx 0;
    overflow: hidden;
    .item-name {
      font-weight: bold;
      color: #231815;
    }
    .item-cost {
      flex: 1;
      font-size: 24rpx;
      color: #999;
    }
    .item-time {
      font-size: 20rpx;
      color: #999;
    }
  }
  .tag {
    margin-left: 10rpx;
    height: 50rpx;
    width: 100rpx;
    font-size: 20rpx;
    line-height: 50rpx;
    text-align: center;
    border-radius: 26rpx;
    &.unused {
      background-color: rgba(4, 172, 0, 0.1);
      color: #04ac00;
    }
    &.used {
      background-color: rgb(153, 153, 153, 0.1);
      color: #999999;
    }
  }
}
</style>
