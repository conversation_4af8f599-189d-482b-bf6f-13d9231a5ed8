const tokenStorageName = "access_token"
const tokenExpiresTime = "access_token_expires_time"

/** 清除token */
export const clearToken = async () => {
  uni.removeStorageSync(tokenStorageName)
  uni.removeStorageSync(tokenExpiresTime)
  uni.removeStorageSync("user_avatar")
  uni.removeStorageSync("user_name")
  uni.removeStorageSync("user_phone")
}

/** 刷新token */
export const refreshToken = async (
  accessToken: string,
  tokenExpires: number
) => {
  uni.setStorageSync(tokenStorageName, accessToken)
  uni.setStorageSync(tokenExpiresTime, tokenExpires)
  // 这里让它重新查看这个广告
  uni.removeStorageSync("popperImg")
  uni.removeStorageSync("popperUrl")
}

/** 检查token是否可用 */
export const checkToken = () => {
  const accessToken = uni.getStorageSync(tokenStorageName)
  const tokenExpires = parseInt(uni.getStorageSync(tokenExpiresTime))
  const now = new Date().getTime()
  switch (true) {
    case !accessToken:
    case !tokenExpires:
    case isNaN(tokenExpires):
    case tokenExpires < now:
      clearToken()
      return false
    default:
      return true
  }
}

/** 读取token 已拼接 直接使用 */
export const getToken = async () => {
  const accessToken = uni.getStorageSync(tokenStorageName)
  return `Bearer ${accessToken}`
}
