//const baseUrl = "https://www.welshine.shop/applet"
// const baseUrl = "https://wx.shop.cheerray.com"
const baseUrl = "https://wx.sh.welshine.shop"
//const baseUrl = "http://47.119.48.183:8070"
let webName = ""

const kfid = "kfcd271a202428d5edf" // 开发、测试 kfc24d79d61f7da4981 正式 kfcd271a202428d5edf 千芮 kfca4f3e7bb867af787
const corpId = "ww0bcc1f28fd587bdd" // 开发、测试 wwb612f4203c7ac868 正式 ww0bcc1f28fd587bdd 千芮 wwabea2e390b278df3
const customerUrl = "https://work.weixin.qq.com/kfid/" + kfid

// 根据 baseUrl 设置 webName
if (baseUrl.includes("cheerray")) {
    webName = "cheerray"
}
else if (baseUrl.includes("sh.welshine")) {
    webName = "sh.welshine"
}
else{
    webName = "welshine"
}

export { baseUrl, customerUrl, corpId , webName}
