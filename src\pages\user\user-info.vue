<template>
  <view>
    <view class="page-header">
      <v-navbar title="更新头像/昵称" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <view class="avatar-container">
        <button
          class="button"
          open-type="chooseAvatar"
          @chooseavatar="chooseavatar"
        >
          <v-image
            v-if="avatarUrl"
            mode="aspectFill"
            height="140rpx"
            width="140rpx"
            border-radius="70rpx"
            :src="avatarUrl"
          />
          <v-image
            v-else
            height="140rpx"
            width="140rpx"
            src="/static/image/empty-avatar.svg"
          />
        </button>
      </view>
      <view class="nickname-container">
        <view class="label">
          昵称:
        </view>
        <view class="value">
          <input
            v-model="nickName"
            type="nickname"
            maxlength="32"
            placeholder="微信用户"
            @change="change"
          >
        </view>
      </view>
    </scroll-view>
    <view class="page-footer">
      <view class="page-padding-top" />
      <view class="button-list">
        <view class="button-item">
          <v-button type="primary" height="94rpx" @click="submit">
            确定
          </v-button>
        </view>
      </view>
      <view class="page-padding-bottom" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { getToken } from "@/utils/auth/token"
const { pageHeight } = usePageLayout()

const avatarId = ref<number>()
const avatarUrl = ref<string>()
const nickName = ref("")

/** 上传头像到服务器 */
const uploadImage = async (filePath: string) => {
  const token = await getToken()
  uni.uploadFile({
    url: $api.config.baseUrl + "/wxapp/member/tempUploadFile",
    filePath,
    fileType: "image",
    name: "fileList",
    header: { Authorization: token },
    timeout: 3000,
    success: (res) => {
      const response = JSON.parse(res.data)
      if (response.code === "2000" && response.data?.[0]) {
        avatarId.value = response.data[0].fileId
        avatarUrl.value = response.data[0].fileUrl
      } else {
        uni.hideLoading()
        uni.showModal({ title: "上传头像失败", showCancel: false })
      }
    },
    fail: (err) => {
      uni.hideLoading()
      uni.showModal({ title: "上传头像出错", showCancel: false })
      console.error(err)
    }
  })
}

onLoad(() => {
  avatarUrl.value = uni.getStorageSync("user_avatar")
  nickName.value = uni.getStorageSync("user_name")
})

/** 选择用户头像 */
const chooseavatar = async (event: ChooseAvatar) => {
  uploadImage(event?.detail?.avatarUrl)
}

/** 这里获取点击昵称事件 */
const change = (ev: Event) => {
  const event = ev as InputEvent
  nickName.value = event?.detail?.value ?? ""
}

/** 更新信息 */
const submit = async () => {
  const response = await $api.updateMemberInfoWeChat({
    headIcon: avatarId.value,
    nickName: nickName.value
  })
  switch (response.__error) {
    case undefined:
      uni.hideLoading()
      uni.setStorageSync("user_avatar", avatarUrl.value)
      uni.setStorageSync("user_name", nickName.value)
      await uni.showModal({ title: "更新成功", showCancel: false })
      uni.switchTab({ url: "/pages/user/user-page" })
  }
}
</script>

<style lang="scss" scoped>
.avatar-container {
  height: 250rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  .button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: #666666;
    background-color: transparent;
    border-radius: 0;
    &::after {
      display: none;
    }
  }
}
.nickname-container {
  height: 100rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  .label {
    width: 80rpx;
    font-size: 30rpx;
    font-weight: bold;
    margin-right: 20rpx;
  }
  .value {
    flex: 1;
  }
}
.button-list {
  padding: 0 20rpx;
}
</style>
