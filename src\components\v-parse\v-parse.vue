<template>
  <view class="v-parse">
    <rich-text :nodes="content ?? ''" />
  </view>
</template>

<script setup lang="ts">
defineProps<{ content: string }>()
</script>

<style lang="scss" scoped>
.v-parse {
  padding: 20rpx;
  :deep(.ql-align-justify) {
    text-align: justify;
  }
  :deep(.ql-align-center) {
    text-align: center;
  }
  :deep(.ql-align-right) {
    text-align: right;
  }
}
</style>
