<template>
  <view>
    <view class="page-header">
      <v-navbar title="提交订单" back />
    </view>
    <scroll-view
      v-if="pageHeight"
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
    >
      <view
        style="
          height: 211rpx;
          font-size: 40rpx;
          font-weight: bold;
          color: #e60012;
        "
        class="flex-center-center"
      >
        <v-price :price="order?.shouldPayCost" size="40" />
      </view>
      <view
        style="
          font-size: 24rpx;
          font-weight: bold;
          color: #999999;
          margin-left: 21rpx;
          margin-bottom: 24rpx;
        "
      >
        支付方式
      </view>
      <view class="v-block">
        <view class="payment-info flex-center-between">
          <view class="flex-center">
            <v-icon
              size="54rpx"
              margin-right="20rpx"
              src="/static/icons/order/wechart.png"
            />
            <view> 微信支付 </view>
          </view>
          <v-checkbox checked />
        </view>
      </view>
      <view class="count-pad-container">
        <v-button
          type="primary"
          height="100rpx"
          width="702rpx"
          font-size="30rpx"
          @click="submit"
        >
          去支付
        </v-button>
        <view class="page-padding-bottom" />
      </view>
    </scroll-view>>
  </view>
</template>

<script setup lang="ts">
import usePayment from "@/composables/use-payment"

const { pageHeight } = usePageLayout()
const { payOrder } = usePayment()
let orderNo: string
const submit = async () => {
  uni.showLoading({ title: "请稍后", mask: true })
  const result = await payOrder(orderNo)
  if (result) {
    uni.redirectTo({
      url: `/package-order/order/pay-result?orderno=${orderNo}`
    })
  } else {
    uni.reLaunch({
      url: `/package-order/order/order-details?orderno=${orderNo}`
    })
  }
  uni.hideLoading()
}
onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.orderno) return
  orderNo = query.orderno
})
/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  refreshOrder()
}
/** 刷新订单 */
const refreshOrder = async () => {
  if (loading.value) return
  loading.value = true
  const result = await getOrderDetails()
  loading.value = false
  refreshing.value = false
  return result
}
const order = ref<{
  payStatus: string
  handleStatus: string
  shouldPayCost: string
  discountAmount: string
}>()
onLoad(() => {
  getOrderDetails()
})
const getOrderDetails = async () => {
  const response = await $api.orderDetail({ orderNo })
  switch (response.__error) {
    case undefined: {
      const data = response.data
      order.value = {
        discountAmount: data.discountAmount,
        payStatus: data.payStatus,
        handleStatus: data.handleStatus,
        shouldPayCost: data.shouldPayCost
      }
    }
  }
  await nextTick()
  refreshing.value = false
}
</script>

<style lang="scss" scoped>
.count-pad-container {
  position: fixed;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  z-index: 10;
}
</style>
