import type { Ref } from "vue"

export default (): {
  safeNavigateBack: () => void,
  /** 页面高度 */
  pageHeight: Ref<number>
  rpxRatio: Ref<number>
  headerHeight: Ref<number>
  footerHeight: Ref<number>
  /** 刷新页面高度 */
  refreshPageHeight: () => Promise<void>
} => {
  const safeNavigateBack = () => {
    const pageLength = getCurrentPages().length
    if (pageLength <= 1) {
      uni.switchTab({ url: "/pages/index/index-page" })
    } else {
      uni.navigateBack()
    }
  }
  const pageStore = $store.page()
  /** 页面高度 */
  const pageHeight = ref(0)
  const rpxRatio = ref(0)
  const headerHeight = ref(0)
  const footerHeight = ref(0)
  /** 刷新页面高度 */
  const refreshPageHeight = async () => {
    await new Promise((resolve) => nextTick(() => resolve(true)))
    await pageStore.hideTabBar()

    const system = uni.getWindowInfo()
    let screenHeight
    // #ifdef MP-WEIXIN
    screenHeight = system.screenHeight
    // #endif
    // #ifdef MP-ALIPAY
    screenHeight = system.windowHeight
    // #endif
    // #ifdef H5
    screenHeight = system.screenHeight
    // #endif
    const query = uni.createSelectorQuery()
    headerHeight.value = await new Promise<number>((resolve) => {
      query
        .select(".page-header")
        .boundingClientRect((res) => {
          const result = res as UniApp.NodeInfo
          resolve(result?.height ?? 0)
        })
        .exec()
    })
    footerHeight.value = await new Promise<number>((resolve) => {
      query
        .select(".page-footer")
        .boundingClientRect((res) => {
          const result = res as UniApp.NodeInfo
          resolve(result?.height ?? 0)
        })
        .exec()
    })
    pageHeight.value = screenHeight - headerHeight.value - footerHeight.value
    rpxRatio.value = 750 / system.windowWidth
  }

  // 每次切换页面都重新计算高度
  let loaded = false
  // 初次加载完页面刷新
  onReady(() => {
    refreshPageHeight()
    loaded = true
  })
  onShow(() => {
    if (loaded) refreshPageHeight()
  })

  return {
    safeNavigateBack,
    pageHeight,
    rpxRatio,
    headerHeight,
    footerHeight,
    refreshPageHeight
  }
}
