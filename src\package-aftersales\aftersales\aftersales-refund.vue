<template>
  <view>
    <view class="page-header">
      <v-navbar title="退货退款" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <v-loading-block v-if="!order" />
      <template v-else>
        <template v-for="product in order.productList" :key="product.id">
          <view v-if="product.selected === true" class="v-block">
            <view class="product-item">
              <v-product-item :product="product">
                <template #name>
                  {{ product.name }}
                </template>
                <template #spec>
                  {{ product.productInfoSpec }}
                </template>
                <template #default>
                  <view class="text-light text-24">
                    {{ `装箱规格: ${product.displayAmount}pcs/箱` }}
                  </view>
                  <view class="text-sub text-24">
                    {{ `订购数量: ${product.quantity}箱` }}
                  </view>
                </template>
              </v-product-item>
              <view class="divider" style="margin-top: 14rpx" />
              <view class="v-form-item flex-center-between">
                <view class="text-bold label required">
                  申请数量(箱)
                </view>
                <v-number-input
                  v-model="product.number"
                  :min="1"
                  :max="product.quantity"
                />
              </view>
            </view>
          </view>
        </template>
        <view class="v-block">
          <view class="v-form-item flex-center-between">
            <view class="text-bold label">
              总申请数量
            </view>
            <view class="text-32 text-bold red">
              {{ count }}箱
            </view>
          </view>
          <view class="divider" />
          <view class="v-form-item flex-center-between">
            <view class="text-bold label required">
              退货原因
            </view>
            <view class="flex-1">
              <v-select
                v-model="isShowReasonPicker"
                :value="reason"
                placeholder="请选择"
                text-align="right"
              />
            </view>
          </view>
        </view>
        <view class="v-block">
          <view class="v-form-item">
            <view class="text-bold label margin-bottom">
              退款说明
            </view>
            <v-textarea
              v-model="remark"
              maxlength="200"
              custom-style="height: 100rpx; width: auto"
              :cursor-spacing="75"
              placeholder="请填写退款说明"
            />
          </view>
          <view class="divider" />
          <view class="v-form-item">
            <view class="text-bold label required margin-bottom">
              上传凭证
            </view>
            <v-uploader @change="changeImageList" />
          </view>
        </view>

        <view class="v-block">
          <view class="v-form-item flex-center-between">
            <view class="text-bold label">
              预计退款金额
            </view>
            <v-price :price="countPrice" size="32rpx" />
          </view>
          <view class="price-notice">
            以最终到账金额为准
          </view>
        </view>
      </template>
      <view class="submit-button-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        type="primary"
        height="94rpx"
        font-size="32rpx"
        @click="submit"
      >
        提交申请
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-picker
      ref="pickerRef"
      v-model="isShowReasonPicker"
      :options="refundReasonList"
      @confirm="changeReason"
    />
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import type { UploaderItem } from "@/components/v-uploader/modules/types"
import { num2str, str2num } from "@/utils/type-transfer"

const { pageHeight } = usePageLayout()

const pageStore = $store.page()
const order = computed(() => pageStore.afterSalesOrder)
const refundReasonList = computed(() => pageStore.reasonList)

const isShowReasonPicker = ref(false)
const reason = ref("")
const remark = ref("")
const imageList = ref<number[]>([])
const count = computed(() => {
  if (!order.value) return 0
  const list = order.value.productList.filter((item) => item.selected)
  let sum = 0
  for (let i = 0; i < list.length; i += 1) {
    sum += list[i].number
  }
  return sum
})

const countPrice = computed(() => {
  if (!order.value) return 0
  if (!order.value.actualPayCost) return 0
  if (!order.value.totalPrice) return 0
  const ratio = order.value.actualPayCost / order.value.totalPrice
  const list = order.value.productList.filter((item) => item.selected)
  let sum = 0
  for (let i = 0; i < list.length; i += 1) {
    sum += list[i].number * list[i].unitPrice * list[i].unitAmount * ratio
  }
  return sum
})

const changeReason = (list: [{ label: string }]) => {
  reason.value = list[0].label
}

const changeImageList = (list: UploaderItem[]) => {
  imageList.value = list
    .filter((item) => item.status === "success")
    .map((item) => str2num(item.id))
}

const validate = () => {
  switch (true) {
    case !reason.value:
      uni.showToast({ title: "请选择退货原因", icon: "none" })
      return false
    case imageList.value.length === 0:
      uni.showToast({ title: "请上传凭证后提交", icon: "none" })
      return false
    default:
      return true
  }
}

const submit = async () => {
  if (!order.value) return
  if (!validate()) return
  uni.showLoading({ title: "提交中", mask: true })
  const filterSelectProduct = order.value.productList.filter(
    (item) => item.selected === true
  )
  const response = await $api.saveOrderRefund({
    afterSalesType: 0,
    backExplain: remark.value,
    backImgs: imageList.value,
    backReason: reason.value,
    orderNo: order.value.orderNo,
    shoppingCartList: filterSelectProduct.map((item) => {
      return {
        productInfoId: item.id,
        quantity: num2str(item.number),
        quantityRetail: "0"
      }
    })
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      redirectToResult(response.data.orderBackId)
  }
}

const redirectToResult = (id: string) => {
  uni.redirectTo({
    url: `/package-aftersales/aftersales/aftersales-result?id=${id}`
  })
}
</script>

<style lang="scss" scoped>
.label.margin-bottom {
  margin-bottom: 14rpx;
}
.submit-button-blank {
  height: 94rpx;
}
.submit-button {
  position: fixed;
  padding: 0 24rpx;
  left: 0;
  right: 0;
  bottom: 0;
}
.red {
  color: #e60012;
}
.price-notice {
  color: #999999;
  font-size: 22rpx;
}
</style>
