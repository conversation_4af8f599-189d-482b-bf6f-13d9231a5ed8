<template>
  <view>
    <view class="page-header">
      <v-navbar title="选择公司" back />
    </view>
    <scroll-view
      v-if="pageHeight"
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
    >
      <view class="page-padding-top" />
      <template v-if="!refreshing">
        <v-loading-block v-if="loading" />
        <v-empty
          v-else-if="corporateList.length === 0"
          src="/static/image/empty-search.png"
        >
          <view> 暂无公司 </view>
          <view class="create-container flex-center-center">
            <v-button
              type="primary"
              height="60rpx"
              font-size="28rpx"
              @click="navigateToCorporateCreate"
            >
              + 新增公司
            </v-button>
          </view>
        </v-empty>
        <template v-else>
          <view
            v-for="corporate in corporateList"
            :key="corporate.id"
            class="corporate-item"
          >
            <v-swipe>
              <template #default>
                <view class="v-block" @click="() => selectCorporate(corporate)">
                  <view class="corporate-name flex-center-between">
                    <view class="text-32 text-bold">
                      {{ corporate.corporateName }}
                    </view>
                    <v-checkbox
                      :checked="selectedCorporateId === corporate.id"
                    />
                  </view>
                </view>
              </template>
              <template #button>
                <view class="flex" style="height: 100%">
                  <view
                    class="edit-button flex-center-center"
                    @click="() => navigateToEdit(corporate)"
                  >
                    <v-icon
                      size="36rpx"
                      src="/static/icons/components/edit.png"
                    />
                  </view>
                  <view
                    class="delete-button flex-center-center"
                    @click="() => delCorporate(corporate)"
                  >
                    <v-icon
                      size="36rpx"
                      src="/static/icons/components/delete.png"
                    />
                  </view>
                </view>
              </template>
            </v-swipe>
          </view>
          <view class="v-block create-container flex-center-center">
            <v-button
              type="primary"
              height="60rpx"
              font-size="28rpx"
              :disabled="corporateList.length >= 10"
              @click="navigateToCorporateCreate"
            >
              {{
                corporateList.length >= 10 ? "公司数量已达上限" : "+ 新增公司"
              }}
            </v-button>
          </view>
        </template>
      </template>
      <view class="submit-button-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        v-if="!loading"
        type="primary"
        height="94rpx"
        font-size="32rpx"
        :disabled="!selectedCorporate"
        @click="submitSelect"
      >
        确定
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { safeNavigateBack, pageHeight } = usePageLayout()

const pageStore = $store.page()

const corporateList = computed(() => pageStore.corporateList)

const selectedCorporateId = ref("")
const selectedCorporate = computed(() =>
  corporateList.value.find((item) => item.id === selectedCorporateId.value)
)

onLoad(() => {
  uni.$on("select-corporate", (id: string) => {
    selectedCorporateId.value = id
  })
})
onUnload(() => {
  uni.$off("select-corporate")
})

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  await getCorporate()
  await nextTick()
  refreshing.value = false
}

const getCorporate = async () => {
  if (loading.value) return
  loading.value = true
  await pageStore.getCorporateList()
  loading.value = false
}
onShow(() => getCorporate())
onLoad(() => {
  selectedCorporateId.value = corporateList.value.find(
    (item) => item.corporateName === pageStore.selectedCorporate
  )?.id ?? ""
})

const submitSelect = () => {
  if (!selectedCorporate.value) return
  pageStore.setCorporate(selectedCorporate.value.corporateName)
  safeNavigateBack()
}

const selectCorporate = (corporate: Corporate) => {
  selectedCorporateId.value = corporate.id
}

const delCorporate = async (corporate: Corporate) => {
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "提示",
      content: "是否确认删除该公司?",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return
  uni.showLoading({ title: "删除中", mask: true })
  const response = await $api.delCorporate({ corporateId: corporate.id })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      uni.$emit("unselectCorporate", corporate.corporateName)
      uni.showToast({ title: "删除成功", icon: "none" })
      // getCorporate()
      pageStore.deleteCorporate(corporate.id)
  }
}

const navigateToCorporateCreate = () => {
  uni.navigateTo({ url: "/package-user/user/corporate-create-and-edit" })
}
const navigateToEdit = (corporate: Corporate) => {
  uni.navigateTo({
    url: `/package-user/user/corporate-create-and-edit?id=${corporate.id}`
  })
}
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0;
}
.corporate-item {
  margin: 0 24rpx 24rpx;
  .corporate-name {
    padding: 10rpx 0;
  }
}
.edit-button {
  background-color: #005bac;
  height: 100%;
  width: 90rpx;
  margin-left: -20rpx;
  padding-left: 20rpx;
}
.delete-button {
  background-color: #f06671;
  border-radius: 0 18rpx 18rpx 0;
  height: 100%;
  width: 90rpx;
}
.create-container {
  margin: 0 24rpx 24rpx;
  padding: 30rpx 0;
}
.submit-button-blank {
  height: 94rpx;
}
.submit-button {
  position: fixed;
  padding: 0 24rpx;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
