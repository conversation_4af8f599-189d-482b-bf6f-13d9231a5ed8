<template>
  <view class="discount-popover">
    <view class="discount-popover__mask-container">
      <v-transition type="fade" :show="isShow">
        <view class="discount-popover__mask" @click="hidePopover" />
      </v-transition>
    </view>

    <view
      class="discount-popover__container"
      :class="{ 'padding-bottom': paddingBottom }"
      :style="{ bottom: `calc(${footerHeight}px + 148rpx)` }"
    >
      <v-transition
        type="collapse"
        :show="isShow"
        timing="ease-out"
        custom-class="flex-start--end"
      >
        <view>
          <view class="v-block box-shadow popover-container">
            <view class="popover-header flex-center-between">
              <view class="title">
                优惠明细
              </view>
              <view @click="hidePopover">
                <v-icon size="24rpx" src="/static/icons/cart/close-icon.png" />
              </view>
            </view>
            <view class="divider" />
            <view class="flex-col flex--evenly popover-content">
              <view class="sum-price flex-baseline-between">
                <view class="label">
                  商品总价
                </view>
                <view class="price">
                  <v-price :price="totalPrice" size="28rpx" />
                </view>
              </view>
              <view class="subinfo flex-baseline-between">
                <view class="copon-label">
                  优惠券
                </view>
                <view>
                  <template v-if="coupon?.couponType === 0">
                    满减
                  </template>
                  <template v-else-if="coupon?.couponType === 1">
                    折扣
                  </template>
                </view>
              </view>
              <view class="price flex-baseline-end">
                <v-price :price="-discountPrice" size="24rpx" />
              </view>
            </view>
            <view class="divider" />
            <view class="sum-discount flex-baseline-between">
              <view class="label">
                共优惠
              </view>
              <view>
                <v-price :price="-discountPrice" size="28rpx" />
              </view>
            </view>
          </view>
        </view>
      </v-transition>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  totalPrice: number
  paddingBottom: boolean
  coupon?: CouponItem
}>()

const totalPrice = toRef(props, "totalPrice")
const coupon = toRef(props, "coupon")

const isShow = ref(false)

const { footerHeight } = usePageLayout()

const discountPrice = computed(() => {
  if (!coupon.value) return 0
  const value = parseFloat(coupon.value.couponValue)
  return value
})

const showPopover = () => {
  isShow.value = true
}
const hidePopover = () => {
  isShow.value = false
}

onHide(() => {
  hidePopover()
})

defineExpose({ showPopover, hidePopover })
</script>

<style lang="scss" scoped>
.discount-popover__mask-container {
  position: fixed;
  z-index: 999;
}
.discount-popover__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.05);
}
.discount-popover__container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: fit-content;
  z-index: 1000;
  &.padding-bottom {
    margin-bottom: calc(constant(safe-area-inset-bottom));
    margin-bottom: calc(env(safe-area-inset-bottom));
  }
}
.popover-container {
  height: fit-content;
  width: calc(100vw - 24rpx);
  margin: 0 12rpx;
  box-sizing: border-box;
  .popover-header {
    line-height: 80rpx;
    .title {
      color: #999999;
      font-size: 28rpx;
    }
  }
  .popover-content {
    height: 190rpx;
  }
  .sum-price {
    .label {
      color: #231815;
      font-size: 28rpx;
    }
  }
  .subinfo {
    position: relative;
    color: #666666;
    font-size: 24rpx;
    margin-left: 14rpx;
    .copon-label {
      &::before {
        position: absolute;
        content: " ";
        width: 8rpx;
        height: 8rpx;
        border-radius: 100%;
        background-color: #666666;
        left: -14rpx;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .sum-discount {
    line-height: 80rpx;
    .label {
      color: #231815;
      font-size: 28rpx;
    }
  }
}
.counter-blank {
  height: 148rpx;
}
</style>
