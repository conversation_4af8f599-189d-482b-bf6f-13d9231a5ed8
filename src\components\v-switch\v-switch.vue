<template>
  <view
    class="v-switch flex-center"
    :class="{ active: modelValue, disabled: disabled }"
    :style="{
      paddingLeft: padding,
      paddingRight: padding,
      height: height,
      width: width
    }"
    @click="toggle"
  >
    <view
      class="content"
      :style="{
        height: size,
        width: size,
        transform: transform
      }"
    />
  </view>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    modelValue?: boolean
    width?: string
    height?: string
    size?: string
    padding?: string
    disabled?: boolean
  }>(),
  {
    modelValue: false,
    width: "84rpx",
    height: "50rpx",
    size: "42rpx",
    padding: "4rpx",
    disabled: false
  }
)

interface Emits {
  (event: "update:model-value", val: boolean): void
  (event: "change", val: boolean): void
  (event: "active"): void
  (event: "deactive"): void
}
const emits = defineEmits<Emits>()

const modelValue = toRef(props, "modelValue")
const width = toRef(props, "width")
const size = toRef(props, "size")
const disabled = toRef(props, "disabled")

const transform = computed(() => {
  if (modelValue.value) {
    return `translateX(calc(${width.value} - ${size.value}))`
  } else {
    return "translateX(0)"
  }
})
const toggle = () => {
  if (disabled.value) return
  const val = !modelValue.value
  emits("update:model-value", val)
  emits("change", val)
  if (val) {
    emits("active")
  } else {
    emits("deactive")
  }
}
</script>

<style lang="scss" scoped>
.v-switch {
  background-color: #999999;
  border-radius: 100vh;
  transition: background-color 0.1s ease-in-out;
  .content {
    background-color: white;
    border-radius: 50%;
    box-shadow: 0, 0, 5px rgba(0, 0, 0, 0.2);
    transition: transform 0.1s ease-in-out;
  }
  &.active {
    background-color: #005bac;
  }
  &.disabled {
    opacity: 0.4;
  }
}
</style>
