<template>
  <v-icon :size="size" :src="src" @click="$emit('click')" />
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    checked?: boolean
    indeterminate?: boolean
    size?: string
  }>(),
  {
    checked: false,
    indeterminate: false,
    size: "32rpx"
  }
)

interface Emits {
  (event: "click"): void
}
defineEmits<Emits>()

const indeterminate = toRef(props, "indeterminate")
const checked = toRef(props, "checked")
const src = computed(() => {
  if (indeterminate.value) {
    return "/static/icons/components/checkbox-indeterminate.png"
  }
  if (checked.value) return "/static/icons/components/checkbox-checked.png"
  return "/static/icons/components/checkbox.png"
})
</script>
