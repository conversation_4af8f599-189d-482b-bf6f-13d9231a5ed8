/// <reference types="./response" />
declare type AnyResult = $res[keyof $res] | undefined

/** 请求类型 */
declare type RequestMethod =
  | "GET"
  | "POST"
  | "PUT"
  | "DELETE"
  | "OPTIONS"
  | "HEAD"
  | "TRACE"
  | "CONNECT"

/** 请求参数 */
declare interface RequestOptions {
  /** 接口信息 */
  apiInfo: string
  /** 请求类型 */
  method?: RequestMethod
  /** 域名 拼接在访问地址前 */
  baseUrl?: string
  /** 访问地址 */
  url: string
  /** 请求参数 会直接编码拼接在url后面 */
  params?: Record<string, string | number>
  /** 请求数据 类型由uniapp规定 上传有 uni.uploadFile 接口不做补充 */
  data?: Record<string, unknown>
  /** 请求头 特别地 uniapp不能设置 Referer */
  headers?: Record<string, string>
  /** 超时时间 单位ms */
  timeout?: number
  /** 数据类型 特别地 uniapp会对json格式 返回的数据做一次 JSON.parse */
  dataType?: string
  /** 请求内容 */
  contentType?: string
  /** 响应数据类型 */
  responseType?: "arraybuffer" | "text"
  /** 是否不需要token */
  noNeedToken?: boolean
  /** 是否可多次请求 */
  multiple?: boolean
  /** 是否统一显示请求错误 默认显示 */
  notifyRequestError?: boolean
  /** 是否统一显示响应错误 默认显示 */
  notifyResponseError?: boolean
  /**
   * 是否禁止重新授权再次发起请求
   * 特殊处理401状态码，一般接口收到401之后会重新登录刷新token，再重新进行请求
   * 重新登录后该属性也会自动置为true
   */
  authed?: boolean
  /** 是否统一显示接口错误 默认显示 */
  notifyApiError?: boolean
  /** 忽略掉不捕获的编码 */
  ignoreApiCodes?: (number | string)[]
  /** 用于重试的参数，如果存在该项则无视任何处理，直接请求 */
  requestOptions?: RequestOptions
}

declare interface ResponseResult {
  /** 请求参数 */
  __requestOptions: RequestOptions
  /** 请求时间戳 */
  __requestTime?: number
  /** 响应或请求终止时间戳 */
  __responseTime?: number
}

/** 请求终止返回数据 */
declare interface RequestAbortResponseResult extends ResponseResult {
  /** 错误来源 */
  __error: "请求终止"
}

/** 请求错误返回数据  */
declare interface RequestErrorResponseResult extends ResponseResult {
  /** 错误来源 */
  __error: "请求错误"
  /** 错误信息 */
  __errMsg: string
}

/** 响应错误返回数据 */
declare interface ResponseErrorResponseResult extends ResponseResult {
  /** 错误来源 */
  __error: "响应错误"
  /** 错误信息 */
  __errMsg: string
  /** 请求状态码 */
  __status?: number
}

/** 接口错误返回数据 */
declare interface ApiErrorResponseResult extends ResponseResult {
  /** 错误来源 */
  __error: "接口错误"
  /** 错误信息 */
  __errMsg: string
  /** 请求状态码 */
  __status: number
  /** 接口状态码 */
  __code?: string
}

/** 正常返回数据 */
declare type RegularResponseResult<T> = T & ResponseResult & { __error: undefined }

/** 请求响应返回数据 */
declare type Result<T> =
  | RegularResponseResult<T>
  | RequestAbortResponseResult
  | RequestErrorResponseResult
  | ResponseErrorResponseResult
  | ApiErrorResponseResult
