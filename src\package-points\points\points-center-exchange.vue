<template>
  <view>
    <view class="page-header background-white">
      <v-navbar title="积分兑换" back />
    </view>
    <scroll-view scroll-y :style="{ height: `${pageHeight}px` }">
      <view class="page-padding-top" />
      <v-loading-block v-if="!details" />
      <template v-else>
        <view class="v-block flex">
          <view class="image-container">
            <v-image
              height="160rpx"
              width="160rpx"
              mode="aspectFit"
              :src="details.couponViceIMG"
            />
          </view>
          <view class="info-list flex flex-col">
            <view class="price-count flex-center-between">
              <view class="price">
                <text style="font-weight: bold; font-weight: 32rpx">
                  {{ details.integrate }}
                </text>
                <text>积分</text>
              </view>
              <view class="count">
                {{ `月兑 ${details.receiveAmount}` }}
              </view>
            </view>
            <view class="item-name">
              {{ details.couponName }}
            </view>
            <view class="item-time">
              {{ `优惠券有效期：自领取后${details.couponExp.expDays}天有效` }}
            </view>
          </view>
        </view>
        <view class="v-block">
          <view class="title">
            使用规则
          </view>
          <view class="divider" />
          <view class="text-content">
            {{ details.couponRule }}
          </view>
        </view>
      </template>
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="page-footer">
      <view class="submit-button">
        <v-button
          type="primary"
          round
          block
          @click="submit"
        >
          立即兑换
        </v-button>
      </view>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { pageHeight } = usePageLayout()

const details = ref<ExchangeItemDetails>()

let couponNo = ""
let rewardNo = ""
const getRewardsPointsDetail = async () => {
  const response = await $api.getRewardsPointsDetail({
    couponNo,
    rewardsPointsNo: rewardNo
  })
  switch (response.__error) {
    case undefined:
      details.value = response.data
      break
  }
}

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query) return
  couponNo = query.coupon
  rewardNo = query.reward
  getRewardsPointsDetail()
})

const disabledButton = ref(false)
const submit = async () => {
  if (disabledButton.value) return
  const result = await uni.showModal({
    content: "确认兑换此优惠券？"
  })
  if (!result.confirm) return
  disabledButton.value = true
  const response = await $api.rewardsPointsReceiveCoupon({
    couponNo,
    rewardsPointsNo: rewardNo
  })
  switch (response.__error) {
    case undefined:
      if (response.code === "14122") {
        disabledButton.value = false
        $toast.show("可用积分不足，无法兑换")
      } else {
        redirectToResult(response.data)
      }
      break
    default:
      disabledButton.value = false
  }
}

const redirectToResult = (id: string) => {
  uni.redirectTo({
    url: `/package-points/points/points-exchange-record-details?id=${id}&result=true`
  })
}
</script>

<style lang="scss" scoped>
.image-container {
  height: 160rpx;
  width: 160rpx;
}
.info-list {
  flex: 1;
  margin-left: 20rpx;
  padding: 12rpx 0;
  overflow: hidden;
  .price-count {
    .price {
      font-size: 32rpx;
      font-weight: bold;
      color: #fe5852;
    }
    .count {
      font-size: 24rpx;
      color: #999;
    }
  }
  .item-name {
    flex: 1;
    color: #666;
  }
  .item-time {
    font-size: 24rpx;
    color: #999;
  }
}
.v-block {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  .title {
    font-weight: bold;
    color: #231815;
  }
  .divider {
    margin: 10rpx 0;
  }
  .text-content {
    color: #666;
    white-space: pre-wrap;
  }
}
.submit-button {
  padding: 0 24rpx;
}
</style>
