<template>
  <view class="v-loadmore flex-center-center" @click="$emit('click')">
    <view class="divider flex-1" />
    <view class="text-24 text-light">
      <template v-if="status === 'loadmore'">
        <text>{{ loadmoreText }}</text>
      </template>
      <template v-else-if="status === 'loading'">
        <text>{{ loadingText }}</text>
      </template>
      <template v-else-if="status === 'nomore'">
        <text>{{ nomoreText }}</text>
      </template>
    </view>
    <view class="divider flex-1" />
  </view>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    status: string
    loadmoreText?: string
    loadingText?: string
    nomoreText?: string
  }>(),
  {
    status: "loadmore",
    loadmoreText: "点击加载更多",
    loadingText: "加载中",
    nomoreText: "已经到底啦"
  }
)

interface Emits {
  (event: "click"): void
}
defineEmits<Emits>()
</script>

<style lang="scss" scoped>
.v-loadmore {
  height: 60rpx;
  .divider {
    margin: 0 30rpx;
  }
}
</style>
