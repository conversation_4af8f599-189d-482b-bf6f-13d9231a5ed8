<template>
  <view>
    <view class="page-header background-white">
      <v-navbar title="积分兑换详情" back />
      <view v-if="result" class="result-block flex-center-center flex-col">
        <v-icon size="84rpx" src="/static/icons/points/success.png" />
        <view class="text">
          兑换成功
        </view>
      </view>
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
    >
      <v-loading-block v-if="!details" />
      <template v-else>
        <view class="page-padding-top" />
        <view class="v-block">
          <view class="flex-center">
            <view class="image-container">
              <v-image
                height="160rpx"
                width="160rpx"
                mode="aspectFit"
                :src="details.couponViceIMG"
              />
            </view>
            <view class="info-list flex--evenly flex-col">
              <view class="item-name">
                {{ details.couponName }}
              </view>
              <view class="item-count">
                × 1
              </view>
            </view>
            <view v-if="details.isUsed === 0" class="tag unused">
              未使用
            </view>
            <view v-else-if="details.isUsed === 1" class="tag used">
              已使用
            </view>
          </view>
          <view class="divider" />
          <view class="form-item flex-center-between">
            <view class="label">
              兑换积分
            </view>
            <view class="value">
              {{ `${details.integrate}积分` }}
            </view>
          </view>
          <view class="divider" />
          <view class="form-item flex-center-between">
            <view class="label">
              兑换时间
            </view>
            <view class="value">
              {{ formateUtc(details.receiveTime) }}
            </view>
          </view>
          <view class="divider" />
          <view class="form-item flex-center-between">
            <view class="label">
              兑换流水号
            </view>
            <view class="value">
              {{ details.rewardsPointsReceiveRecordNo }}
            </view>
          </view>
        </view>
        <view v-if="details.couponRule" class="v-block">
          <view class="title">
            使用规则
          </view>
          <view class="divider" />
          <view class="text-content">
            {{ details.couponRule }}
          </view>
        </view>
      </template>
      <view class="page-padding-bottom" />
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"

const { pageHeight } = usePageLayout()

const result = ref(false)
let exchangeId = ""
const details = ref<ExchangeRecordDetails>()

const getRewardsPointsReceiveRecordDetail = async () => {
  const response = await $api.getRewardsPointsReceiveRecordDetail({
    rewardsPointsReceiveRecordId: exchangeId
  })
  switch (response.__error) {
    case undefined:
      details.value = response.data
      break
  }
  await nextTick()
  refreshing.value = false
}
onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query) return
  exchangeId = query.id
  result.value = query.result
  getRewardsPointsReceiveRecordDetail()
})

const refreshing = ref(false)
const refresh = () => {
  refreshing.value = true
  getRewardsPointsReceiveRecordDetail()
}
</script>

<style lang="scss" scoped>
.result-block {
  background-color: white;
  height: 262rpx;
}
.v-block {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  .title {
    font-weight: bold;
    color: #231815;
  }
  .divider {
    margin: 10rpx 0;
  }
  .form-item {
    padding: 12rpx 0;
    .label {
      color: #999;
    }
  }
  .text-content {
    color: #666;
    white-space: pre-wrap;
  }
  .image-container {
    height: 160rpx;
    width: 160rpx;
  }
  .info-list {
    flex: 1;
    align-self: stretch;
    margin-left: 20rpx;
    padding: 12rpx 0;
    overflow: hidden;
    .item-name {
      font-weight: bold;
      color: #231815;
    }
    .item-count {
      color: #999;
    }
  }
  .tag {
    margin-left: 10rpx;
    height: 50rpx;
    width: 100rpx;
    font-size: 20rpx;
    line-height: 50rpx;
    text-align: center;
    border-radius: 26rpx;
    &.unused {
      background-color: rgba(4, 172, 0, 0.1);
      color: #04ac00;
    }
    &.used {
      background-color: rgb(153, 153, 153, 0.1);
      color: #999999;
    }
  }
}
.submit-button {
  padding: 20rpx 24rpx 0;
}
</style>
