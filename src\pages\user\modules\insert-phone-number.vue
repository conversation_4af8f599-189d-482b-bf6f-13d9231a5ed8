<template>
  <view class="step-container">
    <view class="title">
      绑定手机号
    </view>
    <view class="input-container flex-center-between">
      <v-input
        v-model="phone"
        :replace="/[^\d]/g"
        type="number"
        custom-style="font-size: 36rpx"
        maxlength="11"
        placeholder="请输入手机号"
      />
    </view>
    <view class="divider" />
    <view class="input-container flex-center-between">
      <v-input
        v-model="code"
        :replace="/[^\d]/g"
        type="number"
        maxlength="6"
        custom-style="font-size: 36rpx"
        placeholder="请输入验证码"
      />
      <v-button
        :disabled="counter > 0"
        height="58rpx"
        width="200rpx"
        padding="0"
        border
        font-size="24rpx"
        @click="sendCode"
      >
        {{ buttonText }}
      </v-button>
    </view>
    <view class="divider" />
    <v-button
      type="primary"
      height="94rpx"
      width="540rpx"
      font-size="32rpx"
      margin="80rpx auto 10rpx"
      shadow
      @click="submitHandle"
    >
      确认
    </v-button>
  </view>
</template>

<script setup lang="ts">
import { refreshToken } from "@/utils/auth/token"
import { str2num } from "@/utils/type-transfer"

const props = defineProps<{
  checkbox: boolean
}>()
const checkbox = toRef(props, "checkbox")

interface Emits {
  (event: "checked"): void
}
const emits = defineEmits<Emits>()

const { safeNavigateBack } = usePageLayout()

const phone = ref("")
const code = ref("")
const counter = ref(-1)

const validatePhone = () => {
  if (!/^\d{11}$/.test(phone.value)) {
    uni.showToast({ title: "请输入正确的手机号", icon: "none" })
    return false
  }
  return true
}
const validateCode = () => {
  if (!/^\d{6}$/.test(code.value)) {
    uni.showToast({ title: "验证码格式错误", icon: "none" })
    return false
  }
  return true
}

const buttonText = computed(() => {
  switch (true) {
    case sending.value:
      return "发送中"
    case counter.value >= 57:
      return "发送成功"
    case counter.value === -1:
      return "获取验证码"
    case counter.value === 0:
      return "重新获取"
    default:
      return `重新获取(${counter.value}s)`
  }
})

const submitHandle = async () => {
  if (!checkbox.value) {
    const result = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: "确认",
        content: "已阅读并同意用户协议和隐私政策",
        success: (res) => resolve(res.confirm)
      })
    })
    if (!result) return
    emits("checked")
    return
  }
  if (!validatePhone()) return
  if (!validateCode()) return
  uni.showLoading({ title: "提交中", mask: true })
  const openIdCode = await new Promise<string>((resolve) => {
    uni.login({
      success: (res) => resolve(res.code),
      fail: () => resolve("")
    })
  })
  if (!openIdCode) return
  const response = await $api.signUpOrRegisterByMobile({
    mobile: phone.value,
    validateCode: code.value,
    code: openIdCode
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      if (response.code === "23014") {
        uni.showModal({
          title: "您的账号异常",
          content: "请通过公众号联系客服处理",
          showCancel: false
        })
      } else {
        refreshToken(response.data.token, str2num(response.data.tokenExpires))
        safeNavigateBack()
        uni.$emit("loginFinish")
      }
  }
}

const sending = ref(false)
let timer: number | undefined
const sendCode = async () => {
  if (!validatePhone()) return
  sending.value = true
  const response = await $api.smsMobileValidateCode({ mobile: phone.value })
  sending.value = false
  switch (response.__error) {
    case undefined:
      beginCountDown()
      break
  }
}

const beginCountDown = () => {
  counter.value = 60
  timer = setInterval(() => {
    counter.value -= 1
    if (counter.value <= 0) {
      clearInterval(timer)
      timer = undefined
    }
  }, 1000)
}
</script>

<style lang="scss" scoped>
.step-container {
  width: 100vw;
  padding-bottom: 20px;
  .title {
    padding: 60rpx 44rpx;
    color: #231815;
    font-size: 64rpx;
  }
  .divider {
    margin: 0 40rpx;
  }
  .input-container {
    padding: 0 40rpx;
    height: 114rpx;
  }
}
</style>
