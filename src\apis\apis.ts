import request from "@/utils/http"

export const activityReceiveCoupon = (data: {
  /** 活动编号 */
  activityNo: string
  /** 优惠券编号 */
  couponNo: string
}) =>
  request.post<$res["activityReceiveCoupon"]>({
    apiInfo: "活动领券",
    url: "/wxapp/activity/activityReceiveCoupon",
    data
  })

export const couponCenterReceiveCoupon = (data: {
  /** 优惠券编号 */
  couponNo: string
  /** 领券中心id */
  receiveCouponCenterRelationId: long
}) =>
  request.post<$res["couponCenterReceiveCoupon"]>({
    apiInfo: "领券中心领券",
    url: "/wxapp/activity/couponCenterReceiveCoupon",
    data
  })

export const getActivityCenterDetail = (data: {
  /** 活动编号 */
  activityNo: string
}) =>
  request.post<$res["getActivityCenterDetail"]>({
    apiInfo: "获取活动中心详情",
    url: "/wxapp/activity/getActivityCenterDetail",
    data,
    noNeedToken: true
  })

export const getActivityCenterList = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["getActivityCenterList"]>({
    apiInfo: "获取活动中心列表",
    url: "/wxapp/activity/getActivityCenterList",
    data
  })

export const addSysOpinionFeedback = (data: {
  /** 反馈类型: 0->功能异常; 1->优化建议; */
  feedbackType?: number
  /** 图片Ids */
  imgIds?: number[]
  /** 问题或建议 */
  remark?: string
  /** 联系方式 */
  tel?: string
}) =>
  request.post<$res["addSysOpinionFeedback"]>({
    apiInfo: "新增建议反馈",
    url: "/wxapp/cms/addSysOpinionFeedback",
    data
  })

export const getAdvertising = (data: {
  /** 广告位子: 0->首页轮播广告；1->首页中部广告；2->首页弹窗公告 */
  advertisingSpace?: number
}) =>
  request.post<$res["getAdvertising"]>({
    apiInfo: "获取广告",
    url: "/wxapp/cms/getAdvertising",
    data,
    noNeedToken: true,
    multiple: true
  })

export const getSysContentManage = (data: {
  /** 协议类型: 0->第三方配送服务说明; 1->价格说明; 2->订购说明; 3->售后政策说明; 4->售后退款说明;5->隐私协议; 6->用户服务协议; 7->发票说明; 8->积分规则; 9->活动规则 */
  contentType?: number
}) =>
  request.post<$res["getSysContentManage"]>({
    apiInfo: "获取内容管理信息",
    url: "/wxapp/cms/getSysContentManage",
    data,
    noNeedToken: true
  })

export const getWXTemplateId = (data: { body?: Record<string, never> }) =>
  request.post<$res["getWXTemplateId"]>({
    apiInfo: "获取小程序订阅消息模版Id",
    url: "/wxapp/cms/getWXTemplateId",
    data
  })

export const getAllCouponListByUserId = (data: {
  body?: Record<string, never>
}) =>
  request.post<$res["getAllCouponListByUserId"]>({
    apiInfo: "获取我的优惠券列表(不分页)",
    url: "/wxapp/coupon/GetAllCouponListByUserId",
    data
  })

export const getCouponListByUserId = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  requestParams: {
    /** 地区id */
    areaId?: number
    /** 优惠券类型: 0->满减;1->打折 */
    couponType?: number
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["getCouponListByUserId"]>({
    apiInfo: "获取我的优惠券列表",
    url: "/wxapp/coupon/GetCouponListByUserId",
    data
  })

export const getHistoryCouponListByUserId = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  requestParams: {
    /** 查询类型: 0->已使用;1->已失效 */
    historyType?: number
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["getHistoryCouponListByUserId"]>({
    apiInfo: "获取我的历史优惠券列表",
    url: "/wxapp/coupon/getHistoryCouponListByUserId",
    data
  })

export const getReceiveCouponCenterList = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["getReceiveCouponCenterList"]>({
    apiInfo: "获取领券中心列表",
    url: "/wxapp/coupon/getReceiveCouponCenterList",
    data
  })

export const getToken = (params: { code?: string }) =>
  request.get<$res["getToken"]>({
    apiInfo: "Token测试",
    url: "/wxapp/form/getToken",
    params
  })

export const signUpOrRegisterByCode = (data: {
  /** 微信用户登陆code */
  code: string
  /** 微信code */
  wxCode: string
}) =>
  request.post<$res["signUpOrRegisterByCode"]>({
    apiInfo: "会员账号注册登录---wxCode",
    url: "/wxapp/form/signUpOrRegisterByCode",
    data,
    noNeedToken: true,
    ignoreApiCodes: ["23014"]
  })

export const signUpOrRegisterByMobile = (data: {
  /** 微信用户登陆code */
  code: string
  /** 手机号 */
  mobile: string
  /** 验证码 */
  validateCode: string
}) =>
  request.post<$res["signUpOrRegisterByMobile"]>({
    apiInfo: "会员账号注册登录---mobile",
    url: "/wxapp/form/signUpOrRegisterByMobile",
    data,
    noNeedToken: true,
    ignoreApiCodes: ["23014"]
  })

export const smsMobileValidateCode = (data: {
  /** 手机号 */
  mobile: string
}) =>
  request.post<$res["smsMobileValidateCode"]>({
    apiInfo: "手机号验证码",
    url: "/wxapp/form/smsMobileValidateCode",
    data,
    noNeedToken: true
  })

export const addressList = (data: { body?: Record<string, never> }) =>
  request.post<$res["addressList"]>({
    apiInfo: "个人中心地址列表",
    url: "/wxapp/member/addressList",
    data
  })

export const addressSaveAndUpdate = (data: {
  /** 收货人详细地址 */
  address: string
  /** 市id */
  cityCode: number
  /** 市名称 */
  cityName: string
  /** 区id */
  districtCode?: number
  /** 区名称 */
  districtName: string
  /** 主键,当设置为0或null是新增地址 */
  id?: long
  /** 是否默认地址 */
  isDefault?: number
  /** 收货人手机 */
  mobile: string
  /** 收货人名称 */
  name: string
  /** 省id */
  provinceCode?: number
  /** 省名称 */
  provinceName: string
}) =>
  request.post<$res["addressSaveAndUpdate"]>({
    apiInfo: "新增或修改地址",
    url: "/wxapp/member/addressSaveAndUpdate",
    data
  })

export const collectList = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  /** 我的收藏列表请求参数 */
  requestParams: {
    /** 商品分类Id */
    categoryId?: long
    enumOrderBy?: number
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["collectList"]>({
    apiInfo: "收藏列表",
    url: "/wxapp/member/collectList",
    data
  })

export const corporateList = (data: { body?: Record<string, never> }) =>
  request.post<$res["corporateList"]>({
    apiInfo: "公司列表",
    url: "/wxapp/member/corporateList",
    data
  })

export const corporateSaveAndUpdate = (data: {
  /** 公司名称 */
  corporateName: string
  /** 公司Id */
  id?: long
}) =>
  request.post<$res["corporateSaveAndUpdate"]>({
    apiInfo: "新增或修改公司列表",
    url: "/wxapp/member/corporateSaveAndUpdate",
    data
  })

export const delAddress = (data: {
  /** 地址id */
  addressId?: long
}) =>
  request.post<$res["delAddress"]>({
    apiInfo: "删除地址",
    url: "/wxapp/member/delAddress",
    data
  })

export const delCorporate = (data: {
  /** 公司Id */
  corporateId: long
}) =>
  request.post<$res["delCorporate"]>({
    apiInfo: "删除公司列表",
    url: "/wxapp/member/delCorporate",
    data
  })

export const deleteMemberCollect = (data: {
  /** 商品Id */
  productInfoId: long
}) =>
  request.post<$res["deleteMemberCollect"]>({
    apiInfo: "删除收藏产品",
    url: "/wxapp/member/deleteMemberCollect",
    data
  })

export const districtList = (data: { body?: Record<string, never> }) =>
  request.post<$res["districtList"]>({
    apiInfo: "地区列表",
    url: "/wxapp/member/districtList",
    data
  })

export const findMemberInfoWeChat = (data: { body?: Record<string, never> }) =>
  request.post<$res["findMemberInfoWeChat"]>({
    apiInfo: "获取会员信息-小程序",
    url: "/wxapp/member/findMemberInfoWeChat",
    data
  })

export const findMemberPointsDetailList = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  requestParams: {
    /** 积分状态，0->到账;1->消耗 */
    pointsStatus?: number
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["findMemberPointsDetailList"]>({
    apiInfo: "用户积分明细列表",
    url: "/wxapp/member/findMemberPointsDetailList",
    data
  })

export const isCollectProduct = (data: {
  /** 商品Id */
  productInfoId: long
}) =>
  request.post<$res["isCollectProduct"]>({
    apiInfo: "是收藏产品",
    url: "/wxapp/member/isCollectProduct",
    data
  })

export const saveMemberCollect = (data: {
  /** 商品Id */
  productInfoId: long
}) =>
  request.post<$res["saveMemberCollect"]>({
    apiInfo: "保存会员收藏",
    url: "/wxapp/member/saveMemberCollect",
    data
  })

export const setDefaultAddress = (data: {
  /** 地址id */
  addressId?: long
}) =>
  request.post<$res["setDefaultAddress"]>({
    apiInfo: "设置默认地址",
    url: "/wxapp/member/setDefaultAddress",
    data
  })

export const tempUploadFile = (data: {
  /** 上传的图片-fileList */
  fileList?: File[]
}) =>
  request.post<$res["tempUploadFile"]>({
    apiInfo: "上传图片",
    url: "/wxapp/member/tempUploadFile",
    contentType: "multipart/form-data",
    data
  })

export const updateMemberInfoWeChat = (data: {
  /** 会员头像 */
  headIcon?: number
  /** 会员名称 */
  nickName?: string
}) =>
  request.post<$res["updateMemberInfoWeChat"]>({
    apiInfo: "更新会员信息-小程序",
    url: "/wxapp/member/updateMemberInfoWeChat",
    data
  })

export const invoicingDelete = (data: {
  /** 开票信息ID */
  makeInvoiceId: long
}) =>
  request.post<$res["invoicingDelete"]>({
    apiInfo: "删除开票信息",
    url: "/wxapp/memberInvoice/invoicingDelete",
    data
  })

export const invoicingInsert = (data: {
  /** 开户银行 */
  invoiceBank?: string
  /** 银行账号 */
  invoiceBankAccount?: string
  /** 公司地址 */
  invoiceCompanyAddress?: string
  /** 公司电话 */
  invoiceCompanyTel?: string
  /** 税号 */
  invoiceDutyParagraph: string
  /** 电子邮箱 */
  invoiceEmail: string
  /** 发票抬头 */
  invoiceHeader: string
  /** 抬头类型，0-企业 */
  invoiceHeaderType: number
}) =>
  request.post<$res["invoicingInsert"]>({
    apiInfo: "新增开票信息",
    url: "/wxapp/memberInvoice/invoicingInsert",
    data
  })

export const invoicingList = (data: { body?: Record<string, never> }) =>
  request.post<$res["invoicingList"]>({
    apiInfo: "开票信息列表",
    url: "/wxapp/memberInvoice/invoicingList",
    data
  })

export const invoicingUpdate = (data: {
  /** 开户银行 */
  invoiceBank?: string
  /** 银行账号 */
  invoiceBankAccount?: string
  /** 公司地址 */
  invoiceCompanyAddress?: string
  /** 公司电话 */
  invoiceCompanyTel?: string
  /** 税号 */
  invoiceDutyParagraph: string
  /** 电子邮箱 */
  invoiceEmail: string
  /** 发票抬头 */
  invoiceHeader: string
  /** 抬头类型，0-企业 */
  invoiceHeaderType: number
  /** 开票信息id */
  makeInvoiceId: long
}) =>
  request.post<$res["invoicingUpdate"]>({
    apiInfo: "更新开票信息",
    url: "/wxapp/memberInvoice/invoicingUpdate",
    data
  })

export const afterSaleProductPriceCount = (data: {
  /** 订单号 */
  orderNo: string
  shoppingCartList?: {
    /** 商品信息id */
    productInfoId: long
    /** 整件数量-箱 */
    quantity: long
    /** 零售数量-个 */
    quantityRetail: long
  }[]
}) =>
  request.post<$res["afterSaleProductPriceCount"]>({
    apiInfo: "退款商品价格计算",
    url: "/wxapp/orderBack/afterSaleProductPriceCount",
    data
  })

export const cancelOrderRefund = (data: {
  /** 退款单id */
  orderBackId?: string
}) =>
  request.post<$res["cancelOrderRefund"]>({
    apiInfo: "取消退货退款",
    url: "/wxapp/orderBack/cancelOrderRefund",
    data
  })

export const findOrderRefund = (data: {
  /** 退款单id */
  orderBackId?: string
}) =>
  request.post<$res["findOrderRefund"]>({
    apiInfo: "根据退款id查询退款单详情-小程序",
    url: "/wxapp/orderBack/findOrderRefund",
    data
  })

export const ncReceiveOrderAfterSale = (data: {
  /** 售后编号 */
  orderAfterSaleNo?: string
}) =>
  request.post<$res["ncReceiveOrderAfterSale"]>({
    apiInfo: "同步售后单到NC-况自用测试-其他人勿用！",
    url: "/wxapp/orderBack/ncReceiveOrderAfterSale",
    data
  })

export const orderRefundList = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  requestParams: {
    /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->同意退款，6->退款待确认，8->用户主动取消，9->已完成 */
    backStatuses?: {
      /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->同意退款，6->退款待确认，8->用户主动取消，9->已完成 */
      afterSaleStatus?: number
    }[]
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["orderRefundList"]>({
    apiInfo: "退款单列表",
    url: "/wxapp/orderBack/orderRefundList",
    data,
    multiple: true
  })

export const saveOrderRefund = (data: {
  /** 售后类型：0->退货退款 */
  afterSalesType: number
  /** 退款说明 */
  backExplain?: string
  /** 退款图片 */
  backImgs: number[]
  /** 退货原因 */
  backReason: string
  /** 订单号 */
  orderNo: string
  shoppingCartList?: {
    /** 商品信息id */
    productInfoId: long
    /** 整件数量-箱 */
    quantity: long
    /** 零售数量-个 */
    quantityRetail: long
  }[]
}) =>
  request.post<$res["saveOrderRefund"]>({
    apiInfo: "提交退款单",
    url: "/wxapp/orderBack/saveOrderRefund",
    data
  })

export const updateOrderBackDelivery = (data: {
  /** 物流名称 */
  deliveryName: string
  /** 物流单号 */
  deliveryNo: string
  /** 退款单id */
  orderBackId?: string
}) =>
  request.post<$res["updateOrderBackDelivery"]>({
    apiInfo: "更新退货物流信息",
    url: "/wxapp/orderBack/updateOrderBackDelivery",
    data
  })

export const confirmReceipt = (data: {
  /** 订单编号 */
  orderNo: string
}) =>
  request.post<$res["confirmReceipt"]>({
    apiInfo: "确认收货",
    url: "/wxapp/order/confirmReceipt",
    data
  })

export const deleteOrder = (data: {
  /** 订单编号 */
  orderNo: string
}) =>
  request.post<$res["deleteOrder"]>({
    apiInfo: "删除订单",
    url: "/wxapp/order/deleteOrder",
    data
  })

export const getOrderFollowWaybill = (data: {
  /** 订单编号 */
  orderNo: string
}) =>
  request.post<$res["getOrderFollowWaybill"]>({
    apiInfo: "获取订单物流token",
    url: "/wxapp/order/getOrderFollowWaybill",
    data,
    notifyApiError: false
  })

export const ncReceiveOrder = (data: {
  /** 订单编号 */
  orderNo: string
}) =>
  request.post<$res["ncReceiveOrder"]>({
    apiInfo: "同步订单到NC-况自用测试-其他人勿用！",
    url: "/wxapp/order/ncReceiveOrder",
    data
  })

export const orderCancelAndRefund = (data: {
  /** 订单编号 */
  orderNo: string
}) =>
  request.post<$res["orderCancelAndRefund"]>({
    apiInfo: "取消订单并退款",
    url: "/wxapp/order/orderCancelAndRefund",
    data
  })

export const orderConfirm = (data: {
  shoppingCartList?: {
    /** 商品信息id */
    productInfoId: long
    /** 整件数量-箱 */
    quantity: long
    /** 零售数量-个 */
    quantityRetail: long
  }[]
}) =>
  request.post<$res["orderConfirm"]>({
    apiInfo: "订单确认页",
    url: "/wxapp/order/orderConfirm",
    data
  })

export const orderConfirmToken = (data: { body?: Record<string, never> }) =>
  request.post<$res["orderConfirmToken"]>({
    apiInfo: "订单确认token",
    url: "/wxapp/order/orderConfirmToken",
    data
  })

export const orderDetail = (data: {
  /** 订单编号 */
  orderNo: string
}) =>
  request.post<$res["orderDetail"]>({
    apiInfo: "订单详情",
    url: "/wxapp/order/orderDetail",
    data
  })

export const orderDetailByTransactionId = (data: {
  /** 订单的支付单编号 */
  transactionId: string
}) =>
  request.post<$res["orderDetailByTransactionId"]>({
    apiInfo: "订单详情（通过支付单的TransactionId查询）",
    url: "/wxapp/order/orderDetailByTransactionId",
    data
  })

export const orderHandleStatus = (data: { body?: Record<string, never> }) =>
  request.post<$res["orderHandleStatus"]>({
    apiInfo: "订单处理状态",
    url: "/wxapp/order/orderHandleStatus",
    data
  })

export const orderListByInvoice = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  requestParams: {
    /** 订单状态0->待发货；1->待收货；2->已完成；3->已取消；4->已下单；5->强制关闭； */
    handleStatusRequests?: {
      /** 订单状态0->待发货；1->待收货；2->已完成；3->已取消；4->已下单；5->强制关闭； */
      handleStatus?: number
    }[]
    /** 支付状态：0->待支付；1->已支付；2->退款失败；3->退款成功；4->支付失败；5->退款中； */
    payStatus?: number
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["orderListByInvoice"]>({
    apiInfo: "发票管理-可开票订单-不会显示已开发票的订单",
    url: "/wxapp/order/orderListByInvoice",
    data
  })

export const orderListByWeChat = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  requestParams: {
    /** 订单状态0->待发货；1->待收货；2->已完成；3->已取消；4->已下单；5->强制关闭； */
    handleStatusRequests?: {
      /** 订单状态0->待发货；1->待收货；2->已完成；3->已取消；4->已下单；5->强制关闭； */
      handleStatus?: number
    }[]
    /** 支付状态：0->待支付；1->已支付；2->退款失败；3->退款成功；4->支付失败；5->退款中； */
    payStatus?: number
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["orderListByWeChat"]>({
    apiInfo: "订单列表-小程序",
    url: "/wxapp/order/orderListByWeChat",
    data
  })

export const submitOrder = (data: {
  /** 优惠券关联业务Id */
  couponMemberRelationId?: long
  /** 物流地址 */
  deliveryAddress?: string
  /** 物流公司 */
  deliveryName?: string
  /** 物流电话 */
  deliveryTel?: string
  /** 配送方式:0-场内配送;1-仓库自提;2-自费物流 */
  deliveryType: number
  /** 公司名称 */
  enterpriseAbbr: string
  /** 是否立即购买 */
  isBuyNow: boolean
  /** 收货地址的id */
  memberAddressId: long
  /** 防重令牌 */
  orderToken: string
  /** 订单备注 */
  remark?: string
  shoppingCartList?: {
    /** 商品信息id */
    productInfoId: long
    /** 整件数量-箱 */
    quantity: long
    /** 零售数量-个 */
    quantityRetail: long
  }[]
}) =>
  request.post<$res["submitOrder"]>({
    apiInfo: "订单提交-小程序",
    url: "/wxapp/order/submitOrder",
    data
  })

export const findOrderInvoiceDetail = (data: {
  /** 发票编号 */
  invoiceNo: string
}) =>
  request.post<$res["findOrderInvoiceDetail"]>({
    apiInfo: "发票详情查询",
    url: "/wxapp/order/invoice/findOrderInvoiceDetail",
    data
  })

export const findOrderInvoiceList = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  requestParams: {
    /** 发票状态，0->申请中;1->已开票 */
    invoiceStatus?: number
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["findOrderInvoiceList"]>({
    apiInfo: "发票列表查询",
    url: "/wxapp/order/invoice/findOrderInvoiceList",
    data
  })

export const insertOrderCommonInvoiceByBusiness = (data: {
  /** 开户银行 */
  invoiceBank?: string
  /** 银行账号 */
  invoiceBankAccount?: string
  /** 公司地址 */
  invoiceCompanyAddress?: string
  /** 公司电话 */
  invoiceCompanyTel?: string
  /** 税号 */
  invoiceDutyParagraph: string
  /** 电子邮箱 */
  invoiceEmail: string
  /** 发票抬头 */
  invoiceHeader: string
  /** 抬头类型，0-企业 */
  invoiceHeaderType: number
  /**   发票类型，0->电子普通发票;1->增值税电子专用发票 */
  invoiceType: number
  /** 订单编号列表 */
  orderNoDTOList: {
    /** 订单编号 */
    orderNo: string
  }[]
}) =>
  request.post<$res["insertOrderCommonInvoiceByBusiness"]>({
    apiInfo: "提交发票信息-普通发票-企业",
    url: "/wxapp/order/invoice/insertOrderCommonInvoiceByBusiness",
    data
  })

export const insertOrderCommonInvoiceByPerson = (data: {
  /** 电子邮箱 */
  invoiceEmail: string
  /** 发票抬头 */
  invoiceHeader: string
  /** 抬头类型，1-个人 */
  invoiceHeaderType: number
  /**   发票类型，0->电子普通发票; */
  invoiceType: number
  /** 订单编号列表 */
  orderNoDTOList: {
    /** 订单编号 */
    orderNo: string
  }[]
}) =>
  request.post<$res["insertOrderCommonInvoiceByPerson"]>({
    apiInfo: "提交发票信息-普通发票-个人",
    url: "/wxapp/order/invoice/insertOrderCommonInvoiceByPerson",
    data
  })

export const insertOrderMajorInvoiceByBusiness = (data: {
  /** 开户银行 */
  invoiceBank: string
  /** 银行账号 */
  invoiceBankAccount: string
  /** 公司地址 */
  invoiceCompanyAddress: string
  /** 公司电话 */
  invoiceCompanyTel: string
  /** 税号 */
  invoiceDutyParagraph: string
  /** 电子邮箱 */
  invoiceEmail: string
  /** 发票抬头 */
  invoiceHeader: string
  /** 抬头类型，0-企业 */
  invoiceHeaderType: number
  /** 发票类型，1->增值税电子专用发票 */
  invoiceType: number
  /** 订单编号列表 */
  orderNoDTOList: {
    /** 订单编号 */
    orderNo: string
  }[]
}) =>
  request.post<$res["insertOrderMajorInvoiceByBusiness"]>({
    apiInfo: "提交发票信息-专用发票-企业",
    url: "/wxapp/order/invoice/insertOrderMajorInvoiceByBusiness",
    data
  })

export const checkWeChatPayStatus = (data: {
  /** 订单编号 */
  orderNo: string
}) =>
  request.post<$res["checkWeChatPayStatus"]>({
    apiInfo: "检查微信支付状态",
    url: "/wxapp/pay/checkWeChatPayStatus",
    data,
    notifyApiError: false,
    notifyRequestError: false,
    notifyResponseError: false
  })

export const weChatPay = (data: {
  /** 订单号 */
  orderNo: string
  /** 微信code */
  wxCode: string
}) =>
  request.post<$res["weChatPay"]>({
    apiInfo: "微信支付",
    url: "/wxapp/pay/weChatPay",
    data
  })

export const categoryList = (data: { body?: Record<string, never> }) =>
  request.post<$res["categoryList"]>({
    apiInfo: "分类菜单列表",
    url: "/wxapp/category/categoryList",
    data,
    noNeedToken: true
  })

export const getCategoryRelationList = (data: {
  /** 分类Id */
  categoryId?: number
}) =>
  request.post<$res["getCategoryRelationList"]>({
    apiInfo: "获取关联商品列表",
    url: "/wxapp/category/getCategoryRelationList",
    data,
    noNeedToken: true
  })

export const findProductByCategoryIdTwo = (data: {
  /** 分类二级id */
  categoryIdTwo: number
}) =>
  request.post<$res["findProductByCategoryIdTwo"]>({
    apiInfo: "根据二级分类Id查询商品接口",
    url: "/wxapp/product/findProductByCategoryIdTwo",
    data,
    noNeedToken: true
  })

export const getRecommendProductList = (data: {
  body?: Record<string, never>
}) =>
  request.post<$res["getRecommendProductList"]>({
    apiInfo: "获取推荐商品列表",
    url: "/wxapp/product/getRecommendProductList",
    data,
    noNeedToken: true
  })

export const productDetail = (data: {
  /** 商品Id */
  productInfoId: long
}) =>
  request.post<$res["productDetail"]>({
    apiInfo: "商品详情",
    url: "/wxapp/product/productDetail",
    data,
    ignoreApiCodes: ["50006"],
    noNeedToken: true
  })

export const searchProduct = (data: {
  /** 分类三级id */
  categoryIdThree?: number
  /** 是否热销，为null不作筛选 */
  isHot?: boolean
  /** 是否新品，为null不作筛选 */
  isNew?: boolean
  /** 是否促销，为null不作筛选 */
  isPromoteSales?: boolean
  /** 是否推荐，为null不作筛选 */
  isRecommend?: boolean
  /** 关键词，为null或空时不作筛选 */
  keyword?: string
  /** 排序字段，当前支持:productPrice 字段 */
  orderField?: string
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["searchProduct"]>({
    apiInfo: "商品搜索",
    url: "/wxapp/product/searchProduct",
    data,
    noNeedToken: true
  })

export const addTheGoodsToTheCart = (data: {
  /** 商品信息id */
  productInfoId: long
  /** 整件数量-箱 */
  quantity: long
  /** 零售数量-个 */
  quantityRetail: long
}) =>
  request.post<$res["addTheGoodsToTheCart"]>({
    apiInfo: "加入购物车",
    url: "/wxapp/shoppingCart/addTheGoodsToTheCart",
    data,
    ignoreApiCodes: ["30003"]
  })

export const comeAgainCart = (data: {
  shoppingCartList?: {
    /** 商品信息id */
    productInfoId: long
    /** 整件数量-箱 */
    quantity: long
    /** 零售数量-个 */
    quantityRetail: long
  }[]
}) =>
  request.post<$res["comeAgainCart"]>({
    apiInfo: "再来一单-购物车",
    url: "/wxapp/shoppingCart/comeAgainCart",
    data
  })

export const deleteShoppingCar = (data: {
  /** 商品信息id列表 */
  productInfoIdList?: long[]
}) =>
  request.post<$res["deleteShoppingCar"]>({
    apiInfo: "删除购物车商品",
    url: "/wxapp/shoppingCart/deleteShoppingCar",
    data
  })

export const getCartList = (data: { body?: Record<string, never> }) =>
  request.post<$res["getCartList"]>({
    apiInfo: "获取购物车商品列表",
    url: "/wxapp/shoppingCart/getCartList",
    data
  })

export const selectProductSumPrice = (data: {
  shoppingCartChooseList?: {
    /** 商品信息id */
    productInfoId: long
    /** 整件数量-箱 */
    quantity: long
    /** 零售数量-个 */
    quantityRetail: long
  }[]
}) =>
  request.post<$res["selectProductSumPrice"]>({
    apiInfo: "选中商品购物车价格计算",
    url: "/wxapp/shoppingCart/selectProductSumPrice",
    data
  })

export const updateShoppingCar = (data: {
  /** 商品信息id */
  productInfoId: long
  /** 整件数量-箱 */
  quantity: long
  /** 零售数量-个 */
  quantityRetail: long
}) =>
  request.post<$res["updateShoppingCar"]>({
    apiInfo: "更新购物车",
    url: "/wxapp/shoppingCart/updateShoppingCar",
    data,
    ignoreApiCodes: ["30003", "90004"]
  })

export const productShelvesStockActualTime = (data: {
  /** 商品信息id */
  productInfoId: long
}) =>
  request.post<$res["productShelvesStockActualTime"]>({
    apiInfo: "商品实时上架库存",
    url: "/wxapp/stock/productShelvesStockActualTime",
    data
  })

export const getRewardsPointsDetail = (data: {
  /** 优惠券编号 */
  couponNo: string
  /** 兑换方案编号 */
  rewardsPointsNo?: string
}) =>
  request.post<$res["getRewardsPointsDetail"]>({
    apiInfo: "获取兑换方案详情",
    url: "/wxapp/rewardspoints/getRewardsPointsDetail",
    data
  })

export const getRewardsPointsList = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  requestParams: {
    /** 兑换类型: 0->优惠券; */
    rewardsPointsType?: number
  }
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["getRewardsPointsList"]>({
    apiInfo: "积分中心列表",
    url: "/wxapp/rewardspoints/getRewardsPointsList",
    data
  })

export const getRewardsPointsReceiveRecordDetail = (data: {
  /** 兑换记录id */
  rewardsPointsReceiveRecordId?: long
}) =>
  request.post<$res["getRewardsPointsReceiveRecordDetail"]>({
    apiInfo: "获取积分兑换记录详情",
    url: "/wxapp/rewardspoints/getRewardsPointsReceiveRecordDetail",
    data
  })

export const getRewardsPointsReceiveRecordList = (data: {
  orderField?: string
  pageIndex: long
  pageSize: long
  /** 排序方式 desc or asc */
  sortType?: string
}) =>
  request.post<$res["getRewardsPointsReceiveRecordList"]>({
    apiInfo: "获取积分兑换记录列表",
    url: "/wxapp/rewardspoints/getRewardsPointsReceiveRecordList",
    data
  })

export const rewardsPointsReceiveCoupon = (data: {
  /** 优惠券编号 */
  couponNo: string
  /** 兑换方案编号 */
  rewardsPointsNo?: string
}) =>
  request.post<$res["rewardsPointsReceiveCoupon"]>({
    apiInfo: "积分兑换",
    url: "/wxapp/rewardspoints/rewardsPointsReceiveCoupon",
    data,
    ignoreApiCodes: ["14122"]
  })
