<template>
  <view class="v-empty flex-center-center">
    <view :style="{ padding: padding }">
      <v-image
        v-if="src"
        :height="height"
        :width="width"
        :src="src"
      />
      <view class="text text-light text-center">
        <slot />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  height?: string
  width?: string
  src?: string
  padding?: string
}>(), {
  height: "320rpx",
  width: "320rpx",
  src: undefined,
  padding: "100rpx 0"
})
</script>

<style lang="scss" scoped>
.text {
  margin-top: 10rpx;
}
</style>
