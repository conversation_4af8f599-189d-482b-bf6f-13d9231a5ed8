## 构建版本
该项目使用 `nodejs` 和 `pnpm` 进行构建，使用微信开发者工具进行调试  
当前版本如下，大版本尽量一样，小版本可以不一致
``` bash
node -v
# 20.12.2

npm -v
# 10.9.0

pnpm -v
# 9.13.2
```
## 常用指令
``` bash
# 安装依赖
pnpm i

# 编译小程序 (开发环境
# 生成目录在 dist/dev/mp-weixin，可以用微信开发者工具打开这个目录，而不是打开项目根目录
pnpm run dev:mp-weixin

# 编译小程序 (生产环境
# 生成目录在 dist/build/mp-weixin，可以用微信开发者工具打开这个目录，而不是打开项目根目录
pnpm run build:mp-weixin
```
## 环境变量
位于 `src/apis/config.ts` 中  
`baseUrl` 是请求地址  
用于与 `src/apis/index.ts` 的 `url` 进行拼接进行请求  
`kfid` 是客服id，用于企业微信客服  
`corpId` 是企业id，用于企业微信客服  
`customerUrl` 是企业微信客服的请求地址  

位于 `src/manifest.json` 中  
`appid` 是小程序的appid  

## 发布
1. 使用`pnpm run build:mp-weixin`进行编译
2. 使用微信开发者工具打开`dist/build/mp-weixin`目录
3. 界面上方，中部位置，清缓存全部清除
4. 点击左边编译，等待重新构建，出现页面 (如果没重新加载就从第一步开始再来一次，或者重新打开开发者工具，它偶尔会抽风)
5. 界面右上，上传按钮

## 请求相关
当前项目请求做了强类型校验

请求库的声明文件写在 `src/utils/http/request/types.ts` 类型文件，在请求库外部应该不会被调用  
而请求配置以及相关类型写在 `src/types/request.d.ts` 声明文件  
传入参数类型直接写在 `src/apis/apis.ts` 接口文件的每个接口函数传入的参数中

接口返回的类型写在 `src/types/response.d.ts` 声明文件，作为一个全局的接口

本项目的 `src/apis/apis.ts` 和 `src/types/response.d.ts` 由后台项目生成，这边没对应页面

## 目录结构
所有项目配置在根目录下

所有的业务代码在 `src` 目录下

`src/apis` 是请求接口，其他地方以 `$api.xxx` 的形式调用  
`src/apis/apis.ts` 包含所有的请求接口

`src/components` 为所有的组件，这里由 `uni-app` 自动导入，需文件夹名、组件名一致

`src/hooks` 统一管理的一些钩子函数  
`$toast` 为小弹窗通知  

`src/composables` 为所有的组合式函数，一些公用的逻辑提取在这里

`src/pages` 为主包页面  
`src/package-xxx` 为分包页面  
其中若文件夹内包含 `modules` 则为页面模块，可以认为是页面专用的组件  

`src/stores` 状态管理仓库，使用 `pinia` 进行状态管理，其他地方以 `$store.xxx` 的形式调用  

`src/styles` 为样式文件  

`src/types` 为类型定义文件  
其中 `auto-imports.d.ts` 为`unplugin-auto-import` 库自动导入的文件，不应该手动修改  

`src/utils` 为工具类  
`src/utils/http` 请求库  
`src/utils/auth` 负责管理token和登录状态  
