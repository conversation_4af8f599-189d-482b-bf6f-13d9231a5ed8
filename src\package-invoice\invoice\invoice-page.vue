<template>
  <view>
    <view class="page-header background-white">
      <v-navbar title="发票管理" back />
      <v-tabs
        v-model="tabIndex"
        :list="['可开票订单', '申请中', '已开票']"
        height="86rpx"
      >
        <view class="v-tab-block" style="transform: translateX(0)" />
      </v-tabs>
    </view>
    <swiper
      :current="tabIndex"
      :style="{ height: `${pageHeight}px` }"
      @change="tabSwiperChange"
      @transition="wxs.transition"
      @animationfinish="wxs.transitionFinish"
    >
      <swiper-item>
        <invoiceList
          ref="invoiceList0"
          :page-height="pageHeight"
          status="可开票订单"
        />
      </swiper-item>
      <swiper-item>
        <invoiceList
          ref="invoiceList1"
          :page-height="pageHeight"
          status="申请中"
        />
      </swiper-item>
      <swiper-item>
        <invoiceList
          ref="invoiceList2"
          :page-height="pageHeight"
          status="已开票"
        />
      </swiper-item>
    </swiper>
    <view v-if="isShowBtnGroup" class="bottom-btn-group">
      <view class="flex-center-between margin-adjust">
        <view class="flex-center" @click="selectedAllItem">
          <v-checkbox :checked="selectedStatus" />
          <view style="margin: 0 6rpx">
            全选
          </view>
        </view>
        <view class="flex-center">
          <view>已选</view>
          <view style="color: #005bac; margin: 0 4rpx">
            {{ countLength }}
          </view>
          <view>个订单</view>
        </view>
      </view>
      <view class="flex-center-around">
        <v-button
          :border="false"
          width="306rpx"
          height="94rpx"
          background-color="#e5eef7"
          @click="goToInoviceMsgManagement"
        >
          <view style="font-size: 28rpx; color: #005bac">
            开票信息管理
          </view>
        </v-button>
        <v-button
          :border="false"
          width="388rpx"
          height="94rpx"
          background-color="#005BAC"
          @click="goToSelectNotIssueInvoicePage"
        >
          <view style="font-size: 32rpx; font-weight: 800; color: #fff">
            申请开票
          </view>
        </v-button>
      </view>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script lang="ts">
import invoiceList from "./modules/invoice-list.vue"
import wxs from "@/utils/wxs"

export default {
  components: {
    invoiceList
  },
  setup () {
    const pageStore = $store.page()
    const notIssueInvoiceList = computed(() => {
      return pageStore.notIssueInvoiceList
    })
    const countLength = computed(() => {
      if (!pageStore.notIssueInvoiceList) return 0
      return pageStore.notIssueInvoiceList.filter((item) => item.selected)
        .length
    })
    const selectedStatus = computed(() => {
      if (!pageStore.notIssueInvoiceList) return false
      return !pageStore.notIssueInvoiceList.some((item) => !item.selected)
    })
    return { countLength, notIssueInvoiceList, selectedStatus }
  },
  data () {
    return {
      countSelectedItem: 0,
      isShowBtnGroup: true,
      pageHeight: 0,
      tabIndex: 0,
      wxs: {
        transition: wxs.transition,
        transitionFinish: wxs.transitionFinish
      }
    }
  },
  onReady () {
    this.refreshPageHeight()
  },
  onShow () {
    const listRef = this.$refs.invoiceList0 as InstanceType<typeof invoiceList>
    listRef.initInvoiceList()
  },
  methods: {
    selectedAllItem () {
      const flag = !this.selectedStatus
      this.notIssueInvoiceList.forEach((item) => (item.selected = flag))
    },
    goToSelectNotIssueInvoicePage () {
      if (
        this.notIssueInvoiceList.filter((item) => item.selected).length === 0
      ) {
        return uni.showToast({ title: "请选择可开票订单", icon: "none" })
      }
      uni.navigateTo({ url: "/package-invoice/invoice/invoice-form" })

      // uni.navigateTo({ url: "/pages/invoice/select-not-issue-invoice" })
    },
    goToInoviceMsgManagement () {
      // select参数 是否支持选中并返回页面
      uni.navigateTo({ url: "/package-invoice/invoice/invoice-msg-management?select=0" })
    },
    tabSwiperChange (event: SwiperChangeEvent) {
      this.tabIndex = event?.detail?.current
      const listRef = this.$refs[
        `invoiceList${event?.detail?.current}`
      ] as InstanceType<typeof invoiceList>
      listRef.initInvoiceList()
      this.isShowBtnGroup = this.tabIndex === 0
    },
    async refreshPageHeight () {
      const system = uni.getWindowInfo()
      let screenHeight
      // #ifdef MP-WEIXIN
      screenHeight = system.screenHeight
      // #endif
      // #ifdef MP-ALIPAY
      screenHeight = system.windowHeight
      // #endif
      // #ifdef H5
      screenHeight = system.screenHeight
      // #endif
      const query = uni.createSelectorQuery()
      const headerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-header")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      this.pageHeight = screenHeight - headerHeight
    }
  }
}
</script>

<script
  src="../../components/v-tabs/v-tabs.wxs"
  module="wxs"
  lang="wxs"
></script>

<style lang="scss" scoped>
.v-tab-block {
  height: 100%;
  width: 100%;
  background-color: #005bac;
}

.bottom-btn-group {
  padding-top: 28rpx;
  background-color: #fff;
  position: fixed;
  left: 0rpx;
  right: 0rpx;
  bottom: 0;
  z-index: 10;
}
.margin-adjust {
  margin: 0 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}
</style>
