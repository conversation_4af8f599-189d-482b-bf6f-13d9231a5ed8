<template>
  <view class="scroll-view-container">
    <view class="tabs flex">
      <view
        class="tabs-item flex-center-center"
        :class="{ active: tabPosition === 'product' }"
        @click="() => clickTab('product')"
      >
        商品
      </view>
      <view
        class="tabs-item flex-center-center"
        :class="{ active: tabPosition === 'product-info' }"
        @click="() => clickTab('product-info')"
      >
        详细信息
      </view>
      <view
        class="tabs-item flex-center-center"
        :class="{ active: tabPosition === 'product-details' }"
        @click="() => clickTab('product-details')"
      >
        图片详情
      </view>
      <view
        class="tabs-item flex-center-center"
        :class="{ active: tabPosition === 'delivery-explain' }"
        @click="() => clickTab('delivery-explain')"
      >
        订购说明
      </view>
    </view>
    <scroll-view
      scroll-y
      :scroll-into-view="scrollId"
      :style="{ height: `${pageHeight}px` }"
      :tab="tabPosition"
      :change:tab="wxs.changeTab"
      scroll-with-animation
      @scroll="wxs.scrollHandle"
    >
      <view id="product" class="product">
        <view class="product-content">
          <view class="swiper-container">
            <v-swiper
              :list="product.imageList"
              height="750rpx"
              width="750rpx"
              type="count"
              circular
            />
          </view>
          <view class="v-block">
            <view class="price flex-baseline-between">
              <v-price :price="product.displayPrice" size="40rpx">
                <view class="text-26 text-1-row">
                  <text>/pcs</text>
                  <text class="origin-price">
                    {{ `原价:￥${product.marketPrice}` }}
                  </text>
                </view>
              </v-price>
            </view>
            <view class="text-32 text-bold text-2-row">
              {{ product.productSpecName || product.title }}
            </view>
          </view>
          <view class="v-block spec-info flex--between">
            <view class="text-light">
              说明
            </view>
            <view>
              按箱起售
              {{ `${product.displayAmount}pcs/箱` }}
            </view>
          </view>
          <view
            class="v-block spec-info flex--between"
            @click="$emit('show-select')"
          >
            <view class="text-light">
              {{ product ? "已选" : "请选择" }}
            </view>
            <view class="flex-1">
              <view class="text-right text-1-row" style="margin-bottom: 6rpx">
                {{ product.productSpecName || product.name }}
              </view>
            </view>
            <view class="icon-container">
              <v-icon
                height="30rpx"
                width="30rpx"
                margin-left="10rpx"
                src="/static/icons/right-arrow.png"
              />
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="relatedGoodsList && relatedGoodsList.length > 0"
        id="related-goods-list"
        class="v-block"
      >
        <view class="headline">
          关联商品
        </view>
        <scroll-view scroll-x scroll-with-animation class="related-goods-list">
          <view
            v-for="item in relatedGoodsList"
            :key="item.categoryId"
            class="related-goods-list"
            @click="navigateToProductDetail(item.productId)"
          >
            <view class="related-goods">
              <view class="goods-item">
                <v-image
                  border-radius="16rpx 16rpx 0 0"
                  height="180rpx"
                  width="180rpx"
                  :src="item.categoryImg"
                  mode="aspectFill"
                />
                <view class="categoryName">
                  {{ item.categoryName }}
                </view>
                <v-price :price="item.price">
                  /pcs
                </v-price>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view id="product-info" class="product-info">
        <view class="product-info-content">
          <view class="v-block" style="padding: 12rpx 24rpx">
            <view class="info-item flex-center-between">
              <view class="label">
                商品名称
              </view>
              <view class="value text-bold">
                {{ product.productSpecName || product.name }}
              </view>
            </view>
            <view class="divider" />
            <view class="info-item flex-center-between">
              <view class="label">
                商品编号
              </view>
              <view class="value text-bold">
                {{ product.productSpecNumber }}
              </view>
            </view>
            <view class="divider" />
            <view class="info-item flex-center-between">
              <view class="label">
                商品毛重
              </view>
              <view class="value text-bold">
                {{ product.ncWeight }}
              </view>
            </view>
            <view class="divider" />
            <view class="info-item flex-center-between">
              <view class="label">
                产品规格
              </view>
              <view class="value text-bold">
                {{ product.productSpec }}
              </view>
            </view>
            <view class="divider" />
            <view class="info-item flex-center-between">
              <view class="label">
                外箱规格
              </view>
              <view class="value text-bold">
                {{ product.outerBoxSpec }}
              </view>
            </view>
            <view class="divider" />
            <view class="info-item flex-center-between">
              <view class="label">
                材质
              </view>
              <view class="value text-bold">
                {{ product.ncMaterial }}
              </view>
            </view>
            <view class="divider" />
            <view class="info-item flex-center-between">
              <view class="label">
                商品分类
              </view>
              <view class="value text-bold">
                {{ product.ncCategory }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <view id="product-details" class="product-details">
        <view class="product-details-content">
          <view class="v-block">
            <v-image
              v-for="image in productDescriptionList"
              :key="image"
              :src="image"
              border-radius="0"
            />
          </view>
        </view>
      </view>
      <view id="delivery-explain" class="delivery-explain">
        <view class="delivery-explain-content">
          <view v-if="webName === 'cheerray'" 
            class="v-block"
            :style="`min-height: calc(${pageHeight}px - 158rpx); margin: 0 16rpx`"
          >
            <productDeliveryExplainCheerray />
          </view>
          <view v-else
            class="v-block"
            :style="`min-height: calc(${pageHeight}px - 158rpx); margin: 0 16rpx`"
          >
            <ProductDeliveryExplain />
          </view>
        </view>
      </view>
      <view class="page-padding-bottom" />
    </scroll-view>
  </view>
</template>

<script lang="ts">
import ProductDeliveryExplain from "./product-delivery-explain.vue"
import productDeliveryExplainCheerray from "./product-delivery-explain-cheerray.vue"
import wxs from "@/utils/wxs"
import type { PropType } from "vue"
import { webName as globalWebName  } from '@/apis/config' 

export default {
  components: {
    ProductDeliveryExplain,
    productDeliveryExplainCheerray
  },
  props: {
    pageHeight: {
      type: Number,
      required: true
    },
    product: {
      type: Object as PropType<ProductDetails>,
      required: true
    },
    relatedGoodsList: {
      type: Object as PropType<RelatedGoods[]>,
      required: true
    }
  },
  emits: ["show-select"],
  data () {
    return {
      scrollId: "product",
      tabPosition: "product",
      wxs: {
        changeTab: wxs.changeTab,
        scrollHandle: wxs.scrollHandle
      }
    }
  },
  computed: {
    productDescriptionList () {
      return (
        this.product?.description?.match(/http([^"]*?)(\.jpg|\.png|\.jpeg)/g) ?? []
      )
    },
    webName () {
      return globalWebName
    }
  },
  methods: {
    async scrollToPosition (position: string) {
      this.scrollId = ""
      await new Promise((resolve) => nextTick(() => resolve(true)))
      this.scrollId = position
    },
    changeTab (position: string) {
      this.tabPosition = position
    },
    navigateToProductDetail (productId: string) {
      const pageLength = getCurrentPages()?.length ?? 0
      if (pageLength >= 6) {
        console.log(pageLength)
        uni.redirectTo({
          url: `/package-product/product/product-details?productid=${productId}`
        })
      } else {
        uni.navigateTo({
          url: `/package-product/product/product-details?productid=${productId}`
        })
      }
    },
    clickTab (position: string) {
      this.scrollToPosition(position)
      this.changeTab(position)
    }
  }
}
</script>

<script src="./product-details-content.wxs" module="wxs" lang="wxs"></script>

<style lang="scss" scoped>
.scroll-view-container {
  position: relative;
  .product-details,
  .product-info,
  .delivery-explain {
    position: relative;
    margin-top: -98rpx;
    padding-top: 98rpx;
  }
  .product {
    position: relative;
    z-index: 4;
  }

  .product-details {
    z-index: 3;
  }
  .product-info {
    z-index: 2;
  }
  .delivery-explain {
    z-index: 1;
  }
  .product-content,
  .product-details-content,
  .product-info-content {
    overflow: hidden;
  }
}
.swiper-container {
  margin-bottom: 24rpx;
}
.v-block {
  padding: 24rpx;
  margin: 0 16rpx 24rpx;
  .price {
    margin-bottom: 8rpx;
    .origin-price {
      text-decoration: line-through;
      margin-left: 10rpx;
    }
  }
}
.spec-info {
  line-height: 38rpx;
  .icon-container {
    padding-top: 6rpx;
  }
}
.info-item {
  padding: 24rpx;
  .label {
    width: 140rpx;
  }
  .value {
    max-width: calc(100% - 140rpx);
  }
}
.tabs {
  position: absolute;
  height: 86rpx;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
  opacity: 0;
  pointer-events: none;
  box-shadow: 0 4px 4px -4px rgba(0, 0, 0, 0.1);
  .tabs-item {
    position: relative;
    flex: 1;
    .text {
      color: #999999;
      transition: color 0.2s ease-out;
    }
    .icon {
      margin-left: 8rpx;
      .desc {
        transform-origin: 50% 50%;
        transform: rotate(180deg);
      }
    }
    &::after {
      content: " ";
      position: absolute;
      left: 50%;
      bottom: 0;
      height: 4rpx;
      width: 0;
      background-color: #005bac;
      transform: translateX(-50%);
      transition: width 0.2s ease-out;
    }
    &.active {
      .text {
        color: #231815;
        font-weight: bold;
      }
      &::after {
        width: 110rpx;
      }
    }
  }
}
.related-goods-list {
  display: inline-block;
  white-space: nowrap;
  position: relative;
  z-index: 9;
  .goods-item {
    margin-right: 24rpx;
    width: 180rpx;
    height: 270rpx;
    border-radius: 16rpx;
    border: 1rpx solid #dcdcdc;
    .categoryName {
      padding: 16rpx;
      font-size: 24rpx;
      padding-bottom: 0;
      box-sizing: border-box;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.headline {
  font-size: 28rpx;
  margin-bottom: 24rpx;
  color: #999999;
}
</style>
