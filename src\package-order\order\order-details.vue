<template>
  <view>
    <view
      v-if="order"
      class="background-status"
      :style="{
        '--bar-height': `${navBarHeight + statusBarHeight}px`,
        backgroundColor: backgroundColor
      }"
    />
    <view class="page-header hide-shadow">
      <v-navbar
        :type="order ? 'white' : 'default'"
        back
        background-color="transparent"
      />
    </view>
    <v-loading-block v-if="!order || !pageHeight" />
    <scroll-view
      v-else
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-default-style="
        backgroundColor === '#005bac' ? 'white' : 'black'
      "
      :refresher-triggered="refreshing"
      :refresher-background="backgroundColor"
      @refresherrefresh="refresh"
    >
      <view class="status-list" :style="{ backgroundColor: backgroundColor }">
        <view class="status-pad flex-center-between text-white">
          <view class="status-text">
            {{ orderStatus(order) }}
          </view>
          <v-countdown
            v-if="order.payStatus === '0' && order.orderStatus !== '3'"
            :time="counterCount"
          />
        </view>
        <view class="status-info text-white">
          {{ orderStatusInfo(order) }}
        </view>
      </view>
      <view v-if="order.deliveryType !== '1'" class="address-container">
        <view class="v-block">
          <view class="address flex-center">
            <view class="address-name text-32 text-bold text-1-row">
              {{ order.name }}
            </view>
            <view class="text-32 text-bold text-1-row">
              {{ order.mobile }}
            </view>
          </view>
          <view class="address-info">
            <view class="text-sub text-2-row">
              {{ order.address }}
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="
          order.deliveryType === '2' &&
            (order.orderStatus === '2' || order.orderStatus === '1')
        "
        class="v-block"
        @click="openWaybill"
      >
        <view class="order-info flex-center-between">
          <view> 物流状态 </view>
          <view class="flex-center">
            <view class="review">
              查看
            </view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
        </view>
      </view>
      <view class="v-block">
        <template
          v-for="(product, productIndex) in order.productList"
          :key="product.id"
        >
          <view v-if="productIndex !== 0" class="divider" />
          <view class="product-item">
            <v-product-item
              :product="product"
              @click="() => navigateToProduct(product)"
            >
              <template #name>
                {{ product.name }}
              </template>
              <template #spec>
                {{ product.productInfoSpec }}
              </template>
              <template #default>
                <view class="flex-center-between">
                  <view class="text-light text-24">
                    {{ `装箱规格: ${product.displayAmount}pcs/箱` }}
                  </view>
                  <view class="text-light text-24">
                    {{ `订购数量: ${product.number}` }}
                  </view>
                </view>
              </template>
            </v-product-item>
            <view class="product-count flex-baseline-between text-sub text-28">
              <view class="flex-baseline">
                <text> 单价: </text>
                <v-price :price="product.displayPrice" size="28rpx">
                  /pcs
                </v-price>
              </view>
              <view class="flex-baseline">
                <text> 小计: </text>
                <v-price
                  :price="
                    product.number * product.unitAmount * product.unitPrice
                  "
                  size="28rpx"
                />
              </view>
            </view>
          </view>
        </template>
        <view class="order-info flex-center-between">
          <view> 商品总额 </view>
          <v-price :price="order.orderPrice" color="#231815" />
        </view>
        <template v-if="order.discountAmount">
          <view class="divider" />
          <view class="order-info flex-center-between">
            <view> 优惠券 </view>
            <v-price :price="-order.discountAmount" />
          </view>
        </template>
        <view class="divider" />
        <!-- <view class="order-info flex-center-between">
          <view> 运费 </view>
          <v-price :price="0" color="#231815" />
        </view>
        <view class="divider" /> -->
        <view class="order-info flex-center-between">
          <view>
            {{ order.payStatus === "1" ? "实付" : "应付" }}
          </view>
          <v-price
            :price="
              order.payStatus === '1'
                ? order.actualPayPrice
                : order.shouldPayPrice
            "
            size="32rpx"
          />
        </view>
        <view class="divider" />
        <view class="order-info flex-center-between">
          <view> 配送方式 </view>
          <view>
            <text v-if="order.deliveryType === '0'">
              场内配送
            </text>
            <text v-else-if="order.deliveryType === '1'">
              仓库自提
            </text>
            <text v-else-if="order.deliveryType === '2'">
              自费物流
            </text>
          </view>
        </view>
      </view>
      <view class="v-block">
        <view class="order-info flex-center-between">
          <view class="order-label">
            订单编号
          </view>
          <view class="text-light flex-1">
            {{ order.orderNo }}
          </view>
          <view class="flex-center" @click="copyOrderNo">
            <v-icon size="28rpx" src="/static/icons/order/copy.png" />
            <view class="text-20" style="color: #005bac">
              复制
            </view>
          </view>
        </view>
        <view class="divider" />
        <view class="order-info flex-center-between">
          <view class="order-label">
            支付方式
          </view>
          <view class="text-light flex-1">
            {{
              ["微信支付", "支付宝支付"][parseInt(order.payType)] ?? "未支付"
            }}
          </view>
        </view>
        <template v-if="order.payTime">
          <view class="divider" />
          <view class="order-info flex-center-between">
            <view class="order-label">
              支付时间
            </view>
            <view class="text-light flex-1">
              {{ formateUtc(order.payTime) }}
            </view>
          </view>
        </template>
        <template v-if="order.remark">
          <view class="divider" />
          <view class="order-info flex-center-between">
            <view class="order-label">
              备注
            </view>
            <view class="text-light flex-1">
              {{ order.remark }}
            </view>
          </view>
        </template>
        <template v-if="order.cancelTime">
          <view class="divider" />
          <view class="order-info flex-center-between">
            <view class="order-label">
              取消时间
            </view>
            <view class="text-light flex-1">
              {{ formateUtc(order.cancelTime) }}
            </view>
          </view>
        </template>
      </view>
      <view class="button-list-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view v-if="order" class="button-list">
      <view class="flex-center-end">
        <view class="button-item">
          <!-- <v-button
            height="94rpx"
            background-color="white"
            @click="customerService"
          >
            <view class="flex-center">
              <v-icon
                size="34rpx"
                src="/static/icons/order/customer-service-dark.png"
              />
              <view style="margin-left: 10rpx">
                客服
              </view>
            </view>
          </v-button> -->
          <v-button
            height="94rpx"
            background-color="white"
            open-type="contact"
            send-message-title="订单详情"
            :send-message-path="`/package-order/order/order-details.html?orderno=${orderNo}&customer=2`"
            :send-message-img="logoSrc"
            show-message-card
          >
            <view class="flex-center">
              <v-icon
                size="34rpx"
                src="/static/icons/order/customer-service-dark.png"
              />
              <view style="margin-left: 10rpx">
                客服
              </view>
            </view>
          </v-button>
        </view>
        <template v-if="order.orderStatus === '4' && order.payStatus === '0'">
          <!-- 待支付 -->
          <view class="button-item">
            <v-button
              height="94rpx"
              background-color="white"
              @click="cancelOrderHandle"
            >
              取消订单
            </v-button>
          </view>
          <view class="button-item">
            <v-button type="primary" height="94rpx" @click="submitPay">
              去支付
            </v-button>
          </view>
        </template>
        <template
          v-else-if="order.orderStatus === '4' && order.payStatus === '1'"
        >
          <!-- 已支付 可取消状态 -->
          <view class="button-item">
            <v-button height="94rpx" type="primary" @click="cancelOrderHandle">
              取消订单
            </v-button>
          </view>
        </template>
        <template v-else-if="order.orderStatus === '0'">
          <!-- 待发货 -->
          <view class="button-item">
            <v-button height="94rpx" type="primary" @click="cancelOrderHandle">
              取消订单
            </v-button>
          </view>
        </template>
        <template v-else-if="order.orderStatus === '1'">
          <!-- 待收货 -->
          <view class="button-item">
            <v-button type="primary" height="94rpx" @click="receiveHandle">
              确认收货
            </v-button>
          </view>
        </template>
        <template v-else-if="order.orderStatus === '2'">
          <!-- 已完成 -->
          <view v-if="order.ableAfterSale" class="button-item">
            <v-button
              height="94rpx"
              background-color="white"
              @click="navigateToSelectProduct"
            >
              售后申请
            </v-button>
          </view>
          <view
            v-else-if="[0, 1].includes(order.afterSaleStatus)"
            class="button-item"
          >
            <v-button
              height="94rpx"
              background-color="white"
              @click="cancelAftersales"
            >
              取消售后
            </v-button>
          </view>
          <view v-if="order.orderAfterSaleId" class="button-item">
            <v-button
              type="primary"
              height="94rpx"
              @click="navigateToafterSalesDetails"
            >
              售后查询
            </v-button>
          </view>
        </template>
        <template v-else-if="['3', '5'].includes(order.orderStatus)">
          <!-- 已取消 包括手动和强制关闭 -->
          <view class="button-item">
            <v-button
              height="94rpx"
              background-color="white"
              @click="deleteOrderHandle"
            >
              删除订单
            </v-button>
          </view>
        </template>
        <!-- 退款失败、退款成功、退款中 显示 -->
        <template v-if="['2', '3', '5'].includes(order.payStatus)">
          <view class="button-item">
            <v-button
              type="primary"
              height="94rpx"
              @click="navigateToRefundPage"
            >
              退款查询
            </v-button>
          </view>
        </template>
      </view>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
import { str2num } from "@/utils/type-transfer"
import usePayment from "@/composables/use-payment"
import useOrder from "@/composables/use-order"
import { checkToken } from "@/utils/auth/token"

const { safeNavigateBack, pageHeight } = usePageLayout()
const { navBarHeight, statusBarHeight } = useNavbarLayout()
const { payOrder } = usePayment()
const { orderStatus, orderStatusInfo, deleteOrder, cancelOrder, receive } =
  useOrder()

const backgroundColor = computed(() => {
  if (!order.value) return "#FFF"
  if (order.value.orderStatus === "6" || order.value.orderStatus === "7") {
    return "#999999"
  }
  return "#005bac"
})

const navigateToafterSalesDetails = () => {
  uni.navigateTo({
    url: `/package-aftersales/aftersales/aftersales-details?id=${order.value?.orderAfterSaleId}`
  })
}
const orderNo = ref("")
const transactionId = ref<string>()
const order = ref<OrderDetails>()

const counterCount = ref(0)
const checkCountDown = async () => {
  if (order.value?.payStatus !== "0") return
  const now = new Date().getTime()
  const time = order.value.payDeadline
  const deadline = new Date(time).getTime()
  counterCount.value = Math.floor((deadline - now) / 1000)
}

/** 取消售后单 */
const cancelAftersales = async () => {
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "提示",
      content: "是否确认取消该售后申请?",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return
  uni.showLoading({ title: "加载中", mask: true })
  const response = await $api.cancelOrderRefund({
    orderBackId: order.value?.orderAfterSaleId
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      uni.showToast({ title: "取消成功", mask: true })
      getOrderDetails()
  }
}

/** 刷新 */
const refreshing = ref(false)
const refresh = async () => {
  refreshing.value = true
  await getOrderDetails()
  await nextTick()
  refreshing.value = false
}
const getOrderDetails = async () => {
  const response = await $api.orderDetail({
    orderNo: orderNo.value
  })
  switch (response.__error) {
    case undefined: {
      const data = response.data
      order.value = {
        discountAmount: str2num(data.discountAmount),
        afterSaleStatus: data.afterSaleStatus,
        invoiceNo: data.invoiceNo,
        orderId: data.id,
        orderNo: data.orderNo,
        orderStatus: data.handleStatus,
        payType: data.payType,
        orderPrice: str2num(data.sumPrice),
        shouldPayPrice: str2num(data.shouldPayCost),
        actualPayPrice: str2num(data.actualPayCost),
        orderTime: data.postDate,
        payTime: data.paymentTime,
        payDeadline: data.postDateEnd,
        completeTime: data.receiveTime,
        orderAfterSaleId: data.orderAfterSaleId,
        refundTime: data.refundTime,
        cancelTime: data.cancelTime,
        payStatus: data.payStatus,
        ableAfterSale: data.isLaunchAfterSale === 1,
        payTransactionId: data.payTransactionId,
        productList: data.orderProductRelationVOList.map((product) => {
          const item: ProductOrderItem = {
            id: product.productInfoId,
            image: product.productInfoProductImg,
            name: product.productInfoName,
            unitPrice: str2num(product.productInfoPrice),
            unitAmount: str2num(product.productInfoPackage),
            displayPrice: str2num(product.displayUnitPrice),
            displayAmount: product.displayPackage,
            number: str2num(product.quantity),
            productInfoSpec: product.productInfoSpec,
            totalPackage: product.totalPackage
          }
          return item
        }),
        address: data.consigneeAddress,
        mobile: data.consigneeMobile,
        name: data.consigneeName,
        deliveryCode: data.deliveryCode,
        deliveryName: data.deliveryName,
        deliveryNo: data.deliveryNo,
        deliveryType: data.deliveryType,
        remark: data.remark
      }
      checkCountDown()
      break
    }
    default:
      navigateBack()
  }
}
const getOrderDetailsByTransactionId = async () => {
  const response = await $api.orderDetailByTransactionId({
    transactionId: transactionId.value as string
  })
  switch (response.__error) {
    case undefined: {
      const data = response.data
      orderNo.value = data.orderNo
      transactionId.value = undefined
      order.value = {
        discountAmount: str2num(data.discountAmount),
        afterSaleStatus: data.afterSaleStatus,
        invoiceNo: data.invoiceNo,
        orderId: data.id,
        orderNo: data.orderNo,
        orderStatus: data.handleStatus,
        payType: data.payType,
        orderPrice: str2num(data.sumPrice),
        shouldPayPrice: str2num(data.shouldPayCost),
        actualPayPrice: str2num(data.actualPayCost),
        orderTime: data.postDate,
        payTime: data.paymentTime,
        payDeadline: data.postDateEnd,
        completeTime: data.receiveTime,
        orderAfterSaleId: data.orderAfterSaleId,
        refundTime: data.refundTime,
        cancelTime: data.cancelTime,
        payStatus: data.payStatus,
        ableAfterSale: data.isLaunchAfterSale === 1,
        payTransactionId: data.payTransactionId,
        productList: data.orderProductRelationVOList.map((product) => {
          const item: ProductOrderItem = {
            id: product.productInfoId,
            image: product.productInfoProductImg,
            name: product.productInfoName,
            unitPrice: str2num(product.productInfoPrice),
            unitAmount: str2num(product.productInfoPackage),
            displayPrice: str2num(product.displayUnitPrice),
            displayAmount: product.displayPackage,
            number: str2num(product.quantity),
            productInfoSpec: product.productInfoSpec,
            totalPackage: product.totalPackage
          }
          return item
        }),
        address: data.consigneeAddress,
        mobile: data.consigneeMobile,
        name: data.consigneeName,
        deliveryCode: data.deliveryCode,
        deliveryName: data.deliveryName,
        deliveryNo: data.deliveryNo,
        deliveryType: data.deliveryType,
        remark: data.remark
      }
      checkCountDown()
      break
    }
    default:
      navigateBack()
  }
}
let unmounted = false
const navigateBack = () => {
  if (unmounted) return
  uni.$emit("refresh-order-list")
  safeNavigateBack()
}
onUnmounted(() => (unmounted = true))
onHide(() => (unmounted = true))

const pageStore = $store.page()
onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.orderno) {
    if (query?.transaction_id) {
      transactionId.value = query.transaction_id
    } else {
      safeNavigateBack()
    }
  } else if (query?.customer && !checkToken()) {
    uni.redirectTo({
      url: `/pages/customer/customer-service?source=${query.customer}&number=${query.orderno}`
    })
  } else {
    pageStore.getWXTemplateId()
    orderNo.value = query.orderno
  }
})
onShow(() => {
  if (orderNo.value) {
    getOrderDetails()
  } else if (transactionId.value) {
    getOrderDetailsByTransactionId()
  }
})

// const customerService = () => {
//   if (!orderNo.value) return
//   uni.openCustomerServiceChat({
//     extInfo: { url: $api.config.customerUrl },
//     corpId: $api.config.corpId,
//     showMessageCard: true,
//     sendMessageTitle: "订单详情",
//     sendMessagePath: `/package-order/order/order-details.html?orderno=${orderNo.value}&customer=2`,
//     sendMessageImg: "/static/image/service-logo.png",
//     fail: (err) => console.log(err)
//   })
// }

const submitPay = async () => {
  if (!order.value) return
  const orderNo = order.value.orderNo
  await pageStore.requestSubscribeMessage()
  uni.showLoading({ title: "提交中", mask: true })
  const result = await payOrder(orderNo)
  uni.hideLoading()
  if (result) {
    uni.navigateTo({
      url: `/package-order/order/pay-result?orderno=${orderNo}`
    })
  } else {
    getOrderDetails()
  }
}

const openWaybill = async () => {
  const response = await $api.getOrderFollowWaybill({
    orderNo: order.value?.orderNo as string
  })
  switch (response.__error) {
    case undefined: {
      const waybillToken = response.data?.waybillToken
      if (waybillToken) {
        const plugin = requirePlugin("logisticsPlugin")
        plugin.openWaybillTracking({
          waybillToken
        })
      } else {
        uni.showModal({
          title: "查看物流状态失败",
          content: "获取物流单信息时发生了错误",
          showCancel: false
        })
      }
      break
    }
    default:
      uni.showModal({
        title: "查看物流状态失败",
        content: "获取物流单信息时发生了错误",
        showCancel: false
      })
  }
}
const deleteOrderHandle = async () => {
  const result = await deleteOrder(orderNo.value)
  if (result) {
    await uni.showModal({ title: "删除成功", showCancel: false })
    safeNavigateBack()
  }
}

const cancelOrderHandle = async () => {
  const result = await cancelOrder(orderNo.value)
  if (result) {
    uni.showToast({ title: "取消成功", icon: "none" })
    getOrderDetails()
  }
}

const receiveHandle = async () => {
  const result = await receive(orderNo.value, order.value?.payTransactionId)
  if (result) {
    uni.showToast({ title: "收货成功", icon: "none" })
    getOrderDetails()
  }
}

const navigateToRefundPage = () => {
  if (!order.value) return
  uni.navigateTo({
    url: `/package-order/order/refund-page?orderno=${order.value.orderNo}`
  })
}

const navigateToProduct = (product: ProductOrderItem) => {
  uni.navigateTo({
    url: `/package-product/product/product-details?productid=${product.id}`
  })
}

const navigateToSelectProduct = () => {
  if (!order.value) return
  uni.navigateTo({
    url: `/package-aftersales/aftersales/aftersales-select-type?orderno=${order.value.orderNo}`
  })
}

const copyOrderNo = () => {
  if (!order.value) return
  uni.setClipboardData({
    data: order.value.orderNo,
    success: () => {
      uni.showToast({ title: "复制成功", icon: "none" })
    },
    fail: (err) => {
      console.log({ err })
      uni.showToast({ title: "复制失败", icon: "none" })
    }
  })
}

import { webName } from '@/apis/config' 

const logoSrc = computed(() => {
  if (webName === 'cheerray') {
    return "/static/image/service-logo-cheerray.png"
  } else {
    return "/static/image/service-logo.png"
  }
})
</script>

<style lang="scss" scoped>
.background-status {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--bar-height);
}
.status-pad {
  height: 88rpx;
  padding: 0 24rpx;
  transition: color 0.3s linear;
  .status-text {
    font-size: 40rpx;
    font-weight: bold;
  }
}
.status-list {
  height: 204rpx;
  margin-bottom: -52rpx;
  .status-info {
    height: 30rpx;
    padding: 0 24rpx 30rpx;
    transition: color 0.3s linear;
  }
}
.address-container {
  position: relative;
}
.address {
  padding: 10rpx 0;
  .address-name {
    margin-right: 10rpx;
  }
}
.address-info {
  padding-bottom: 10rpx;
}
.product-item {
  padding: 16rpx 0;
  .tag-container {
    height: 62rpx;
  }
  .product-count {
    padding: 10rpx 0;
  }
}
.order-info {
  padding: 16rpx 0;
  .order-label {
    width: 140rpx;
  }
}
.button-list-blank {
  height: 94rpx;
}
.button-list {
  position: fixed;
  left: 12rpx;
  right: 12rpx;
  bottom: 0;
  z-index: 10;
  .button-item {
    flex: 1;
    margin: 0 12rpx;
    max-width: calc(50% - 24rpx);
  }
}
.review {
  margin-right: 10rpx;
  color: #666;
}
</style>
