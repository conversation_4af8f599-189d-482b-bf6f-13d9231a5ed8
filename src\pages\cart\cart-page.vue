<template>
  <view>
    <view class="page-header">
      <v-navbar title="购物车" />
    </view>
    <cart-list ref="cartListRef" :page-height="pageHeight" @select="showSelect" />
    <view class="page-footer">
      <v-tabbar :index="2" />
    </view>
    <v-product-category ref="selectRef" @refresh="refresh" />
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import CartList from "./modules/cart-list.vue"

const { pageHeight } = usePageLayout()

const cartListRef = ref()
const refresh = () => {
  cartListRef.value.refresh()
}

/** 选择商品 */
const selectRef = ref()
const showSelect = (id: string, number: number) => {
  selectRef.value.showSelect({ id, number })
}
</script>

<style lang="scss" scoped></style>
