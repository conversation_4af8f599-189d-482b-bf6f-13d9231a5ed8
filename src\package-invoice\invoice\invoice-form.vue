<template>
  <view>
    <view class="page-header">
      <v-navbar title="开票申请" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight - 14}px` }"
      enable-back-to-top
    >
      <view class="headline">
        开票信息
      </view>
      <view class="v-block plain">
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            开票金额
          </view>
          <view class="flex-center flex-1 price-color">
            {{ countInvoicePrice() }}
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            发票类型
          </view>
          <view class="flex-1">
            <view
              class="flex-center"
              style="margin-bottom: 39rpx"
              @click="invoicingMsg.invoiceType = 0"
            >
              <v-checkbox
                :checked="invoicingMsg.invoiceType === 0"
              />&nbsp;电子普通发票
            </view>
            <view class="flex-center" @click="selectedMajorInvoice">
              <v-checkbox
                :checked="invoicingMsg.invoiceType === 1"
              />&nbsp;增值税电子专用发票
            </view>
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            抬头类型
          </view>
          <view class="flex-center flex-1">
            <view
              class="flex-center"
              @click="invoicingMsg.invoiceHeaderType = 0"
            >
              <v-checkbox
                :checked="invoicingMsg.invoiceHeaderType === 0"
              />&nbsp;企业
            </view>
            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            <view
              v-if="invoicingMsg.invoiceType === 0"
              class="flex-center"
              @click="invoicingMsg.invoiceHeaderType = 1"
            >
              <v-checkbox
                :checked="invoicingMsg.invoiceHeaderType === 1"
              />&nbsp;个人
            </view>
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view
            class="text-bold label"
            :class="{ required: invoicingMsg.invoiceHeaderType !== 1 }"
          >
            发票抬头
          </view>
          <view class="flex-center flex-1" style="position: relative">
            <input
              v-if="invoicingMsg.invoiceHeaderType !== 1"
              v-model="invoicingMsg.invoiceHeader"
              class="flex-1"
              placeholder="请选择或输入发票抬头(必填)"
              placeholder-class="placeholder"
            >
            <view v-else>
              个人
            </view>
            <view
              v-if="invoicingMsg.invoiceHeaderType !== 1"
              class="invoicing-icon"
              @click="goToInoviceMsgManagement"
            >
              <v-icon size="31rpx" src="/static/icons/invoice/invoicing.png" />
              发票抬头
            </view>
          </view>
        </view>
        <view v-if="invoicingMsg.invoiceHeaderType !== 1" class="divider" />
        <view
          class="v-form-item flex-center"
          :style="`display:${
            invoicingMsg.invoiceHeaderType === 1 ? 'none' : 'flex'
          };`"
        >
          <view class="text-bold required label">
            税&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceDutyParagraph"
              class="flex-1"
              placeholder="请输入税号(必填)"
              placeholder-class="placeholder"
            >
          </view>
        </view>
        <view v-if="invoicingMsg.invoiceHeaderType !== 1" class="divider" />
        <view
          class="auto-height-box"
          :style="`height:${isShowHidingContent ? 347.12 : 0}rpx;display:${
            invoicingMsg.invoiceHeaderType === 1 ? 'none' : 'block'
          };`"
        >
          <view class="v-form-item flex-center">
            <view class="text-bold label">
              公司地址
            </view>
            <view class="flex-center flex-1">
              <input
                v-model="invoicingMsg.invoiceCompanyAddress"
                class="flex-1"
                placeholder="请输入公司地址(非必填)"
                placeholder-class="placeholder"
              >
            </view>
          </view>
          <view class="divider" />
          <view class="v-form-item flex-center">
            <view class="text-bold label">
              公司电话
            </view>
            <view class="flex-center flex-1">
              <input
                v-model="invoicingMsg.invoiceCompanyTel"
                class="flex-1"
                placeholder="请输入公司电话(非必填)"
                placeholder-class="placeholder"
              >
            </view>
          </view>
          <view class="divider" />
          <!-- <view class="v-form-item flex-center">
            <view class="text-bold label">
              开户银行
            </view>
            <view class="flex-center flex-1">
              <input
                v-model="invoicingMsg.invoiceBank"
                class="flex-1"
                placeholder="请输入开户银行(非必填)"
                placeholder-class="placeholder"
              >
            </view>
          </view>
          <view class="divider" />
          <view class="v-form-item flex-center">
            <view class="text-bold label">
              银行账号
            </view>
            <view class="flex-center flex-1">
              <input
                v-model="invoicingMsg.invoiceBankAccount"
                class="flex-1"
                placeholder="请输入银行账号(非必填)"
                placeholder-class="placeholder"
              >
            </view>
          </view>
          <view class="divider" /> -->
        </view>
        <view
          v-if="invoicingMsg.invoiceHeaderType !== 1"
          class="show-all-btn"
          @click="switchHidingContentHandle"
        >
          <view v-if="!isShowHidingContent" class="flex-center">
            展开内容
            <v-icon size="20rpx" src="/static/icons/invoice/arrow-down.png" />
          </view>
          <view v-else class="flex-center">
            收起内容
            <v-icon size="20rpx" src="/static/icons/invoice/arrow-up.png" />
          </view>
        </view>
      </view>
      <view class="v-block plain" style="margin: 0">
        <view class="v-form-item flex-center">
          <view class="text-bold required label">
            电子邮箱
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceEmail"
              class="flex-1"
              placeholder="请输入发票接收邮箱地址"
              placeholder-class="placeholder"
            >
          </view>
        </view>
      </view>
      <view class="headline flex-center-between" style="height: 90rpx">
        订单信息
        <view class="notice flex-center">
          共<view class="price-color">
            {{
              pageStore.notIssueInvoiceList.filter((item) => item.selected)
                .length
            }}
          </view>个订单 &nbsp;&nbsp;<view class="price-color">
            1
          </view>张发票
        </view>
      </view>

      <view
        v-for="order in pageStore.notIssueInvoiceList"
        :key="order.orderId"
      >
        <view v-if="order.selected" class="v-block order-block">
          <view class="order-id flex-center-between">
            <view class="text-light text-24 flex-center">
              {{ `订单编号:${order.orderNo}` }}
              <view
                class="copy-style text-20"
                style="color: #005bac"
                @click="copyOrderNo(order.orderNo)"
              >
                复制
              </view>
            </view>
            <view class="date text-light text-24">
              {{ formateUtc(order.orderTime, "YYYY-MM-dd") }}
            </view>
          </view>
          <view class="divider order-info-margin-adjust" />
          <view class="order-info flex-center-between">
            <view class="order-headline">
              {{ order.productList[0].name }}
            </view>
            <view class="right-box-style">
              <view class="price">
                ￥{{ order.actualPayPrice }}
              </view>
              <view class="product-sum">
                共{{ countProductNum(order.productList) }}件
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="submit-button-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        type="primary"
        height="94rpx"
        font-size="32rpx"
        @click="submit"
      >
        提交
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"

const { pageHeight } = usePageLayout()

const pageStore = $store.page()
const isShowHidingContent = ref(false)
const invoicingMsg = ref<{
  invoiceBank: string
  // 开户银行
  invoiceBankAccount: string
  // 银行账号
  invoiceCompanyAddress: string
  // 公司地址
  invoiceCompanyTel: string
  // 公司电话
  invoiceDutyParagraph: string
  // 税号
  invoiceEmail: string
  // 电子邮箱
  invoiceHeader: string
  // 发票抬头
  invoiceHeaderType: number
  // 抬头类型，0-企业
  invoiceType: number
  // 发票类型，1->增值税电子专用发票
  orderNoDTOList: {
    orderNo: string
  }[]
}>({
  invoiceBank: "",
  // 开户银行
  invoiceBankAccount: "",
  // 银行账号
  invoiceCompanyAddress: "",
  // 公司地址
  invoiceCompanyTel: "",
  // 公司电话
  invoiceDutyParagraph: "",
  // 税号
  invoiceEmail: "",
  // 电子邮箱
  invoiceHeader: "",
  // 发票抬头
  invoiceHeaderType: 0,
  // 抬头类型，0-企业
  invoiceType: 0,
  // 发票类型，1->增值税电子专用发票
  orderNoDTOList: []
})
const selectedMajorInvoice = () => {
  invoicingMsg.value.invoiceType = 1
  invoicingMsg.value.invoiceHeaderType = 0
}

const goToInoviceMsgManagement = () => {
  uni.navigateTo({
    url: "/package-invoice/invoice/invoice-msg-management?select=1"
  })
}
const switchHidingContentHandle = () => {
  isShowHidingContent.value = !isShowHidingContent.value
}
const countInvoicePrice = () => {
  let count = 0
  pageStore.notIssueInvoiceList.forEach((item) => {
    if (item.selected) {
      count += item.actualPayPrice
    }
  })
  return count.toFixed(2)
}
const countProductNum = (product: ProductOrderItem[]) => {
  let count = 0
  product.forEach((item) => {
    count += item.number
  })
  return count
}
const validateMajorInvoiceByBusiness = () => {
  switch (true) {
    case !invoicingMsg.value.invoiceHeader:
      uni.showToast({ title: "请输入发票抬头", icon: "none" })
      return false
    case !/^(?:(?![-·()（）_, \u3000]{2})[-\u4e00-\u9fa5a-zA-Z0-9·()（）_& /,\u3000]){1,70}$/.test(invoicingMsg.value.invoiceHeader):
      uni.showToast({
        title: "发票抬头格式不正确或长度超过70个字符",
        icon: "none"
      })
      return false
    case !invoicingMsg.value.invoiceDutyParagraph:
      uni.showToast({ title: "请输入税号", icon: "none" })
      return false
    case !/^[0-9a-zA-Z]{10,20}$/.test(invoicingMsg.value.invoiceDutyParagraph):
      uni.showToast({
        title: "税号仅限英文字母与数字，长度限制在10-20以内",
        icon: "none"
      })
      return false
    // case !invoicingMsg.value.invoiceCompanyAddress:
    //   uni.showToast({
    //     title: "请输入公司地址",
    //     icon: "none"
    //   })
    //   return false
    case invoicingMsg.value.invoiceCompanyAddress &&
      invoicingMsg.value.invoiceCompanyAddress.trim().length > 40:
      uni.showToast({
        title: "公司地址长度限制在40个字符以内",
        icon: "none"
      })
      return false
    // case !invoicingMsg.value.invoiceCompanyTel:
    //   uni.showToast({
    //     title: "请输入公司电话",
    //     icon: "none"
    //   })
    //   return false
    case invoicingMsg.value.invoiceCompanyTel &&
      !/^[0-9`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'’，。、]{0,13}$/.test(
        invoicingMsg.value.invoiceCompanyTel
      ):
      uni.showToast({
        title: "公司电话仅限特殊字符与数字，长度限制在13位以内",
        icon: "none"
      })
      return false
    // case !invoicingMsg.value.invoiceBank:
    //   uni.showToast({
    //     title: "请输入开户银行",
    //     icon: "none"
    //   })
    //   return false
    // case invoicingMsg.value.invoiceBank &&
    //   !/^[\u4E00-\u9FA5]{0,20}$/.test(invoicingMsg.value.invoiceBank):
    //   uni.showToast({
    //     title: "开户银行仅限中文，长度限制在1-20以内",
    //     icon: "none"
    //   })
    //   return false
    // case !invoicingMsg.value.invoiceBankAccount:
    //   uni.showToast({
    //     title: "请输入银行账号",
    //     icon: "none"
    //   })
    //   return false
    // case invoicingMsg.value.invoiceBankAccount &&
    //   invoicingMsg.value.invoiceBankAccount.trim().length > 40:
    //   uni.showToast({
    //     title: "银行账号长度限制在40个字符以内",
    //     icon: "none"
    //   })
    //   return false
    case !invoicingMsg.value.invoiceEmail:
      uni.showToast({
        title: "请输入电子邮箱",
        icon: "none"
      })
      return false
    case !/^[0-9A-Za-z`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'’，。、]{1,50}$/.test(
      invoicingMsg.value.invoiceEmail
    ):
      uni.showToast({
        title: "电子邮箱仅限数字、英文字母、特殊字符，长度限制在1-50以内",
        icon: "none"
      })
      return false
    default:
      return true
  }
}
const validateInvoiceByBusiness = () => {
  switch (true) {
    case !invoicingMsg.value.invoiceHeader:
      uni.showToast({ title: "请输入发票抬头", icon: "none" })
      return false
    case !/^(?:(?![-·()（）_, \u3000]{2})[-\u4e00-\u9fa5a-zA-Z0-9·()（）_& /,\u3000]){1,70}$/.test(invoicingMsg.value.invoiceHeader):
      uni.showToast({
        title: "发票抬头格式不正确或长度超过70个字符",
        icon: "none"
      })
      return false
    case !invoicingMsg.value.invoiceDutyParagraph:
      uni.showToast({ title: "请输入税号", icon: "none" })
      return false
    case !/^[0-9a-zA-Z]{10,20}$/.test(invoicingMsg.value.invoiceDutyParagraph):
      uni.showToast({
        title: "税号仅限英文字母与数字，长度限制在10-20以内",
        icon: "none"
      })
      return false
    case invoicingMsg.value.invoiceCompanyAddress.trim().length > 40:
      uni.showToast({
        title: "公司地址长度限制在40个字符以内",
        icon: "none"
      })
      return false
    case !/^[0-9`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'’，。、]{0,13}$/.test(
      invoicingMsg.value.invoiceCompanyTel
    ):
      uni.showToast({
        title: "公司电话仅限特殊字符与数字，长度限制在13位以内",
        icon: "none"
      })
      return false
    // case !/^[\u4E00-\u9FA5]{0,20}$/.test(invoicingMsg.value.invoiceBank):
    //   uni.showToast({
    //     title: "开户银行仅限中文，长度限制在1-20以内",
    //     icon: "none"
    //   })
    //   return false
    // case invoicingMsg.value.invoiceBankAccount.trim().length > 40:
    //   uni.showToast({
    //     title: "银行账号长度限制在40个字符以内",
    //     icon: "none"
    //   })
    //   return false
    case !invoicingMsg.value.invoiceEmail:
      uni.showToast({
        title: "请输入电子邮箱",
        icon: "none"
      })
      return false
    case !/^[0-9A-Za-z`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'’，。、]{1,50}$/.test(
      invoicingMsg.value.invoiceEmail
    ):
      uni.showToast({
        title: "电子邮箱仅限数字、英文字母、特殊字符，长度限制在1-50以内",
        icon: "none"
      })
      return false
    default:
      return true
  }
}
const validateInvoiceByPerson = () => {
  switch (true) {
    case !invoicingMsg.value.invoiceEmail:
      uni.showToast({
        title: "请输入电子邮箱",
        icon: "none"
      })
      return false
    case !/^[0-9A-Za-z`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'’，。、]{1,50}$/.test(
      invoicingMsg.value.invoiceEmail
    ):
      uni.showToast({
        title: "电子邮箱仅限数字、英文字母、特殊字符，长度限制在1-50以内",
        icon: "none"
      })
      return false
    default:
      return true
  }
}

const submit = async () => {
  if (
    invoicingMsg.value.invoiceType === 0 &&
    invoicingMsg.value.invoiceHeaderType === 0
  ) {
    if (!validateInvoiceByBusiness()) return
    uni.showModal({
      content: "请确认开票信息准确无误 提交申请后无法修改",
      confirmText: "确认", // 这块是确定按钮的文字
      cancelText: "再想想", // 这块是取消的文字
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({ title: "提交中", mask: true })
          const response = await $api.insertOrderCommonInvoiceByBusiness({
            ...invoicingMsg.value
          })
          uni.hideLoading()
          switch (response.__error) {
            case undefined:
              uni.redirectTo({
                url: `/package-invoice/invoice/invoice-detail?invoiceno=${response.data.invoiceNo}`
              })
          }
        }
      }
    })
  }
  if (
    invoicingMsg.value.invoiceType === 0 &&
    invoicingMsg.value.invoiceHeaderType === 1
  ) {
    if (!validateInvoiceByPerson()) return
    uni.showModal({
      content: "请确认开票信息准确无误 提交申请后无法修改",
      confirmText: "确认", // 这块是确定按钮的文字
      cancelText: "再想想", // 这块是取消的文字
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({ title: "提交中", mask: true })
          const response = await $api.insertOrderCommonInvoiceByPerson({
            invoiceEmail: invoicingMsg.value.invoiceEmail,
            invoiceHeader: "个人",
            invoiceHeaderType: invoicingMsg.value.invoiceHeaderType,
            invoiceType: invoicingMsg.value.invoiceType,
            orderNoDTOList: invoicingMsg.value.orderNoDTOList
          })
          uni.hideLoading()
          switch (response.__error) {
            case undefined:
              uni.redirectTo({
                url: `/package-invoice/invoice/invoice-detail?invoiceno=${response.data.invoiceNo}`
              })
          }
        }
      }
    })
  }
  if (invoicingMsg.value.invoiceType === 1) {
    if (!validateMajorInvoiceByBusiness()) return
    uni.showModal({
      content: "请确认开票信息准确无误 提交申请后无法修改",
      confirmText: "确认", // 这块是确定按钮的文字
      cancelText: "再想想", // 这块是取消的文字
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({ title: "提交中", mask: true })
          const response = await $api.insertOrderMajorInvoiceByBusiness({
            ...invoicingMsg.value
          })
          uni.hideLoading()
          switch (response.__error) {
            case undefined:
              uni.redirectTo({
                url: `/package-invoice/invoice/invoice-detail?invoiceno=${response.data.invoiceNo}`
              })
          }
        }
      }
    })
  }
  pageStore.setInvoiceHeader(undefined)
}
const copyOrderNo = (number: string) => {
  uni.setClipboardData({
    data: number,
    success: () => {
      uni.showToast({ title: "复制成功", icon: "none" })
    },
    fail: (err) => {
      console.log({ err })
      uni.showToast({ title: "复制失败", icon: "none" })
    }
  })
}
onShow(() => {
  invoicingMsg.value.invoiceBank = pageStore.invoiceHeader?.invoiceBank || ""
  invoicingMsg.value.invoiceBankAccount =
    pageStore.invoiceHeader?.invoiceBankAccount || ""
  invoicingMsg.value.invoiceCompanyAddress =
    pageStore.invoiceHeader?.invoiceCompanyAddress || ""
  invoicingMsg.value.invoiceCompanyTel =
    pageStore.invoiceHeader?.invoiceCompanyTel || ""
  invoicingMsg.value.invoiceDutyParagraph =
    pageStore.invoiceHeader?.invoiceDutyParagraph || ""
  invoicingMsg.value.invoiceEmail =
    pageStore.invoiceHeader?.invoiceEmail || ""
  invoicingMsg.value.invoiceHeader =
    pageStore.invoiceHeader?.invoiceHeader || ""
  invoicingMsg.value.orderNoDTOList = pageStore.notIssueInvoiceList
    .filter((item) => item.selected)
    .map((item) => ({ orderNo: item.orderNo }))
  pageStore.setInvoiceHeader(undefined)
})
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0 0 24rpx;
}
.order-block {
  margin: 0 24rpx 24rpx;
}
.v-block-product {
  margin: 0 24rpx 24rpx;
}
.v-block.plain {
  padding: 8rpx 24rpx;
}
.package-title {
  padding-top: 20rpx;
}
.package-info-title {
  padding: 20rpx 0;
}
.product-title {
  padding-top: 20rpx;
}
.product-number {
  padding: 20rpx 0;
}
.submit-button-blank {
  height: 94rpx;
}
.submit-button {
  padding-top: 28rpx !important;
  position: fixed;
  padding: 0 24rpx;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
}
.tooltip-container {
  position: relative;
}
.tooltip {
  position: absolute;
  top: 0;
  right: 50%;
  height: fit-content;
  width: fit-content;
  transform: translateY(-100%);
  .text {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 20%;
    left: 0;
    color: white;
    font-size: 24rpx;
  }
}

.red {
  color: #e60012;
}
.aftersales-select-handle {
  position: fixed;
  left: 24rpx;
  right: 24rpx;
  bottom: 0;
  z-index: 10;
  .next-btn {
    display: flex;
    margin-top: 32rpx;
    justify-content: center;
    align-items: center;
    height: 94rpx;
    font-size: 32rpx;
    border-radius: 47rpx 47rpx;
    background-color: #005bac;
    color: #fff;
  }
}
.spec-style {
  color: #999999;
  font-size: 24rpx;
}
.count-bottom-style {
  color: #666666;
  font-size: 24rpx;
}
.handle-select {
  display: flex;
  justify-content: space-between;
  .selected-product-notice {
    font-size: 24rpx;
    color: #666;
  }
  .selected-all-style {
    font-size: 24rpx;
    color: #666;
  }
}
.handle-count {
  margin-top: 14rpx;
}
.price-notice {
  color: #999999;
  font-size: 22rpx;
}
.product-info {
  margin: 0 24rpx 24rpx;
}
.back-info-style {
  margin: 7rpx 0;
}
.delivery-hendle-margin {
  margin-bottom: 20rpx;
}
.order-no {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}
.order-headline-margin-adjust {
  margin-bottom: 12rpx;
}
.headline {
  display: flex;
  align-items: center;
  color: #999999;
  font-size: 24rpx;
  padding: 0 24rpx;
  height: 70rpx;
}
.price-color {
  font-weight: 800;
  color: #e60012;
}
.show-all-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 82rpx;
  color: #666;
  font-size: 20rpx;
}
.notice {
  font-size: 28rpx;
  color: #666;
}
.copy-style {
  border-radius: 10rpx;
  border: 1rpx solid #005bac;
  margin-left: 10rpx;
  padding: 0 6rpx;
}
.order-id {
  height: 86rpx;
}
.right-box-style {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}
.order-headline {
  font-weight: 800;
  color: #231815;
  font-size: 28rpx;
}
.order-info {
  margin-bottom: 31rpx;
}
.order-info-margin-adjust {
  margin-bottom: 31rpx;
}
.price {
  color: #231815;
  font-weight: 800;
}
.product-sum {
  color: #999999;
}
.date {
  color: #999;
}
.auto-height-box {
  overflow: hidden;
  transition: 0.2s all;
}
.invoicing-icon {
  z-index: 1000;
  position: absolute;
  right: 0;
  top: -6rpx;
  color: #666666;
  font-size: 18rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
