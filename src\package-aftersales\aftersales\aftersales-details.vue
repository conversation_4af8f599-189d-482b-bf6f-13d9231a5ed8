view
<template>
  <view>
    <view class="page-header">
      <v-navbar title="退货退款" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <v-loading-block v-if="!aftersales" />
      <template v-else>
        <view class="v-block">
          <view class="info-item info-title text-bold">
            {{ status }}
          </view>
          <view class="info-item-content text-sub">
            {{ statusInfo }}
          </view>
          <view v-if="aftersales.aftersalesStatus === 1" class="accept">
            <view class="info-label text-bold text-32 headline">
              寄回信息
            </view>
            <view class="flex-center-between back-info-style">
              <view class="text-light">
                收件人：{{ aftersales.backName }}
              </view>
              <view class="text-light">
                电话：{{ aftersales.backMobile }}
              </view>
            </view>
            <view class="text-light back-info-style delivery-hendle-margin">
              <view class="flex">
                <view class="text-1-row" style="margin-right: 10rpx">
                  地址：
                </view>
                <view class="flex-1" style="word-break: break-all">
                  {{ aftersales.backAddress }}
                </view>
              </view>
            </view>
            <v-button
              type="primary"
              height="94rpx"
              margin="30rpx 0 20rpx"
              @click="navigateToDeliveryBill"
            >
              输入发货单
            </v-button>
          </view>
          <view
            v-if="
              aftersales.aftersalesStatus !== 8 &&
                aftersales.aftersalesStatus >= 3
            "
            class="confirm-delivery"
          >
            <view class="info-label text-bold text-32 headline">
              退货信息
            </view>
            <view class="text-light back-info-style">
              退回物流公司：{{ aftersales.deliveryName }}
            </view>
            <view class="text-light back-info-style">
              退回物流单号：{{ aftersales.deliveryNo }}
            </view>
          </view>
        </view>
        <view class="v-block">
          <view class="info-item text-bold">
            退款流程
          </view>
          <v-timeline :data="timeLine" />
        </view>
        <view class="text-bold product-info">
          商品信息
        </view>
        <view
          v-for="product in aftersales.productList"
          :key="product.id"
          class="v-block"
          :product="product"
          size="small"
        >
          <view class="order-no">
            订单编号：{{ aftersales.orderNo }}
          </view>
          <view class="divider order-headline-margin-adjust" />
          <view class="product-item">
            <v-product-item :product="product">
              <template #name>
                {{ product.name }}
              </template>
              <template #spec>
                {{ product.productInfoSpec }}
              </template>
              <template #default>
                <view class="text-light text-24">
                  {{ `装箱规格: ${product.displayAmount}pcs/箱` }}
                </view>
                <view class="text-sub text-24">
                  {{ `订购数量: ${product.number}箱` }}
                </view>
              </template>
            </v-product-item>
            <view class="divider handle-count" />
            <view class="v-form-item flex-center-between">
              <view class="text-bold label">
                申请数量(箱)
              </view>
              <view>{{ product.returnNumber }}箱</view>
            </view>
          </view>
        </view>

        <view class="v-block">
          <view class="v-form-item flex-center-between">
            <view class="text-bold label">
              总申请数量
            </view>
            <view class="text-32 text-bold red">
              {{ countProductNumber }}箱
            </view>
          </view>
          <view class="divider" />
          <view class="v-form-item flex-center-between">
            <view class="text-bold label">
              退货原因
            </view>
            <view class="text-32">
              {{ aftersales.aftersalesReason }}
            </view>
          </view>
        </view>

        <view class="v-block">
          <view class="v-form-item flex-center-between">
            <view class="text-bold label">
              预计退款金额
            </view>
            <v-price :price="aftersales.applyBackPrice" size="32rpx" />
          </view>
          <view class="price-notice">
            以最终到账金额为准
          </view>
        </view>
      </template>
      <view class="button-list-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view v-if="aftersales" class="button-list">
      <view class="flex-center-end">
        <view
          v-if="[0, 1, 2].includes(aftersales.aftersalesStatus)"
          class="button-item"
        >
          <!-- <v-button
            height="94rpx"
            background-color="white"
            @click="customerService"
          >
            <view class="flex-center">
              <v-icon
                size="34rpx"
                src="/static/icons/order/customer-service-dark.png"
              />
              <view style="margin-left: 10rpx">
                客服
              </view>
            </view>
          </v-button> -->
          <v-button
            height="94rpx"
            background-color="white"
            open-type="contact"
            send-message-title="售后详情"
            :send-message-path="`/package-aftersales/aftersales/aftersales-details.html?id=${aftersalesId}&number=${aftersales.aftersalesNo}&customer=6`"
            :send-message-img="logoSrc"
            show-message-card
          >
            <view class="flex-center">
              <v-icon
                size="34rpx"
                src="/static/icons/order/customer-service-dark.png"
              />
              <view style="margin-left: 10rpx">
                客服
              </view>
            </view>
          </v-button>
        </view>
        <view
          v-if="[0, 1].includes(aftersales.aftersalesStatus)"
          class="button-item"
        >
          <v-button
            height="94rpx"
            background-color="white"
            @click="cancelAftersales"
          >
            取消售后
          </v-button>
        </view>
      </view>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import type { TimeLine } from "@/components/v-timeline/types"
import { str2num } from "@/utils/type-transfer"
import { checkToken } from "@/utils/auth/token"

const { pageHeight } = usePageLayout()

const aftersalesId = ref("")
const aftersales = ref<AftersalesDetails>()
const countProductNumber = computed(() => {
  if (!aftersales.value?.productList) return 0
  let count = 0
  for (let i = 0; i < aftersales.value.productList.length; i += 1) {
    count += aftersales.value.productList[i]?.returnNumber ?? 0
  }
  return count
})

const status = computed(() => {
  if (!aftersales.value) return ""
  switch (aftersales.value.aftersalesStatus) {
    case 0:
      return "已提交"
    case 1:
      return "审核通过,请寄回产品"
    case 2:
      return "审核驳回"
    case 3:
      return "商品已寄回"
    case 4:
    case 5:
    case 6:
    case 7:
      return "退款中"
    case 8:
      return "已取消"
    case 9:
      return "退款已完成"
    default:
      return "未知状态"
  }
})

const statusInfo = computed(() => {
  if (!aftersales.value) return ""
  switch (aftersales.value.aftersalesStatus) {
    case 0:
      return "请耐心等待审核结果。"
    case 1:
      return "请将产品寄回，并输入寄回快递单号"
    case 2:
      return "审核不通过"
    case 3:
      return "请耐心等待仓库签收"
    case 4:
    case 6:
    case 7:
      return "已收到您的退回商品，请等待退款审核"
    case 5:
      return "退款将在1~3个工作日退回您的支付账户。"
    case 8:
      return "用户主动取消"
    default: {
      const price = `￥${aftersales.value.refundAmount.toFixed(2)}`
      return `${price}退款金额已退还至您的支付账户`
    }
  }
})

const timeLine = computed<TimeLine>(() => {
  const list = [] as TimeLine
  if (!aftersales.value) return list
  const data = aftersales.value
  const status = data.aftersalesStatus
  if (data.aftersalesTime) {
    list.push({
      title: "申请已提交",
      content: "申请已提交，请等待客服审核。",
      time: data.aftersalesTime
    })
  }
  /** 已提交 */
  if (status === 0) return list

  /** 审核不通过 */
  if (status === 2) {
    list.push({
      title: "审核不通过",
      content: "审核不通过，请联系在线客服。",
      time: data.afterSaleOperateDate
    })
    return list
  }
  /** 处理过但是通过了 */
  if (data.afterSaleOperateDate) {
    /** 审核通过 */
    list.push({
      title: "审核通过",
      content: "审核通过，请寄回退货产品。",
      time: data.afterSaleOperateDate
    })
  }
  /** 用户取消 */
  if (status === 8) {
    list.push({
      title: "售后已取消",
      content: "用户主动取消售后",
      time: data.cancelTime
    })
    return list
  }
  /** 填写物流信息 */
  list.push({
    title: "填写物流信息",
    content: "请填写产品寄回快递信息。",
    time: data.deliveryTime
  })
  /** 仅到产品寄回 */
  // if (status === "11") return list

  /** 仓库回收 */
  if (status >= 3) {
    list.push({
      title: "商品已寄回",
      content: "请等待仓库签收您寄回的商品。",
      time: data.receivedTime
    })
  }
  if (status >= 4) {
    /** 退款中 */
    list.push({
      title: "退款中",
      content: "已收到您的退回商品，请等待退款审核",
      time: data.storehouseOperateDate
    })
  }
  if (status === 9) {
    const price = `${aftersales.value.refundAmount.toFixed(2)}`
    /** 退款成功 */
    list.push({
      title: "退款已完成",
      content: `￥${price}退款金额已退还至您的支付账户`,
      time: data.refundTime
    })
  }

  /** 仓库回收成功 */
  return list
})

const getAftersalesDetails = async () => {
  const response = await $api.findOrderRefund({
    orderBackId: aftersalesId.value
  })
  switch (response.__error) {
    case undefined: {
      const data = response.data
      aftersales.value = {
        aftersalesId: data.orderBackId,
        aftersalesNo: data.orderAfterSaleNo,
        aftersalesStatus: data.afterSaleStatus,
        aftersalesType: data.afterSaleType,
        deliveryNo: data.deliverySn,
        deliveryName: data.deliveryCompany,
        backAddress: data.returnGoodAddress,
        backMobile: data.returnGoodPhone,
        backName: data.returnGoodName,
        remark: data.backExplain,
        aftersalesTime: data.createdTime,
        deliveryTime: data.sendDeliveryDate,
        receivedTime: data.storehouseOperateDate,
        afterSaleOperateDate: data.afterSaleOperateDate,
        afterSaleRefundOperateDate: data.afterSaleRefundOperateDate,
        storehouseOperateDate: data.storehouseOperateDate,
        refundOperateDate: data.refundOperateDate,
        refundTime: data.refundDate,
        refundAmount: str2num(data.afterSaleApplyPrice),
        applyBackPrice: str2num(data.applyBackPrice),
        cancelTime: data.afterSaleCancelDate,
        aftersalesReason: data.backReason,
        backImgs: data.imgs,
        orderNo: data.orderNo,
        productList: data.orderProductRelationVOList.map((product) => {
          const item: ProductAftersalesItem = {
            id: product.productInfoId,
            image: product.productInfoProductImg,
            name: product.productInfoName,
            unitPrice: str2num(product.productInfoPrice),
            unitAmount: str2num(product.productInfoPackage),
            displayPrice: str2num(product.displayUnitPrice),
            displayAmount: product.displayPackage,
            productInfoSpec: product.productInfoSpec,
            number: str2num(product.quantity),
            returnNumber: product.memberReturnBoxNumber
          }
          return item
        })
      }
    }
  }
}

/** 取消售后单 */
const cancelAftersales = async () => {
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "提示",
      content: "是否确认取消该售后申请?",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return
  uni.showLoading({ title: "加载中", mask: true })
  const response = await $api.cancelOrderRefund({
    orderBackId: aftersalesId.value
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      uni.showToast({ title: "取消成功", mask: true })
      getAftersalesDetails()
  }
}

// const customerService = () => {
//   if (!aftersalesId.value) return
//   const number = aftersales.value?.aftersalesNo ?? ""
//   uni.openCustomerServiceChat({
//     extInfo: { url: $api.config.customerUrl },
//     corpId: $api.config.corpId,
//     showMessageCard: true,
//     sendMessageTitle: "售后详情",
//     sendMessagePath: `/package-aftersales/aftersales/aftersales-details.html?id=${aftersalesId.value}&number=${number}&customer=6`,
//     sendMessageImg: "/static/image/service-logo.png",
//     fail: (err) => console.log(err)
//   })
// }

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.id) {
    uni.switchTab({ url: "/pages/index/index-page" })
  } else if (query?.customer && !checkToken()) {
    const number = query.number || query.id
    uni.redirectTo({
      url: `/pages/customer/customer-service?source=${query.customer}&number=${number}`
    })
  } else {
    aftersalesId.value = query?.id
  }
})
onShow(() => {
  if (aftersalesId.value) getAftersalesDetails()
})

const pageStore = $store.page()
const navigateToDeliveryBill = () => {
  if (!aftersales.value) return
  pageStore.setAftersalesDelivery({
    orderNo: aftersales.value.orderNo,
    aftersalesNo: aftersales.value.aftersalesNo,
    aftersalesId: aftersales.value.aftersalesId,
    aftersalesTime: aftersales.value.aftersalesTime,
    deliveryName: aftersales.value.deliveryName,
    deliveryNo: aftersales.value.deliveryNo,
    productList: aftersales.value.productList
  })
  uni.navigateTo({
    url: "/package-aftersales/aftersales/aftersales-delivery-bill"
  })
}

// const navigateToProduct = (product: ProductAftersalesItem) => {
//   uni.navigateTo({
//     url: `/package-product/product/product-details?productid=${product.id}`
//   })
// }

import { webName } from '@/apis/config' 

const logoSrc = computed(() => {
  if (webName === 'cheerray') {
    return "/static/image/service-logo-cheerray.png"
  } else {
    return "/static/image/service-logo.png"
  }
})

</script>

<style lang="scss" scoped>
.info-title {
  font-size: 44rpx;
}
.info-item {
  padding: 16rpx 0;
}
.info-item-content {
  padding-bottom: 16rpx;
}
.insert-button {
  margin: 0 24rpx 24rpx;
}
.info-item-light {
  padding: 10rpx 0;
  .order-id {
    margin-right: 20rpx;
  }
  .info-label {
    width: 120rpx;
  }
}
.red {
  color: #e60012;
}
.aftersales-select-handle {
  position: fixed;
  left: 24rpx;
  right: 24rpx;
  bottom: 0;
  z-index: 10;
  .next-btn {
    display: flex;
    margin-top: 32rpx;
    justify-content: center;
    align-items: center;
    height: 94rpx;
    font-size: 32rpx;
    border-radius: 47rpx 47rpx;
    background-color: #005bac;
    color: #fff;
  }
}
.spec-style {
  color: #999999;
  font-size: 24rpx;
}
.count-bottom-style {
  color: #666666;
  font-size: 24rpx;
}
.handle-select {
  display: flex;
  justify-content: space-between;
  .selected-product-notice {
    font-size: 24rpx;
    color: #666;
  }
  .selected-all-style {
    font-size: 24rpx;
    color: #666;
  }
}
.handle-count {
  margin-top: 14rpx;
}
.price-notice {
  color: #999999;
  font-size: 22rpx;
  margin-bottom: 15rpx;
}
.product-info {
  margin: 0 24rpx 24rpx;
}
.back-info-style {
  margin: 7rpx 0;
}
.delivery-hendle-margin {
  margin-bottom: 20rpx;
}
.confirm-delivery {
  margin: 30rpx 0 0 0;
  .headline {
    margin-bottom: 15rpx;
  }
}
.accept {
  margin: 30rpx 0 0 0;
  .headline {
    margin-bottom: 15rpx;
  }
}
.order-no {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}
.order-headline-margin-adjust {
  margin-bottom: 12rpx;
}
.button-list {
  position: fixed;
  left: 12rpx;
  right: 12rpx;
  bottom: 0;
  z-index: 10;
  .button-item {
    flex: 1;
    margin: 0 12rpx;
    max-width: calc(50% - 24rpx);
  }
}
.button-list-blank {
  height: 94rpx;
}
</style>
