import { defineStore } from "pinia"
import { num2str, str2num } from "@/utils/type-transfer"
import { categoryNameToList } from "@/utils/data-transfer"

export default defineStore("page", () => {
  const address = ref<Address | undefined>()
  const deliveryAddress = ref("")
  const deliveryName = ref("")
  const deliveryTel = ref("")
  const enterpriseAbbr = ref("")
  const confirmOrder = ref<ConfirmOrder | undefined>()
  const isBuyImmediate = ref(false)

  const showCustomTabbar = ref(false)

  /** 设置地址 */
  const setAddress = (add: Address | undefined) => {
    address.value = add
  }

  /** 设置订单确认数据 */
  const setConfirmOrder = (
    token: string,
    address: string,
    name: string,
    tel: string,
    enterprise: string,
    sumPrice: string,
    productList: ProductCartItem[],
    buyImmediate?: boolean
  ) => {
    confirmOrder.value = { token, sumPrice, productList }
    deliveryAddress.value = address
    deliveryName.value = name
    deliveryTel.value = tel
    enterpriseAbbr.value = enterprise
    isBuyImmediate.value = buyImmediate === true
  }
  /** 刷新订单token */
  const refreshToken = async () => {
    const response = await $api.orderConfirmToken({})
    switch (response.__error) {
      case undefined:
        if (confirmOrder.value) {
          confirmOrder.value.token = response.data.orderToken
          enterpriseAbbr.value = response.data.enterpriseAbbr ?? ""
        }
    }
  }

  const hideTabBar = async () => {
    const list = getCurrentPages()
    const currentPage = list[list.length - 1]
    // 某些机型这里切出小程序后会自己恢复tabbar，原因不明
    if (
      currentPage.route &&
      [
        "pages/index/index-page",
        "pages/product/product-page",
        "pages/cart/cart-page",
        "pages/user/user-page"
      ].includes(currentPage.route)
    ) {
      showCustomTabbar.value = false
      await new Promise((resolve) => {
        uni.hideTabBar({
          success: () => {
            showCustomTabbar.value = true
          },
          fail: () => {
            console.warn("关闭tabbar失败")
          },
          complete: () => resolve(true)
        })
      })
    }
  }

  const addressList = ref<Address[]>([])
  const selectedAddressId = ref<string>()
  const defaultAddressId = ref<string>()

  /** 获取地址列表 */
  const getAddressList = async () => {
    const response = await $api.addressList({})
    switch (response.__error) {
      case undefined: {
        addressList.value = [...response.data.addressList]
        defaultAddressId.value = response.data.memberAddressDefault
      }
    }
  }

  /** 设置为默认地址 */
  const setDefaultAddress = async (address: Address) => {
    const response = await $api.setDefaultAddress({ addressId: address.id })
    switch (response.__error) {
      case undefined:
        addressList.value = [...response.data.addressList]
        defaultAddressId.value = response.data.memberAddressDefault
    }
  }

  /** 选择地址 */
  const selectAddress = (id: string) => {
    selectedAddressId.value = id
  }

  /** 选中默认地址 */
  const selectDefaultAddress = () => {
    selectedAddressId.value = defaultAddressId.value
  }

  /** 删除地址 */
  const deleteAddress = (id: string) => {
    const index = addressList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      addressList.value.splice(index, 1)
    }
  }

  /** 选中的地址 */
  const selectedAddress = computed(() => {
    const address = addressList.value.find(
      (item) => item.id === selectedAddressId.value
    )
    return address
  })

  /** 默认地址 */
  const defaultAddress = computed(() => {
    const address = addressList.value.find(
      (item) => item.id === defaultAddressId.value
    )
    return address
  })

  /** 公司列表 */
  const corporateList = ref<Corporate[]>([])
  /** 已选的公司 */
  const selectedCorporate = ref<string>()

  /** 获取公司列表 */
  const getCorporateList = async () => {
    const response = await $api.corporateList({})
    switch (response.__error) {
      case undefined:
        corporateList.value = response.data.corporateList
    }
  }

  const setCorporate = (corporate: string) => {
    selectedCorporate.value = corporate
  }

  /** 删除公司 */
  const deleteCorporate = (id: string) => {
    const index = corporateList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      corporateList.value.splice(index, 1)
    }
  }

  const afterSalesOrder = ref<AftersalesOrderDetails | undefined>()
  const afterSalesProduct = ref<AftersalesOrderProductItem | undefined>()
  const reasonList = ref([
    { label: "发错货" },
    { label: "不想要了" },
    { label: "拍错了" }
  ])

  /** 设置申请售后的订单 */
  const setAftersalesOrder = (order: AftersalesOrderDetails | undefined) => {
    afterSalesOrder.value = order
  }

  /** 设置申请售后的商品 */
  const setAftersalesProduct = (product: AftersalesOrderProductItem | undefined) => {
    afterSalesProduct.value = product
  }

  /** 售后物流信息 */
  const aftersalesDelivery = ref<AftersalesDelivery>()

  /** 设置售后物流信息 */
  const setAftersalesDelivery = (delivery: AftersalesDelivery) => {
    aftersalesDelivery.value = delivery
  }

  const selectedIdList = ref<string[]>([])
  const productList = ref<ProductCartItem[]>([])

  const storageKeyName = "cart_selected_list"
  /** 获取最新的缓存 */
  const getProductSelect = () => {
    const storage = uni.getStorageSync(storageKeyName)
    try {
      selectedIdList.value = storage ? JSON.parse(storage) : []
    } catch {
      selectedIdList.value = []
    }
  }

  /** 更新缓存 */
  const updateProductSelect = () => {
    uni.setStorageSync(storageKeyName, JSON.stringify(selectedIdList.value))
  }

  /** 添加选择商品 */
  const addProductSelect = (id: string) => {
    getProductSelect()
    if (!selectedIdList.value.some((item) => item === id)) {
      selectedIdList.value.push(id)
    }
    updateProductSelect()
  }

  /** 选择多个商品 */
  const selectProductList = (ids: string[]) => {
    getProductSelect()
    const list = [] as string[]
    for (let i = 0; i < ids.length; i += 1) {
      if (!list.some((item) => item === ids[i])) {
        list.push(ids[i])
      }
    }
    selectedIdList.value = list
    updateProductSelect()
  }

  /** 切换选择状态 */
  const toggleProductSelect = (id: string, value?: boolean) => {
    getProductSelect()
    const index = selectedIdList.value.findIndex((item) => item === id)
    if (index === -1 && value !== false) {
      selectedIdList.value.push(id)
    } else if (index !== -1 && value !== true) {
      selectedIdList.value.splice(index, 1)
    }
    updateProductSelect()
  }

  /** 切换全选状态 */
  const toggleSelectAll = () => {
    getProductSelect()
    // 有效商品列表
    const activeList = productList.value.filter((item) => {
      // 判断商品是否删除,如果删除不拦截
      if (item.isDeleted) return false
      // 判断商品是否上架,如果上架不拦截
      if (item.isOnSale !== true) return false
      // 判断商品是否还有库存,如果库存大于0不拦截
      if ((item.shelvesStock as number) <= 0) return false
      // 判断商品是否还有库存,如果库存小于商品数量不拦截
      if ((item.shelvesStock as number) < item.number) return false
      return true
    })
    const isSelectedAll = !activeList.some(
      (item) => !selectedIdList.value.includes(item.id)
    )
    if (isSelectedAll) {
      selectedIdList.value = []
    } else {
      selectedIdList.value = activeList.map((item) => item.id)
    }
    updateProductSelect()
  }

  /** 清除所有选择的商品 */
  const clearSelect = () => {
    selectedIdList.value = []
  }

  /** 更新购物车商品 */
  const refreshProductList = async () => {
    const response = await $api.getCartList({})
    switch (response.__error) {
      case undefined: {
        const list: ProductCartItem[] =
          response?.data?.itemVO?.map?.((item) => {
            const name = item.productSpecName || item.productInfoName
            const unitAmount = str2num(item.productInfoNcPackage)
            const stockNum = str2num(item.shelvesStock)
            const stockCount = isNaN(stockNum) ? 0 : stockNum
            return {
              id: item.productInfoId,
              image: item.productInfoProductImg,
              name,
              unitPrice: str2num(item.productInfoPrice),
              unitAmount,
              displayPrice: str2num(item.displayUnitPrice),
              displayAmount: item.displayPackage,
              isOnSale: item.isOnSale === "1",
              isDeleted: item.delFlag === "1",
              shelvesStock: Math.floor(Math.max(stockCount, 0) / unitAmount),
              number: str2num(item.quantity),
              productInfoSpec: item.productSpec,
              serverNumber: str2num(item.quantity),
              changingNumber: false
            }
          }) ?? []
        getProductSelect()
        productList.value = list
        for (let i = selectedIdList.value.length - 1; i >= 0; i -= 1) {
          const productItem = productList.value.find(
            (item) => item.id === selectedIdList.value[i]
          )
          // 剔除非购物车列表中的商品
          if (!productItem) {
            selectedIdList.value.splice(i, 1)
            continue
          }
          // 剔除库存为小于用户加购数量的商品
          if ((productItem.shelvesStock as number) < productItem.number) {
            selectedIdList.value.splice(i, 1)
          }
          // 剔除库存为0的商品
          if ((productItem.shelvesStock as number) <= 0) {
            selectedIdList.value.splice(i, 1)
          }
          // 剔除已下架的商品
          if (productItem.isOnSale === false) {
            selectedIdList.value.splice(i, 1)
          }
        }
        uni.setStorageSync(
          "cart_selected_list",
          JSON.stringify(selectedIdList.value)
        )
        updateProductSelect()
        return true
      }
      default:
        return false
    }
  }

  /** 更新商品数量 */
  const updateProductNumber = async (id: string, number: number) => {
    const product = productList.value.find((item) => item.id === id)
    if (!product) return false

    // 看看服务器那边在不在更新，如果正在请求则跳出
    if (product.changingNumber) return

    product.changingNumber = true
    const response = await $api.updateShoppingCar({
      productInfoId: id,
      quantity: number.toString(),
      quantityRetail: "0"
    })
    product.changingNumber = false
    switch (response.__error) {
      case undefined:
        if (response.code === "30003") {
          $toast.show("该商品库存不足")
          return false
        } else if (response.code === "90004") {
          $toast.show("该商品已下架")
          return false
        } else {
          product.number = str2num(response.data.sumQuantity)
          product.serverNumber = str2num(response.data.sumQuantity)
          return true
        }
      default:
        return false
    }
  }

  /** 刷新商品库存 */
  const updateProductShelvesStock = async (id: string) => {
    const product = productList.value.find((item) => item.id === id)
    if (!product) return false

    const response = await $api.productShelvesStockActualTime({
      productInfoId: id
    })
    switch (response.__error) {
      case undefined: {
        const shelvesStock = str2num(response.data.shelvesStock)
        product.shelvesStock = isNaN(shelvesStock)
          ? 0
          : Math.floor(shelvesStock / product.unitAmount)
        return true
      }
      default:
        return false
    }
  }

  const categoryList = ref<Category[]>()

  const filterCategoryList = (list: Category[] | undefined, level: number) => {
    if (!list) return undefined
    const resultList: Category[] = []
    for (let i = 0; i < list.length; i += 1) {
      const item = list[i]
      if (!item) continue
      if (level === 4) {
        resultList.push(item)
      } else {
        if (!item.children?.length) continue
        const children = filterCategoryList(item.children, level + 1)
        if (!children?.length) continue
        resultList.push({ ...item, children })
      }
    }
    return resultList
  }

  const getCategoryList = async () => {
    if (categoryList.value) return
    const storage = uni.getStorageSync("category_list")
    try {
      if (storage) categoryList.value = JSON.parse(storage)
    } catch {}
    const response = await $api.categoryList({})
    switch (response.__error) {
      case undefined: {
        const resultList = response.data as Category[]
        categoryList.value = filterCategoryList(resultList, 1)
        uni.setStorageSync("category_list", JSON.stringify(response.data))
      }
    }
    if (categoryList.value?.[0]) {
      changeFirstCategory(categoryList.value[0].id)
    }
  }
  const firstCategoryList = computed(() => {
    if (!categoryList.value) return
    const list = categoryList.value.map((item) => ({
      id: item.id,
      img: item.img,
      name: item.name
    }))
    return list
  })
  const firstCategory = ref<Category>()
  const changeFirstCategory = (id: number) => {
    if (!categoryList.value) return
    const category = categoryList.value.find((item) => item.id === id)
    if (!category) return
    firstCategory.value = category
    if (!category.children?.[0]) {
      secondCategory.value = undefined
      thirdCategoryList.value = []
    } else {
      changeSecondCategory(category.children[0].id)
    }
  }
  const secondCategoryList = computed(() => {
    if (!firstCategory.value?.children) return []
    const list = firstCategory.value.children.map((item) => ({
      id: item.id,
      img: item.img,
      name: item.name
    }))
    return list
  })
  const secondCategory = ref<Category>()
  const changeSecondCategory = (id: number) => {
    if (!firstCategory.value?.children) return
    const category = firstCategory.value.children.find((item) => item.id === id)
    if (!category) return
    secondCategory.value = category
    getThirdCategoryList()
  }
  const thirdCategoryStatus = ref<"refreshing" | "loading" | "error" | "done">(
    "done"
  )
  const thirdCategoryList = ref<
  {
    categoryId: number
    categoryName: string
    children: ProductCategoryItem[]
  }[]
  >()

  const refreshThirdCategoryList = async () => {
    if (!secondCategory.value?.id) return
    await findProductByCategoryIdTwo("refreshing")
  }

  const getThirdCategoryList = async () => {
    if (!secondCategory.value?.id) return
    await findProductByCategoryIdTwo("loading")
  }

  const findProductByCategoryIdTwo = async (
    status: "refreshing" | "loading"
  ) => {
    if (!secondCategory.value?.id) return
    thirdCategoryStatus.value = status
    thirdCategoryList.value = []
    const response = await $api.findProductByCategoryIdTwo({
      categoryIdTwo: secondCategory.value.id
    })
    switch (response.__error) {
      case undefined:
        thirdCategoryStatus.value = "done"
        thirdCategoryList.value = response.data.map((item) => {
          const productList: ProductCategoryItem[] = item.productLv4ResponseVo
            .map((item) => ({
              id: num2str(item.productInfoId),
              image: item.categoryImg,
              name: item.categoryName,
              categoryList:
                item.productSpecModelList?.length
                  ? item.productSpecModelList
                  : categoryNameToList(item.categoryName),
              unitPrice: str2num(item.productPrice),
              unitAmount: item.productPackage,
              displayPrice: str2num(item.displayUnitPrice),
              displayAmount: item.productPackage,
              title: item.productTitle
            }))
            .slice(0, 3)
          return {
            categoryId: item.categoryId,
            categoryName: item.categoryName,
            children: productList
          }
        })
        break
      default:
        thirdCategoryStatus.value = "error"
    }
  }

  const invoicingMsg = ref<InvoiceList | undefined>()
  const notIssueInvoiceList = ref<NotIssueInvoice[]>([])
  const invoiceHeader = ref<InvoiceList | undefined>()
  // const selectedAll = ref(false)
  const setInvoicingMsg = (data: InvoiceList) => {
    invoicingMsg.value = data
  }
  const setNotIssueInvoiceList = (data: NotIssueInvoice[]) => {
    notIssueInvoiceList.value = data
  }
  const setInvoiceHeader = (data: InvoiceList | undefined) => {
    invoiceHeader.value = data
  }

  const regionList = ref<Province[] | undefined>()
  interface RegionItem {
    name: string
    id: number
    children?: RegionItem[]
  }
  const changeRegion = (list: RegionItem[]) => {
    const outputList = [] as Province[] | City[] | District[]
    for (let i = 0; i < list.length; i += 1) {
      const children = list[i].children
      outputList.push({
        label: list[i].name,
        value: list[i].id,
        children: children ? changeRegion(children) : undefined
      })
    }
    return outputList
  }
  const getRegionList = async () => {
    if (regionList.value) return
    const response = await $api.districtList({})
    switch (response.__error) {
      case undefined: {
        const data = response.data as unknown as RegionItem[]
        regionList.value = changeRegion(data)
      }
    }
  }

  let subscribeList: string[] = []
  const getWXTemplateId = async () => {
    const response = await $api.getWXTemplateId({})
    switch (response.__error) {
      case undefined: {
        subscribeList = []
        if (response?.data?.orderCancelTemplateId) {
          subscribeList.push(response.data.orderCancelTemplateId)
        }
        // if (response?.data?.orderDeliverTemplateId) {
        //   subscribeList.push(response.data.orderDeliverTemplateId)
        // }
        if (response?.data?.orderRefundTemplateId) {
          subscribeList.push(response.data.orderRefundTemplateId)
        }
      }
    }
  }
  const requestSubscribeMessage = () => {
    if (!subscribeList.length) return
    return new Promise<boolean>((resolve) => {
      uni.requestSubscribeMessage({
        tmplIds: subscribeList,
        success: () => resolve(true),
        fail: () => resolve(false)
      })
    })
  }

  return {
    address,
    deliveryAddress,
    deliveryName,
    deliveryTel,
    enterpriseAbbr,
    confirmOrder,
    showCustomTabbar,
    isBuyImmediate,
    setAddress,
    setConfirmOrder,
    refreshToken,
    hideTabBar,
    addressList,
    selectedAddressId,
    defaultAddressId,
    getAddressList,
    setDefaultAddress,
    selectAddress,
    selectDefaultAddress,
    deleteAddress,
    selectedAddress,
    defaultAddress,
    corporateList,
    getCorporateList,
    selectedCorporate,
    setCorporate,
    deleteCorporate,
    reasonList,
    afterSalesOrder,
    afterSalesProduct,
    setAftersalesOrder,
    setAftersalesProduct,
    aftersalesDelivery,
    setAftersalesDelivery,
    selectedIdList,
    productList,
    addProductSelect,
    selectProductList,
    toggleProductSelect,
    toggleSelectAll,
    clearSelect,
    refreshProductList,
    updateProductNumber,
    updateProductShelvesStock,
    categoryList,
    firstCategoryList,
    firstCategory,
    changeFirstCategory,
    secondCategoryList,
    secondCategory,
    changeSecondCategory,
    thirdCategoryStatus,
    thirdCategoryList,
    getCategoryList,
    refreshThirdCategoryList,
    invoicingMsg,
    setInvoicingMsg,
    setNotIssueInvoiceList,
    notIssueInvoiceList,
    invoiceHeader,
    setInvoiceHeader,
    regionList,
    getRegionList,
    getWXTemplateId,
    requestSubscribeMessage
  }
})
