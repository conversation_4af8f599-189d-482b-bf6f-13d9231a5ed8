<template>
  <view>
    <view class="page-header hide-shadow">
      <v-navbar background-color="transparent" />
    </view>
    <view class="background">
      <view class="bar-1" />
      <view class="bar-2" />
      <view class="bar-3" />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view v-if="logined" class="user-container flex-center">
        <v-image
          v-if="!userAvatar"
          height="140rpx"
          width="140rpx"
          mode="aspectFill"
          padding="20rpx"
          margin="0 24rpx 0 0"
          src="/static/image/empty-avatar.svg"
          @click="navigateToUserConfig"
        />
        <v-image
          v-else
          height="140rpx"
          width="140rpx"
          mode="aspectFill"
          margin="0 24rpx 0 0"
          border-radius="70rpx"
          :src="userAvatar"
          @error="imageError"
          @click="navigateToUserConfig"
        />
        <view v-if="!userName">
          <view
            class="user-name text-32 text-bold"
            @click="navigateToUserConfig"
          >
            微信用户
          </view>
          <view class="text-24 text-sub">
            {{ encryptedPhone }}
          </view>
        </view>
        <view v-else>
          <view
            class="user-name text-1-row text-32 text-bold"
            @click="navigateToUserConfig"
          >
            {{ userName }}
          </view>
          <view class="text-24 text-sub">
            {{ encryptedPhone }}
          </view>
        </view>
      </view>
      <view v-else class="user-container flex-center">
        <v-image
          height="140rpx"
          width="140rpx"
          mode="aspectFill"
          margin="0 24rpx 0 0"
          padding="20rpx"
          src="/static/image/empty-avatar.svg"
          @click="navigateToSignUp"
        />
        <view>
          <view class="user-name text-32 text-bold" @click="navigateToSignUp">
            登录/注册
          </view>
          <view class="text-24 text-sub">
            未登录，点击登录/注册
          </view>
        </view>
      </view>
      <view v-if="logined" class="count-pad flex--evenly">
        <view
          class="count-item flex-center-center flex-col"
          @click="navigateToPointsPage"
        >
          <view class="main">
            {{ typeof points === "number" ? points : "-" }}
          </view>
          <view class="sub">
            我的积分
          </view>
        </view>
        <view
          class="count-item flex-center-center flex-col"
          @click="navigateToCoupon"
        >
          <view class="main">
            {{ typeof coupon === "number" ? coupon : "-" }}
          </view>
          <view class="sub">
            优惠券
          </view>
        </view>
        <view
          class="count-item flex-center-center flex-col"
          @click="navigateToCollection"
        >
          <view class="main">
            {{ typeof collection === "number" ? collection : "-" }}
          </view>
          <view class="sub">
            我的收藏
          </view>
        </view>
      </view>
      <view class="v-block">
        <view class="title flex-center-between">
          <view class="text-bold">
            我的订单
          </view>
          <view class="flex-center" @click="() => navigateToOrder()">
            <view class="text-24 text-light">
              全部订单
            </view>
            <v-icon
              size="18rpx"
              margin-left="12rpx"
              src="/static/icons/right-arrow.png"
            />
          </view>
        </view>
        <view class="content flex-center">
          <view
            class="flex-1 flex-col flex-center-center"
            @click="() => navigateToOrder(1)"
          >
            <view class="order-menu-icon">
              <v-icon
                height="94rpx"
                width="91rpx"
                src="/static/icons/user/pending-pay.png"
              />
              <view class="badge">
                <v-badge :count="orderCount.pendingPay" />
              </view>
            </view>
            <view class="text-24">
              待支付
            </view>
          </view>

          <view
            class="flex-1 flex-col flex-center-center"
            @click="() => navigateToOrder(2)"
          >
            <view class="order-menu-icon">
              <v-icon
                height="94rpx"
                width="91rpx"
                src="/static/icons/user/obligation.png"
              />
              <view class="badge">
                <v-badge :count="orderCount.pendingReceive" />
              </view>
            </view>
            <view class="text-24">
              待发货
            </view>
          </view>
          <view
            class="flex-1 flex-col flex-center-center"
            @click="() => navigateToOrder(3)"
          >
            <view class="order-menu-icon">
              <v-icon
                height="94rpx"
                width="91rpx"
                src="/static/icons/user/pending-receive.png"
              />
              <view class="badge">
                <v-badge :count="orderCount.pendingDelivery" />
              </view>
            </view>
            <view class="text-24">
              待收货
            </view>
          </view>
          <view
            class="flex-1 flex-col flex-center-center"
            @click="() => navigateToOrder(4)"
          >
            <view class="order-menu-icon">
              <v-icon
                height="94rpx"
                width="91rpx"
                src="/static/icons/user/order-complete.png"
              />
              <!-- <view class="badge">
                <v-badge :count="orderCount.complete" />
              </view> -->
            </view>
            <view class="text-24">
              已完成
            </view>
          </view>
          <view
            class="flex-1 flex-col flex-center-center"
            @click="navigateToAftersales"
          >
            <view class="order-menu-icon">
              <v-icon
                height="94rpx"
                width="90rpx"
                src="/static/icons/user/after-sales.png"
              />
              <view class="badge">
                <v-badge :count="orderCount.aftersales" />
              </view>
            </view>
            <view class="text-24">
              售后
            </view>
          </view>
        </view>
      </view>
      <view class="v-block">
        <view class="title text-bold">
          常用功能
        </view>
        <view class="content flex-center flex-warp">
          <!-- <view
            class="flex-1 flex-col flex-center-center"
            @click="navigateToCollection"
          >
            <v-icon size="90rpx" src="/static/icons/user/collection.png" />
            <view class="adjust-margin text-24">
              我的收藏
            </view>
          </view> -->
          <view
            class="menu-item flex-col flex-center-center"
            @click="navigateToInvoicePage"
          >
            <v-icon size="50rpx" src="/static/icons/user/invoice.png" />
            <view class="adjust-margin text-24">
              发票管理
            </view>
          </view>
          <view
            class="menu-item flex-col flex-center-center"
            @click="navigateToAddressList"
          >
            <v-icon size="50rpx" src="/static/icons/user/address.png" />
            <view class="adjust-margin text-24">
              收货地址
            </view>
          </view>
          <view
            class="menu-item flex-col flex-center-center"
            @click="navigateToCustomerService"
          >
            <v-icon size="50rpx" src="/static/icons/user/client-service.png" />
            <view class="adjust-margin text-24">
              客户服务
            </view>
          </view>
          <view
            class="menu-item flex-col flex-center-center"
            @click="navigateToActivity"
          >
            <v-icon size="50rpx" src="/static/icons/user/activity.png" />
            <view class="adjust-margin text-24">
              活动中心
            </view>
          </view>
          <view
            class="menu-item flex-col flex-center-center"
            @click="navigateToPointsCenter"
          >
            <v-icon size="50rpx" src="/static/icons/user/points.png" />
            <view class="adjust-margin text-24">
              积分兑换
            </view>
          </view>
          <view
            class="menu-item flex-col flex-center-center"
            @click="navigateToReceiveCoupon"
          >
            <v-icon size="50rpx" src="/static/icons/user/coupon-center.png" />
            <view class="adjust-margin text-24">
              领券中心
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="page-footer">
      <v-tabbar :index="3" />
    </view>
    <v-toast />
  </view>
</template>

<script lang="ts" setup>
import { checkToken } from "@/utils/auth/token"

const { pageHeight } = usePageLayout()

const navigateToUserConfig = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/pages/user/user-config" })
}
// 优惠券
const navigateToCoupon = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-coupon/coupon/my-coupon" })
}
// 活动中心
const navigateToActivity = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-activity/activity/activity-page" })
}
const navigateToOrder = (index?: number) => {
  if (!checkToken()) return navigateToSignUp()
  if (!index) {
    uni.navigateTo({ url: "/package-order/order/order-page" })
  } else {
    uni.navigateTo({ url: `/package-order/order/order-page?index=${index}` })
  }
}
const navigateToCustomerService = () => {
  uni.navigateTo({ url: "/package-user/user/customer-service" })
}
const navigateToInvoicePage = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-invoice/invoice/invoice-page" })
}

const navigateToAddressList = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-user/user/address-list" })
}

const navigateToAftersales = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-aftersales/aftersales/aftersales-page" })
}

const navigateToCollection = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-user/user/user-collection" })
}
const navigateToPointsCenter = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-points/points/points-center" })
}
const navigateToReceiveCoupon = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-coupon/coupon/receive-coupon" })
}
const navigateToPointsPage = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-points/points/points-page" })
}

const logined = ref(false)
const userAvatar = ref<string | undefined>()
const userName = ref<string | undefined>()
const userPhone = ref<string | undefined>()

const encryptedPhone = computed(() => {
  if (!userPhone.value) return ""
  return userPhone.value.slice(0, 3) + "****" + userPhone.value.slice(-4)
})

const imageError = () => {
  uni.removeStorageSync("user_avatar")
  userAvatar.value = undefined
}

const points = ref<number>()
const coupon = ref<number>()
const collection = ref<number>()
const getUserInfo = async () => {
  const response = await $api.findMemberInfoWeChat({})
  switch (response.__error) {
    case undefined:
      uni.setStorageSync("user_avatar", response.data.headIcon)
      uni.setStorageSync("user_name", response.data.nickName)
      uni.setStorageSync("user_phone", response.data.mobile)
      userAvatar.value = response.data.headIcon
      userName.value = response.data.nickName
      userPhone.value = response.data.mobile
      points.value = parseInt(response.data.availablePoints)
      coupon.value = parseInt(response.data.couponAmount)
      collection.value = parseInt(response.data.collectAmount)
  }
}

const orderCount = ref({
  pendingPay: 0,
  pendingReceive: 0,
  pendingDelivery: 0,
  // complete: 0,
  aftersales: 0
})
const getOrderCount = async () => {
  const response = await $api.orderHandleStatus({})
  switch (response.__error) {
    case undefined: {
      const count = {
        pendingPay: response.data.readyForPayCount,
        pendingReceive: response.data.readyForDeliverCount,
        pendingDelivery: response.data.readyForReceiptCount,
        aftersales: response.data.afterSaleCount
      }
      switch (true) {
        case count.aftersales > 99:
          count.aftersales = 99
          break
        case count.pendingPay > 99:
          count.pendingPay = 99
          break
        case count.pendingReceive > 99:
          count.pendingReceive = 99
          break
        // case count.complete > 99:
        //   count.complete = 99
        //   break
        case count.pendingDelivery > 99:
          count.pendingDelivery = 99
          break
      }
      orderCount.value = count
    }
  }
}

const checkUser = () => {
  if (!checkToken()) {
    logined.value = false
    userAvatar.value = undefined
    userName.value = undefined
  } else {
    logined.value = true
    userAvatar.value = uni.getStorageSync("user_avatar")
    userName.value = uni.getStorageSync("user_name")
    userPhone.value = uni.getStorageSync("user_phone")
    getUserInfo()
    getOrderCount()
  }
}
onShow(() => checkUser())

const navigateToSignUp = () => {
  uni.navigateTo({ url: "/pages/user/sign-up" })
}
</script>

<style lang="scss" scoped>
.background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-color: #f5f5f5;
  filter: blur(60px);
  .bar-1 {
    position: absolute;
    height: 120rpx;
    width: 100vw;
    background-color: rgb(105, 158, 242);
    top: 5vh;
    left: -40vw;
    transform: rotate(-60deg);
    transform-origin: 50% 50%;
  }
  .bar-2 {
    position: absolute;
    right: 20vw;
    top: 20vh;
    height: 160rpx;
    width: 160rpx;
    border-radius: 80rpx;
    background-color: rgb(105, 158, 242);
  }
  .bar-3 {
    position: absolute;
    height: 120rpx;
    width: 100vw;
    background-color: rgb(105, 158, 242);
    top: 5vh;
    left: -40vw;
    transform: rotate(-45deg);
    transform-origin: 50% 50%;
  }
}
.user-container {
  padding: 34rpx 24rpx;
  .user-name {
    margin-bottom: 18rpx;
  }
}
.count-pad {
  padding: 0 0 34rpx;
  .count-item {
    flex: 1;
    padding: 0 20rpx;
    overflow: hidden;
    box-sizing: border-box;
    .main {
      margin-bottom: 10rpx;
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: center;
    }
    .sub {
      font-size: 24rpx;
      color: #999;
    }
  }
}
.title,
.content {
  padding: 10rpx 0;
}
.menu-item {
  width: 25%;
  &:nth-child(n + 5) {
    margin-top: 40rpx;
  }
}
.order-menu-icon {
  position: relative;
  height: fit-content;
  width: fit-content;
  .badge {
    position: absolute;
    right: 0;
    top: 0;
    height: fit-content;
    width: fit-contents;
    transform: translate(-50%, -50%);
  }
}
.adjust-margin {
  margin-top: 10rpx;
}
</style>
