<template>
  <view
    class="v-tabs flex"
    :style="{ height: height, width: width || '100vw' }"
  >
    <view
      v-for="(item, index) in list"
      :key="index"
      class="v-tab-item flex-center-center"
      :style="
        modelValue === index
          ? activeTextStyle || 'font-weight: bold'
          : textStyle || ''
      "
      @click="() => changeTabs(index)"
    >
      {{ item }}
      <view
        v-if="index === 0"
        class="v-tabs-block"
        :style="{
          '--v-tabs-item-width': blockWidth,
          '--v-tabs-item-count': list.length
        }"
      >
        <slot />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: number
  list: string[]
  height: string
  width?: string
  textStyle?: string
  activeTextStyle?: string
}>()

interface Emits {
  (event: "update:model-value", val: number): void,
  (event: "change", val: number): void
}
const emits = defineEmits<Emits>()

const modelValue = toRef(props, "modelValue")
const list = toRef(props, "list")
const width = toRef(props, "width")

const blockWidth = computed(
  () => `(${width.value ?? "100vw"} / ${list.value.length})`
)

const changeTabs = (index: number) => {
  emits("update:model-value", index)
  emits("change", index)
}
</script>

<style lang="scss" scoped>
.v-tab-item {
  position: relative;
  flex: 1;
  .v-tabs-block {
    position: absolute;
    bottom: 0;
    height: 4rpx;
    width: 110rpx;
  }
}
</style>
