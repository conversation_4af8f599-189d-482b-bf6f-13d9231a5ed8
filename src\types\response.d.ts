declare interface $res {
  activityReceiveCoupon: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  couponCenterReceiveCoupon: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  getActivityCenterDetail: {
    code: string
    data: {
      /** 关联标签 */
      achievementList: {
        /** 标签id */
        achievementId: long
        /** 标签名称 */
        achievementName: string
        /** 客户数量 */
        tagCount: number
      }[]
      /** 活动入口图 */
      activityEntrance: string
      /** 活动入口图id */
      activityEntranceId: long
      /** 活动名称 */
      activityName: string
      /** 活动编号 */
      activityNo: string
      /** 活动海报 */
      activityPoster: string
      /** 活动海报id */
      activityPosterId: long
      /** 活动状态:0->草稿;1->未发布;2->已发布;3->已结束 */
      activityStatus: number
      /** 展示时间列表 */
      activityTimeList: {
        /** 开始时间 时间格式为yyyy-MM-dd HH:mm:ss */
        from: string
        /** 结束时间 时间格式为yyyy-MM-dd HH:mm:ss */
        to: string
      }[]
      /** 活动类型: 0->活动中心;1->普通活动 */
      activityType: number
      /** 审核意见 */
      approveRemark: string
      /** 审核状态:0->未提交;1->待审核;2->审核通过;3->驳回审核 */
      auditStatus: number
      /** 关联优惠券列表 */
      couponList: {
        /** 优惠券总数 */
        couponAmount: long
        /** 优惠券状态 */
        couponApproveStatus: number
        couponExp: {
          /** 有效天数 */
          expDays: long
          /** 有效日期区间 */
          ranges: {
            /** 有效期截止日期 */
            endTime: datetime
            /** 有效期开始日期 */
            startTime: datetime
          }[]
        }
        /** 优惠券编id */
        couponId: long
        /** 优惠券名称 */
        couponName: string
        /** 优惠券编号 */
        couponNo: string
        /** 优惠券类型 */
        couponType: number
      }[]
      /** 关联券统计列表 */
      couponStatisticsList: {
        /** 优惠券总数 */
        couponAmount: long
        /** 优惠券id */
        couponId: long
        /** 优惠券名称 */
        couponName: string
        /** 优惠券编号 */
        couponNo: string
        /** 已引用数量 */
        lockAmount: long
        /** 未使用数量 */
        notUsageAmount: long
        /** 领取数量 */
        receiveAmount: long
        /** 剩余数量 */
        residueAmount: long
        /** 已使用数量 */
        usageAmount: long
      }[]
      /** 是否分享:0->不允许;1->允许 */
      isShare: number
      /** 参与条件:0->用户类型 */
      participationCondition: number
      /** 参与条件类型:0->所有客户;1->标签客户; */
      participationConditionType: number
      /** 备注 */
      remark: string
    }
    message: string
    success: boolean
  }
  getActivityCenterList: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 活动入口图 */
        activityEntranceUrl: string
        /** 活动id */
        activityId: long
        /** 活动名称 */
        activityName: string
        /** 活动编号 */
        activityNo: string
        /** 活动类型: 0->活动中心;1->普通活动 */
        activityType: number
        /** 是否分享:0->不允许;1->允许 */
        isShare: number
        /** 发布时间 */
        releaseTime: datetime
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  addSysOpinionFeedback: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  getAdvertising: {
    code: string
    data: {
      /** 广告详情 */
      advertisingDetailList: {
        /** id */
        id: long
        /** 图片Id */
        imgId: long
        /** 图片链接 */
        imgUrl: string
        /** 是否开启 */
        isEnable: number
        /** 备注 */
        remark: string
        /** 排序 */
        sort: number
        /** 跳转链接 */
        url: string
      }[]
      /** 广告位子: 0->首页轮播广告；1->首页中部广告 */
      advertisingSpace: number
      /** 广告类型: 0->轮播图； */
      advertisingType: number
      /** 持续时长 */
      duration: number
      /** id */
      id: long
    }
    message: string
    success: boolean
  }
  getSysContentManage: {
    code: string
    data: {
      /** 协议内容 */
      content: string
      /** 协议位子:0->商品详情;1->客户服务;2->登录页面 */
      contentLocation: string
      /** 协议类型: 0->第三方配送服务说明; 1->价格说明; 2->订购说明; 3->售后政策说明; 4->售后退款说明;5->隐私协议; 6->用户服务协议; 7->发票说明 */
      contentType: number
      /** id */
      id: long
    }
    message: string
    success: boolean
  }
  getWXTemplateId: {
    code: string
    data: {
      /** 订单取消 */
      orderCancelTemplateId: string
      /** 订单发货通知 */
      orderDeliverTemplateId: string
      /** 订单退款通知 */
      orderRefundTemplateId: string
    }
    message: string
    success: boolean
  }
  getAllCouponListByUserId: {
    code: string
    data: {
      couponAreaLimit: {
        /** 优惠券地区限制列表 */
        couponAreaLimitItems: {
          /** 城市id */
          cityId: long
          /** 城市名称 */
          cityName: string
          /** 省份id */
          provinceId: long
          /** 省份名称 */
          provinceName: string
        }[]
      }
      couponExp: {
        /** 有效天数 */
        expDays: long
        /** 有效日期区间 */
        ranges: {
          /** 有效期截止日期 */
          endTime: datetime
          /** 有效期开始日期 */
          startTime: datetime
        }[]
      }
      /** 优惠券过期类型:0->设定;1->领用; */
      couponExpType: number
      /** 用户关联优惠券id */
      couponMemberRelationId: long
      /** 优惠券名称 */
      couponName: string
      /** 优惠券编号 */
      couponNo: string
      /** 优惠券使用价格限制 */
      couponPriceLimit: double
      /** 备注 */
      couponRemark: string
      /** 优惠券类型:0->满减;1->打折; */
      couponType: number
      /** 优惠券用户类型限制:0->全部;1->新用户;2->老用户; */
      couponUserTypeLimit: number
      /** 优惠券面值 */
      couponValue: double
      /** 是否使用:0->否;1->是 */
      isUsed: number
      /** 过期时间 */
      overdueTime: datetime
      /** 用户券表修改时间 */
      updatedTime: datetime
    }[]
    message: string
    success: boolean
  }
  getCouponListByUserId: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        couponAreaLimit: {
          /** 优惠券地区限制列表 */
          couponAreaLimitItems: {
            /** 城市id */
            cityId: long
            /** 城市名称 */
            cityName: string
            /** 省份id */
            provinceId: long
            /** 省份名称 */
            provinceName: string
          }[]
        }
        couponExp: {
          /** 有效天数 */
          expDays: long
          /** 有效日期区间 */
          ranges: {
            /** 有效期截止日期 */
            endTime: datetime
            /** 有效期开始日期 */
            startTime: datetime
          }[]
        }
        /** 优惠券过期类型:0->设定;1->领用; */
        couponExpType: number
        /** 用户关联优惠券id */
        couponMemberRelationId: long
        /** 优惠券名称 */
        couponName: string
        /** 优惠券编号 */
        couponNo: string
        /** 优惠券使用价格限制 */
        couponPriceLimit: double
        /** 备注 */
        couponRemark: string
        /** 优惠券类型:0->满减;1->打折; */
        couponType: number
        /** 优惠券用户类型限制:0->全部;1->新用户;2->老用户; */
        couponUserTypeLimit: number
        /** 优惠券面值 */
        couponValue: double
        /** 是否使用:0->否;1->是 */
        isUsed: number
        /** 过期时间 */
        overdueTime: datetime
        /** 用户券表修改时间 */
        updatedTime: datetime
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  getHistoryCouponListByUserId: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        couponAreaLimit: {
          /** 优惠券地区限制列表 */
          couponAreaLimitItems: {
            /** 城市id */
            cityId: long
            /** 城市名称 */
            cityName: string
            /** 省份id */
            provinceId: long
            /** 省份名称 */
            provinceName: string
          }[]
        }
        couponExp: {
          /** 有效天数 */
          expDays: long
          /** 有效日期区间 */
          ranges: {
            /** 有效期截止日期 */
            endTime: datetime
            /** 有效期开始日期 */
            startTime: datetime
          }[]
        }
        /** 优惠券过期类型:0->设定;1->领用; */
        couponExpType: number
        /** 用户关联优惠券id */
        couponMemberRelationId: long
        /** 优惠券名称 */
        couponName: string
        /** 优惠券编号 */
        couponNo: string
        /** 优惠券使用价格限制 */
        couponPriceLimit: double
        /** 备注 */
        couponRemark: string
        /** 优惠券类型:0->满减;1->打折; */
        couponType: number
        /** 优惠券用户类型限制:0->全部;1->新用户;2->老用户; */
        couponUserTypeLimit: number
        /** 优惠券面值 */
        couponValue: double
        /** 是否使用:0->否;1->是 */
        isUsed: number
        /** 过期时间 */
        overdueTime: datetime
        /** 用户券表修改时间 */
        updatedTime: datetime
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  getReceiveCouponCenterList: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 优惠券总数 */
        couponAmount: long
        couponAreaLimit: {
          /** 优惠券地区限制列表 */
          couponAreaLimitItems: {
            /** 城市id */
            cityId: long
            /** 城市名称 */
            cityName: string
            /** 省份id */
            provinceId: long
            /** 省份名称 */
            provinceName: string
          }[]
        }
        couponExp: {
          /** 有效天数 */
          expDays: long
          /** 有效日期区间 */
          ranges: {
            /** 有效期截止日期 */
            endTime: datetime
            /** 有效期开始日期 */
            startTime: datetime
          }[]
        }
        /** 优惠券过期类型:0->设定;1->领用; */
        couponExpType: number
        /** 优惠券名称 */
        couponName: string
        /** 优惠券编号 */
        couponNo: string
        /** 优惠券使用价格限制 */
        couponPriceLimit: double
        /** 优惠券日领取限制 */
        couponReceiveDailyLimit: long
        /** 优惠券领取限制 */
        couponReceiveLimit: long
        /** 备注 */
        couponRemark: string
        /** 优惠券类型:0->满减;1->打折; */
        couponType: number
        /** 优惠券用户类型限制:0->全部;1->新用户;2->老用户; */
        couponUserTypeLimit: number
        /** 优惠券面值 */
        couponValue: double
        /** 发布时间 */
        publishTime: datetime
        /** 领券中心Id */
        receiveCouponCenterRelationId: long
        /** 优惠券剩余数 */
        residueAmount: long
        /** 序号 */
        serialnumber: long
        /** 优惠券地区限制 */
        strCouponAreaLimit: string
        /** 优惠券有效期 */
        strCouponExp: string
        /** 用户领取数量 */
        userReceiveAmount: number
        /** 用户当日领取数量 */
        userReceiveDailyAmount: number
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  getToken: undefined
  signUpOrRegisterByCode: {
    code: string
    data: {
      /** 登录token */
      token: string
      /** token过期时间 */
      tokenExpires: long
    }
    message: string
    success: boolean
  }
  signUpOrRegisterByMobile: {
    code: string
    data: {
      /** 登录token */
      token: string
      /** token过期时间 */
      tokenExpires: long
    }
    message: string
    success: boolean
  }
  smsMobileValidateCode: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  addressList: {
    code: string
    /** 地址列表 */
    data: {
      /** 地址数量 */
      addressCount: number
      /** 地址列表 */
      addressList: {
        /** 收货人地址 */
        address: string
        /** 市id */
        cityCode: number
        /** 市名称 */
        cityName: string
        /** 公司名称 */
        corporateName: string
        /** 市id */
        districtCode: number
        /** 区名称 */
        districtName: string
        /** 会员地址Id */
        id: long
        /** 默认地址，0-否、1-是 */
        isDefault: number
        /** 标签 */
        label: string
        /** 收货人手机 */
        mobile: string
        /** 收货人名称 */
        name: string
        /** 省id */
        provinceCode: number
        /** 省名称 */
        provinceName: string
        /** 收货人固话 */
        tel: string
      }[]
      /** 默认地址id */
      memberAddressDefault: long
    }
    message: string
    success: boolean
  }
  addressSaveAndUpdate: {
    code: string
    data: {
      /** 收货人地址 */
      address: string
      /** 市id */
      cityCode: number
      /** 市名称 */
      cityName: string
      /** 公司名称 */
      corporateName: string
      /** 市id */
      districtCode: number
      /** 区名称 */
      districtName: string
      /** 会员地址Id */
      id: long
      /** 默认地址，0-否、1-是 */
      isDefault: number
      /** 标签 */
      label: string
      /** 收货人手机 */
      mobile: string
      /** 收货人名称 */
      name: string
      /** 省id */
      provinceCode: number
      /** 省名称 */
      provinceName: string
      /** 收货人固话 */
      tel: string
    }
    message: string
    success: boolean
  }
  collectList: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 是否删除 */
        delFlag: string
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 是否在售 */
        isOnSale: string
        /** 收藏Id */
        memberCollectId: long
        /** NC物料编码 */
        ncCode: string
        /** 销售规格 */
        ncPackage: number
        /** 终端商价格 */
        ncRetailPrice: double
        /** NC库存 */
        ncStock: long
        /** 经销商价格 */
        ncWholesalePrice: double
        /** 外箱规格 */
        outerBoxSpec: string
        /** 商品Id */
        productInfoId: long
        /** 商品主图 */
        productInfoImg: string
        /** 商品名称 */
        productInfoName: string
        /** 商品容量 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品规格 */
        productSpec: string
        /** 商品规格型号 */
        productSpecModel: string
        /** 商品规格名称 */
        productSpecName: string
        /** 商品规格编号 */
        productSpecNumber: string
        /** 上架库存 */
        putawayStock: long
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  corporateList: {
    code: string
    /** 公司列表 */
    data: {
      /** 公司列表信息 */
      corporateList: {
        /** 公司名称 */
        corporateName: string
        /** 公司Id */
        id: long
      }[]
    }
    message: string
    success: boolean
  }
  corporateSaveAndUpdate: {
    code: string
    /** 公司列表 */
    data: {
      /** 公司列表信息 */
      corporateList: {
        /** 公司名称 */
        corporateName: string
        /** 公司Id */
        id: long
      }[]
    }
    message: string
    success: boolean
  }
  delAddress: {
    code: string
    /** 地址列表 */
    data: {
      /** 地址数量 */
      addressCount: number
      /** 地址列表 */
      addressList: {
        /** 收货人地址 */
        address: string
        /** 市id */
        cityCode: number
        /** 市名称 */
        cityName: string
        /** 公司名称 */
        corporateName: string
        /** 市id */
        districtCode: number
        /** 区名称 */
        districtName: string
        /** 会员地址Id */
        id: long
        /** 默认地址，0-否、1-是 */
        isDefault: number
        /** 标签 */
        label: string
        /** 收货人手机 */
        mobile: string
        /** 收货人名称 */
        name: string
        /** 省id */
        provinceCode: number
        /** 省名称 */
        provinceName: string
        /** 收货人固话 */
        tel: string
      }[]
      /** 默认地址id */
      memberAddressDefault: long
    }
    message: string
    success: boolean
  }
  delCorporate: {
    code: string
    /** 公司列表 */
    data: {
      /** 公司列表信息 */
      corporateList: {
        /** 公司名称 */
        corporateName: string
        /** 公司Id */
        id: long
      }[]
    }
    message: string
    success: boolean
  }
  deleteMemberCollect: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  districtList: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  findMemberInfoWeChat: {
    code: string
    data: {
      /** 可用积分 */
      availablePoints: double
      /** 收藏总数 */
      collectAmount: long
      /** 优惠券总数 */
      couponAmount: long
      /** 会员头像 */
      headIcon: string
      /** 手机号 */
      mobile: string
      /** 会员名称 */
      nickName: string
      /** 未到账积分 */
      outstandingPoints: double
    }
    message: string
    success: boolean
  }
  findMemberPointsDetailList: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 创建时间 */
        createdTime: datetime
        /** 删除标记 */
        delFlag: number
        /** 会员id */
        memberId: long
        /** 用户积分详细id */
        memberPointsDetailId: long
        /** 订单支付时间 */
        orderPayTime: datetime
        /** 积分 */
        points: double
        /** 积分状态，0->到账;1->消耗 */
        pointsStatus: number
        /** 积分类型，0->消费积分；1->积分过期；2->积分兑换； */
        pointsType: number
        /** 业务Id */
        serviceId: long
        /** 业务编号 */
        serviceNo: string
        /** 业务类型：0->订单管理；1->兑换活动 */
        serviceType: number
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  isCollectProduct: {
    code: string
    data: {
      /** 是否收藏 */
      isCollected: boolean
    }
    message: string
    success: boolean
  }
  saveMemberCollect: {
    code: string
    data: {
      /** 分类id */
      categoryId: string
      /** 创建时间 */
      createdTime: datetime
      /** 主键 */
      id: long
      /** 会员id */
      memberId: long
      /** 商品编号 */
      productInfoId: string
    }
    message: string
    success: boolean
  }
  setDefaultAddress: {
    code: string
    /** 地址列表 */
    data: {
      /** 地址数量 */
      addressCount: number
      /** 地址列表 */
      addressList: {
        /** 收货人地址 */
        address: string
        /** 市id */
        cityCode: number
        /** 市名称 */
        cityName: string
        /** 公司名称 */
        corporateName: string
        /** 市id */
        districtCode: number
        /** 区名称 */
        districtName: string
        /** 会员地址Id */
        id: long
        /** 默认地址，0-否、1-是 */
        isDefault: number
        /** 标签 */
        label: string
        /** 收货人手机 */
        mobile: string
        /** 收货人名称 */
        name: string
        /** 省id */
        provinceCode: number
        /** 省名称 */
        provinceName: string
        /** 收货人固话 */
        tel: string
      }[]
      /** 默认地址id */
      memberAddressDefault: long
    }
    message: string
    success: boolean
  }
  tempUploadFile: {
    code: string
    data: {
      fileId: long
      fileUrl: string
    }[]
    message: string
    success: boolean
  }
  updateMemberInfoWeChat: {
    code: string
    data: {
      /** 可用积分 */
      availablePoints: double
      /** 收藏总数 */
      collectAmount: long
      /** 优惠券总数 */
      couponAmount: long
      /** 会员头像 */
      headIcon: string
      /** 手机号 */
      mobile: string
      /** 会员名称 */
      nickName: string
      /** 未到账积分 */
      outstandingPoints: double
    }
    message: string
    success: boolean
  }
  invoicingDelete: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  invoicingInsert: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  invoicingList: {
    code: string
    data: {
      /** 开票信息管理列表 */
      makeInvoiceResponseList: {
        /** 创建时间 */
        createdTime: datetime
        /** 开票信息id */
        id: long
        /** 开户银行 */
        invoiceBank: string
        /** 银行账号 */
        invoiceBankAccount: string
        /** 公司地址 */
        invoiceCompanyAddress: string
        /** 公司电话 */
        invoiceCompanyTel: string
        /** 税号 */
        invoiceDutyParagraph: string
        /** 电子邮箱 */
        invoiceEmail: string
        /** 发票抬头 */
        invoiceHeader: string
        /** 抬头类型，0-企业 */
        invoiceHeaderType: number
        /** 会员id */
        memberId: long
      }[]
    }
    message: string
    success: boolean
  }
  invoicingUpdate: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  afterSaleProductPriceCount: {
    code: string
    data: {
      /** 商品总价 */
      sumPrice: double
      /** 商品总数量-箱 */
      sumQuantity: long
    }
    message: string
    success: boolean
  }
  cancelOrderRefund: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  findOrderRefund: {
    code: string
    data: {
      /** 售后申请退款金额 */
      afterSaleApplyPrice: double
      /** 用户取消售后时间 */
      afterSaleCancelDate: datetime
      /** 售后操作时间 */
      afterSaleOperateDate: datetime
      /** 售后退款操作时间 */
      afterSaleRefundOperateDate: datetime
      /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->同意退款，6->退款待确认，8->用户主动取消，9->已完成 */
      afterSaleStatus: number
      /** 售后类型 */
      afterSaleType: string
      /** 客户申请退款金额 */
      applyBackPrice: double
      /** 退款说明 */
      backExplain: string
      /** 退款图片 */
      backImgs: string[]
      /** 退款原因 */
      backReason: string
      /** 创建时间 */
      createdTime: datetime
      /** 物流公司 */
      deliveryCompany: string
      /** 物流单号 */
      deliverySn: string
      imgs: string
      /** 售后编号 */
      orderAfterSaleNo: string
      /** 退款订单id */
      orderBackId: long
      /** 订单号 */
      orderNo: string
      /** 订单商品列表 */
      orderProductRelationVOList: {
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 用户退回箱数 */
        memberReturnBoxNumber: number
        /** 订单退款id */
        orderBackId: long
        /** 订单id */
        orderId: long
        /** 订单商品售后状态，0->退货退款，3->未售后 */
        orderProductAfterSalesStatus: number
        /** 商品实际支付总价 */
        productActualTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoPackage: long
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品积分 */
        productPoints: double
        /** 商品数量 */
        quantity: long
        /** 商品散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 总数量 */
        totalPackage: string
        /** 总价 */
        totalPrice: double
      }[]
      /** 支付金额 */
      payPrice: double
      /** 退款时间 */
      refundDate: datetime
      /** 退款操作时间-财务 */
      refundOperateDate: datetime
      /** 退货地址 */
      returnGoodAddress: string
      /** 退货收件人 */
      returnGoodName: string
      /** 退货电话 */
      returnGoodPhone: string
      /** 寄物流时间 */
      sendDeliveryDate: datetime
      /** 仓库操作时间 */
      storehouseOperateDate: datetime
    }
    message: string
    success: boolean
  }
  ncReceiveOrderAfterSale: {
    code: string
    data: {
      /** 购物车子项信息 */
      cartList: {
        /** 是否删除（0：未删；1：已删） */
        delFlag: string
        /** 提货方式 */
        deliveryType: string
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 购物车id */
        id: long
        /** 是否上下架（0下架，1上架） */
        isOnSale: string
        /** 是否散卖 */
        isRetailSale: string
        /** NC物料编码 */
        ncCode: string
        /** 商品一件总价 (单价 * 数量（1）* 装数) */
        oneTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料颜色 */
        productInfoNcColour: string
        /** 物料材质 */
        productInfoNcMaterial: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品规格-后台新 */
        productSpec: string
        /** 商品规格型号-后台新 */
        productSpecModel: string
        /** 商品规格名称-后台新 */
        productSpecName: string
        /** 整箱购买数量 */
        quantity: long
        /** 散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 商品上架库存-后台新 */
        shelvesStock: long
        /** 总数量 */
        totalPackage: long
        /** 总价 */
        totalPrice: double
      }[]
      /** 物流地址 */
      deliveryAddress: string
      /** 物流公司 */
      deliveryName: string
      /** 物流电话 */
      deliveryTel: string
      /** 公司名称 */
      enterpriseAbbr: string
      /** id */
      id: long
      /** 防重令牌 */
      orderToken: string
      /** 商品总价 */
      sumPrice: double
    }
    message: string
    success: boolean
  }
  orderRefundList: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 售后申请退款金额 */
        afterSaleApplyPrice: double
        /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->同意退款，6->退款待确认，8->用户主动取消，9->已完成 */
        afterSaleStatus: number
        /** 售后类型：0->退货退款 */
        afterSaleType: string
        /** 客户申请退款金额 */
        applyBackPrice: double
        /** 创建时间 */
        createdTime: datetime
        /** 售后编号 */
        orderAfterSaleNo: string
        /** 退款订单id */
        orderBackId: long
        /** 订单号 */
        orderNo: string
        /** 订单商品列表 */
        orderProductRelationVOList: {
          /** 展示规格 */
          displayPackage: number
          /** 展示单价 */
          displayUnitPrice: double
          /** 用户退回箱数 */
          memberReturnBoxNumber: number
          /** 订单退款id */
          orderBackId: long
          /** 订单id */
          orderId: long
          /** 订单商品售后状态，0->退货退款，3->未售后 */
          orderProductAfterSalesStatus: number
          /** 商品实际支付总价 */
          productActualTotalPrice: double
          /** 商品id */
          productInfoId: long
          /** 商品名称 */
          productInfoName: string
          /** 物料计量单位 */
          productInfoNcMeterageUnitName: string
          /** 物料装数 */
          productInfoPackage: long
          /** 商品价格 */
          productInfoPrice: double
          /** 商品图片 */
          productInfoProductImg: string
          /** 商品规格 */
          productInfoSpec: string
          /** 商品标题 */
          productInfoTitle: string
          /** 商品积分 */
          productPoints: double
          /** 商品数量 */
          quantity: long
          /** 商品散卖数量 */
          quantityRetail: long
          /** 散卖最少数量 */
          retailMinUnit: long
          /** 散卖价格 */
          retailPrice: double
          /** 总数量 */
          totalPackage: string
          /** 总价 */
          totalPrice: double
        }[]
        /** 支付金额 */
        payPrice: double
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  saveOrderRefund: {
    code: string
    data: {
      /** 退款单id */
      orderBackId: long
    }
    message: string
    success: boolean
  }
  updateOrderBackDelivery: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  confirmReceipt: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  deleteOrder: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  getOrderFollowWaybill: {
    code: string
    data: {
      /** 订单号 */
      orderNo: string
      /** waybill_token */
      waybillToken: string
    }
    message: string
    success: boolean
  }
  ncReceiveOrder: {
    code: string
    data: {
      /** 购物车子项信息 */
      cartList: {
        /** 是否删除（0：未删；1：已删） */
        delFlag: string
        /** 提货方式 */
        deliveryType: string
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 购物车id */
        id: long
        /** 是否上下架（0下架，1上架） */
        isOnSale: string
        /** 是否散卖 */
        isRetailSale: string
        /** NC物料编码 */
        ncCode: string
        /** 商品一件总价 (单价 * 数量（1）* 装数) */
        oneTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料颜色 */
        productInfoNcColour: string
        /** 物料材质 */
        productInfoNcMaterial: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品规格-后台新 */
        productSpec: string
        /** 商品规格型号-后台新 */
        productSpecModel: string
        /** 商品规格名称-后台新 */
        productSpecName: string
        /** 整箱购买数量 */
        quantity: long
        /** 散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 商品上架库存-后台新 */
        shelvesStock: long
        /** 总数量 */
        totalPackage: long
        /** 总价 */
        totalPrice: double
      }[]
      /** 物流地址 */
      deliveryAddress: string
      /** 物流公司 */
      deliveryName: string
      /** 物流电话 */
      deliveryTel: string
      /** 公司名称 */
      enterpriseAbbr: string
      /** id */
      id: long
      /** 防重令牌 */
      orderToken: string
      /** 商品总价 */
      sumPrice: double
    }
    message: string
    success: boolean
  }
  orderCancelAndRefund: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  orderConfirm: {
    code: string
    data: {
      /** 购物车子项信息 */
      cartList: {
        /** 是否删除（0：未删；1：已删） */
        delFlag: string
        /** 提货方式 */
        deliveryType: string
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 购物车id */
        id: long
        /** 是否上下架（0下架，1上架） */
        isOnSale: string
        /** 是否散卖 */
        isRetailSale: string
        /** NC物料编码 */
        ncCode: string
        /** 商品一件总价 (单价 * 数量（1）* 装数) */
        oneTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料颜色 */
        productInfoNcColour: string
        /** 物料材质 */
        productInfoNcMaterial: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品规格-后台新 */
        productSpec: string
        /** 商品规格型号-后台新 */
        productSpecModel: string
        /** 商品规格名称-后台新 */
        productSpecName: string
        /** 整箱购买数量 */
        quantity: long
        /** 散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 商品上架库存-后台新 */
        shelvesStock: long
        /** 总数量 */
        totalPackage: long
        /** 总价 */
        totalPrice: double
      }[]
      /** 物流地址 */
      deliveryAddress: string
      /** 物流公司 */
      deliveryName: string
      /** 物流电话 */
      deliveryTel: string
      /** 公司名称 */
      enterpriseAbbr: string
      /** id */
      id: long
      /** 防重令牌 */
      orderToken: string
      /** 商品总价 */
      sumPrice: double
    }
    message: string
    success: boolean
  }
  orderConfirmToken: {
    code: string
    data: {
      /** 物流地址 */
      deliveryAddress: string
      /** 物流公司 */
      deliveryName: string
      /** 物流电话 */
      deliveryTel: string
      /** 公司名称 */
      enterpriseAbbr: string
      /** id */
      id: long
      /** 防重令牌 */
      orderToken: string
    }
    message: string
    success: boolean
  }
  orderDetail: {
    code: string
    data: {
      /** 实际支付金额 */
      actualPayCost: double
      /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->同意退款，6->退款待确认，8->用户主动取消，9->已完成 */
      afterSaleStatus: number
      /** 取消订单时间 */
      cancelTime: datetime
      /** 收货人地址  */
      consigneeAddress: string
      /** 收货人城市  */
      consigneeCity: string
      /** 收货人公司名称 */
      consigneeCorporateName: string
      /** 收货人地区  */
      consigneeDistrict: string
      /** 收货人手机号码  */
      consigneeMobile: string
      /**  收货人名称  */
      consigneeName: string
      /** 收货人省份  */
      consigneeProvince: string
      /** 收货人固话号码 */
      consigneeTel: string
      /** 物流简称  */
      deliveryCode: string
      /** 物流名称  */
      deliveryName: string
      /** 物流单号  */
      deliveryNo: string
      /** 提货方式  */
      deliveryType: string
      /** 商品描述 */
      description: string
      /** 优惠金额 */
      discountAmount: double
      /** 订单状态0->待发货；1->待收货；2->已完成；3->已取消；4->已下单；5->强制关闭； */
      handleStatus: string
      /** 订单id */
      id: long
      /** 发票编号 */
      invoiceNo: string
      /** 是否可以发起售后，0->否，1->是 */
      isLaunchAfterSale: number
      /** 会员客户分类 */
      memberCustomerType: string
      /** 售后单id */
      orderAfterSaleId: long
      /** 订单编号 */
      orderNo: string
      /** 订单商品列表 */
      orderProductRelationVOList: {
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 用户退回箱数 */
        memberReturnBoxNumber: number
        /** 订单退款id */
        orderBackId: long
        /** 订单id */
        orderId: long
        /** 订单商品售后状态，0->退货退款，3->未售后 */
        orderProductAfterSalesStatus: number
        /** 商品实际支付总价 */
        productActualTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoPackage: long
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品积分 */
        productPoints: double
        /** 商品数量 */
        quantity: long
        /** 商品散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 总数量 */
        totalPackage: string
        /** 总价 */
        totalPrice: double
      }[]
      /** 支付状态：0->待支付；1->已支付；2->退款失败；3->退款成功；4->支付失败；5->退款中； */
      payStatus: string
      /** 订单支付号（只有支付完成后才有此值） */
      payTransactionId: string
      /** 支付类型：0->微信；1->支付宝 */
      payType: string
      /** 支付时间 */
      paymentTime: datetime
      /** 下单时间 */
      postDate: datetime
      /** 下单超时时间 */
      postDateEnd: datetime
      /** 确认时间 */
      receiveTime: datetime
      /** 退款时间 */
      refundTime: datetime
      /** 备注  */
      remark: string
      /** 应当支付金额 */
      shouldPayCost: double
      /** 订单来源 */
      source: string
      /** 商品总装数 */
      sumPackage: long
      /** 商品价格合计 */
      sumPrice: double
      /** 商品总数量-箱 */
      sumQuantity: number
      /** 商品总数量-个 */
      sumRetailQuantity: number
    }
    message: string
    success: boolean
  }
  orderDetailByTransactionId: {
    code: string
    data: {
      /** 实际支付金额 */
      actualPayCost: double
      /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->同意退款，6->退款待确认，8->用户主动取消，9->已完成 */
      afterSaleStatus: number
      /** 取消订单时间 */
      cancelTime: datetime
      /** 收货人地址  */
      consigneeAddress: string
      /** 收货人城市  */
      consigneeCity: string
      /** 收货人公司名称 */
      consigneeCorporateName: string
      /** 收货人地区  */
      consigneeDistrict: string
      /** 收货人手机号码  */
      consigneeMobile: string
      /**  收货人名称  */
      consigneeName: string
      /** 收货人省份  */
      consigneeProvince: string
      /** 收货人固话号码 */
      consigneeTel: string
      /** 物流简称  */
      deliveryCode: string
      /** 物流名称  */
      deliveryName: string
      /** 物流单号  */
      deliveryNo: string
      /** 提货方式  */
      deliveryType: string
      /** 商品描述 */
      description: string
      /** 优惠金额 */
      discountAmount: double
      /** 订单状态0->待发货；1->待收货；2->已完成；3->已取消；4->已下单；5->强制关闭； */
      handleStatus: string
      /** 订单id */
      id: long
      /** 发票编号 */
      invoiceNo: string
      /** 是否可以发起售后，0->否，1->是 */
      isLaunchAfterSale: number
      /** 会员客户分类 */
      memberCustomerType: string
      /** 售后单id */
      orderAfterSaleId: long
      /** 订单编号 */
      orderNo: string
      /** 订单商品列表 */
      orderProductRelationVOList: {
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 用户退回箱数 */
        memberReturnBoxNumber: number
        /** 订单退款id */
        orderBackId: long
        /** 订单id */
        orderId: long
        /** 订单商品售后状态，0->退货退款，3->未售后 */
        orderProductAfterSalesStatus: number
        /** 商品实际支付总价 */
        productActualTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoPackage: long
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品积分 */
        productPoints: double
        /** 商品数量 */
        quantity: long
        /** 商品散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 总数量 */
        totalPackage: string
        /** 总价 */
        totalPrice: double
      }[]
      /** 支付状态：0->待支付；1->已支付；2->退款失败；3->退款成功；4->支付失败；5->退款中； */
      payStatus: string
      /** 订单支付号（只有支付完成后才有此值） */
      payTransactionId: string
      /** 支付类型：0->微信；1->支付宝 */
      payType: string
      /** 支付时间 */
      paymentTime: datetime
      /** 下单时间 */
      postDate: datetime
      /** 下单超时时间 */
      postDateEnd: datetime
      /** 确认时间 */
      receiveTime: datetime
      /** 退款时间 */
      refundTime: datetime
      /** 备注  */
      remark: string
      /** 应当支付金额 */
      shouldPayCost: double
      /** 订单来源 */
      source: string
      /** 商品总装数 */
      sumPackage: long
      /** 商品价格合计 */
      sumPrice: double
      /** 商品总数量-箱 */
      sumQuantity: number
      /** 商品总数量-个 */
      sumRetailQuantity: number
    }
    message: string
    success: boolean
  }
  orderHandleStatus: {
    code: string
    data: {
      /** 售后单数量 */
      afterSaleCount: number
      /** 已完成数量 */
      finishedCount: number
      /** 待发货数量 */
      readyForDeliverCount: number
      /** 待支付数量 */
      readyForPayCount: number
      /** 待收货数量 */
      readyForReceiptCount: number
    }
    message: string
    success: boolean
  }
  orderListByInvoice: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 实际支付金额 */
        actualPayCost: double
        /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->同意退款，6->退款待确认，8->用户主动取消，9->已完成 */
        afterSaleStatus: number
        /** 配送方式:0-场内配送;1-仓库自提;2-自费物流 */
        deliveryType: number
        /** 商品描述 */
        description: string
        /** 订单状态0->待发货；1->待收货；2->已完成；3->已取消；4->已下单；5->强制关闭； */
        handleStatus: string
        /** 订单id */
        id: long
        /** 发票编号 */
        invoiceNo: string
        /** 是否可以发起售后，0->否，1->是 */
        isLaunchAfterSale: number
        /** 会员客户分类 */
        memberCustomerType: string
        /** 售后单id */
        orderAfterSaleId: long
        /** 订单编号 */
        orderNo: string
        /** 订单商品列表 */
        orderProductRelationVOList: {
          /** 展示规格 */
          displayPackage: number
          /** 展示单价 */
          displayUnitPrice: double
          /** 用户退回箱数 */
          memberReturnBoxNumber: number
          /** 订单退款id */
          orderBackId: long
          /** 订单id */
          orderId: long
          /** 订单商品售后状态，0->退货退款，3->未售后 */
          orderProductAfterSalesStatus: number
          /** 商品实际支付总价 */
          productActualTotalPrice: double
          /** 商品id */
          productInfoId: long
          /** 商品名称 */
          productInfoName: string
          /** 物料计量单位 */
          productInfoNcMeterageUnitName: string
          /** 物料装数 */
          productInfoPackage: long
          /** 商品价格 */
          productInfoPrice: double
          /** 商品图片 */
          productInfoProductImg: string
          /** 商品规格 */
          productInfoSpec: string
          /** 商品标题 */
          productInfoTitle: string
          /** 商品积分 */
          productPoints: double
          /** 商品数量 */
          quantity: long
          /** 商品散卖数量 */
          quantityRetail: long
          /** 散卖最少数量 */
          retailMinUnit: long
          /** 散卖价格 */
          retailPrice: double
          /** 总数量 */
          totalPackage: string
          /** 总价 */
          totalPrice: double
        }[]
        /** 支付状态：0->待支付；1->已支付；2->退款失败；3->退款成功；4->支付失败；5->退款中； */
        payStatus: string
        /** 订单支付号（只有支付完成后才有此值） */
        payTransactionId: string
        /** 支付时间 */
        paymentTime: datetime
        /** 下单时间 */
        postDate: datetime
        /** 下单超时时间 */
        postDateEnd: datetime
        /** 确认收货时间 */
        receiveTime: datetime
        /** 应当支付金额 */
        shouldPayCost: double
        /** 订单来源 */
        source: string
        /** 商品总装数 */
        sumPackage: long
        /** 商品价格合计 */
        sumPrice: double
        /** 商品总数量-箱 */
        sumQuantity: number
        /** 商品总数量-个 */
        sumQuantityRetail: number
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  orderListByWeChat: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 实际支付金额 */
        actualPayCost: double
        /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->同意退款，6->退款待确认，8->用户主动取消，9->已完成 */
        afterSaleStatus: number
        /** 配送方式:0-场内配送;1-仓库自提;2-自费物流 */
        deliveryType: number
        /** 商品描述 */
        description: string
        /** 订单状态0->待发货；1->待收货；2->已完成；3->已取消；4->已下单；5->强制关闭； */
        handleStatus: string
        /** 订单id */
        id: long
        /** 发票编号 */
        invoiceNo: string
        /** 是否可以发起售后，0->否，1->是 */
        isLaunchAfterSale: number
        /** 会员客户分类 */
        memberCustomerType: string
        /** 售后单id */
        orderAfterSaleId: long
        /** 订单编号 */
        orderNo: string
        /** 订单商品列表 */
        orderProductRelationVOList: {
          /** 展示规格 */
          displayPackage: number
          /** 展示单价 */
          displayUnitPrice: double
          /** 用户退回箱数 */
          memberReturnBoxNumber: number
          /** 订单退款id */
          orderBackId: long
          /** 订单id */
          orderId: long
          /** 订单商品售后状态，0->退货退款，3->未售后 */
          orderProductAfterSalesStatus: number
          /** 商品实际支付总价 */
          productActualTotalPrice: double
          /** 商品id */
          productInfoId: long
          /** 商品名称 */
          productInfoName: string
          /** 物料计量单位 */
          productInfoNcMeterageUnitName: string
          /** 物料装数 */
          productInfoPackage: long
          /** 商品价格 */
          productInfoPrice: double
          /** 商品图片 */
          productInfoProductImg: string
          /** 商品规格 */
          productInfoSpec: string
          /** 商品标题 */
          productInfoTitle: string
          /** 商品积分 */
          productPoints: double
          /** 商品数量 */
          quantity: long
          /** 商品散卖数量 */
          quantityRetail: long
          /** 散卖最少数量 */
          retailMinUnit: long
          /** 散卖价格 */
          retailPrice: double
          /** 总数量 */
          totalPackage: string
          /** 总价 */
          totalPrice: double
        }[]
        /** 支付状态：0->待支付；1->已支付；2->退款失败；3->退款成功；4->支付失败；5->退款中； */
        payStatus: string
        /** 订单支付号（只有支付完成后才有此值） */
        payTransactionId: string
        /** 支付时间 */
        paymentTime: datetime
        /** 下单时间 */
        postDate: datetime
        /** 下单超时时间 */
        postDateEnd: datetime
        /** 确认收货时间 */
        receiveTime: datetime
        /** 应当支付金额 */
        shouldPayCost: double
        /** 订单来源 */
        source: string
        /** 商品总装数 */
        sumPackage: long
        /** 商品价格合计 */
        sumPrice: double
        /** 商品总数量-箱 */
        sumQuantity: number
        /** 商品总数量-个 */
        sumQuantityRetail: number
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  submitOrder: {
    code: string
    data: {
      /** 优惠券面值 */
      couponValue: double
      /** 订单号 */
      orderNo: string
      /** 下单时间 */
      postDate: datetime
      /** 应付金额 */
      shouldPayCost: double
    }
    message: string
    success: boolean
  }
  findOrderInvoiceDetail: {
    code: string
    data: {
      /** 申请开票日期 */
      applyInvoiceDate: datetime
      /** 税号 */
      dutyParagraph: string
      /** 发票Id */
      id: long
      /** 发票抬头 */
      invoiceHeader: string
      /** 抬头类型，0->企业;1->个人 */
      invoiceHeaderType: number
      /** 发票编号 */
      invoiceNo: string
      /** 开票金额 */
      invoicePrice: double
      /** 发票状态，0->申请中;1->已开票 */
      invoiceStatus: number
      /**   发票类型，0->电子普通发票;1->增值税电子专用发票 */
      invoiceType: number
      /** 包含订单个数 */
      orderNumber: number
    }
    message: string
    success: boolean
  }
  findOrderInvoiceList: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 申请开票日期 */
        applyInvoiceDate: datetime
        /** 税号 */
        dutyParagraph: string
        /** 发票Id */
        id: long
        /** 发票抬头 */
        invoiceHeader: string
        /** 抬头类型，0->企业;1->个人 */
        invoiceHeaderType: number
        /** 发票编号 */
        invoiceNo: string
        /** 开票金额 */
        invoicePrice: double
        /** 发票状态，0->申请中;1->已开票 */
        invoiceStatus: number
        /**   发票类型，0->电子普通发票;1->增值税电子专用发票 */
        invoiceType: number
        /** 包含订单个数 */
        orderNumber: number
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  insertOrderCommonInvoiceByBusiness: {
    code: string
    data: {
      /** 申请开票日期 */
      applyInvoiceDate: datetime
      /** 税号 */
      dutyParagraph: string
      /** 发票Id */
      id: long
      /** 发票抬头 */
      invoiceHeader: string
      /** 抬头类型，0->企业;1->个人 */
      invoiceHeaderType: number
      /** 发票编号 */
      invoiceNo: string
      /** 开票金额 */
      invoicePrice: double
      /** 发票状态，0->申请中;1->已开票 */
      invoiceStatus: number
      /**   发票类型，0->电子普通发票;1->增值税电子专用发票 */
      invoiceType: number
      /** 包含订单个数 */
      orderNumber: number
    }
    message: string
    success: boolean
  }
  insertOrderCommonInvoiceByPerson: {
    code: string
    data: {
      /** 申请开票日期 */
      applyInvoiceDate: datetime
      /** 税号 */
      dutyParagraph: string
      /** 发票Id */
      id: long
      /** 发票抬头 */
      invoiceHeader: string
      /** 抬头类型，0->企业;1->个人 */
      invoiceHeaderType: number
      /** 发票编号 */
      invoiceNo: string
      /** 开票金额 */
      invoicePrice: double
      /** 发票状态，0->申请中;1->已开票 */
      invoiceStatus: number
      /**   发票类型，0->电子普通发票;1->增值税电子专用发票 */
      invoiceType: number
      /** 包含订单个数 */
      orderNumber: number
    }
    message: string
    success: boolean
  }
  insertOrderMajorInvoiceByBusiness: {
    code: string
    data: {
      /** 申请开票日期 */
      applyInvoiceDate: datetime
      /** 税号 */
      dutyParagraph: string
      /** 发票Id */
      id: long
      /** 发票抬头 */
      invoiceHeader: string
      /** 抬头类型，0->企业;1->个人 */
      invoiceHeaderType: number
      /** 发票编号 */
      invoiceNo: string
      /** 开票金额 */
      invoicePrice: double
      /** 发票状态，0->申请中;1->已开票 */
      invoiceStatus: number
      /**   发票类型，0->电子普通发票;1->增值税电子专用发票 */
      invoiceType: number
      /** 包含订单个数 */
      orderNumber: number
    }
    message: string
    success: boolean
  }
  checkWeChatPayStatus: {
    code: string
    data: {
      /** appId */
      appId: string
      /** 噪音字符串 */
      nonceStr: string
      /** 参数包 */
      package: string
      /** 支付加密后签名 */
      paySign: string
      /** 签名加密类型 */
      signType: string
      /** 时间戳 */
      timeStamp: string
    }
    message: string
    success: boolean
  }
  weChatPay: {
    code: string
    data: {
      /** appId */
      appId: string
      /** 噪音字符串 */
      nonceStr: string
      /** 参数包 */
      package: string
      /** 支付加密后签名 */
      paySign: string
      /** 签名加密类型 */
      signType: string
      /** 时间戳 */
      timeStamp: string
    }
    message: string
    success: boolean
  }
  categoryList: {
    code: string
    data: {
      /** 子分类 */
      children: unknown[]
      /** 分类id */
      id: number
      /** 分类图片 */
      img: string
      /** 分类名称 */
      name: string
    }[]
    message: string
    success: boolean
  }
  getCategoryRelationList: {
    code: string
    data: {
      /** 分类Id */
      categoryId: number
      /** 分类主图 */
      categoryImg: string
      /** 分类名称 */
      categoryName: string
      /** 价格 */
      price: double
      /** 商品Id */
      productId: long
    }[]
    message: string
    success: boolean
  }
  findProductByCategoryIdTwo: {
    code: string
    data: {
      /** 商品分类id */
      categoryId: number
      /** 商品分类名称 */
      categoryName: string
      /** 分类四级商品 */
      productLv4ResponseVo: {
        /** 商品分类id */
        categoryId: number
        /** 商品分类图片 */
        categoryImg: string
        /** 商品分类名称 */
        categoryName: string
        /** 展示单价 */
        displayUnitPrice: double
        /** 分类父级id */
        fatherId: number
        /** 商品Id */
        productInfoId: number
        /** 产品名称 */
        productName: string
        /** 产品包装个数 */
        productPackage: number
        /** 产品价格 */
        productPrice: double
        /** 规格型号 */
        productSpecModel: string
        /** 规格型号集合 */
        productSpecModelList: string[]
        /** 产品标题 */
        productTitle: string
      }[]
    }[]
    message: string
    success: boolean
  }
  getRecommendProductList: {
    code: string
    data: {
      /** 商品分类Id */
      categoryId: number
      /** 商品分类名称 */
      categoryName: string
      /** 展示单价 */
      displayUnitPrice: double
      /** 推荐商品id */
      id: long
      /** 物料终端商价格(三号价) */
      ncRetailPrice: double
      /** 商品Id */
      productId: long
      /** 商品图片 */
      productImg: string
      /** 规格尺寸 */
      productSpec: string
      /** 规格名称 */
      productSpecName: string
      /** 商品编号 */
      productSpecNumber: string
      /** 推荐图片 */
      recommendImg: string
      /** 序号 */
      sort: number
    }[]
    message: string
    success: boolean
  }
  productDetail: {
    code: string
    /** 商品详情 */
    data: {
      /** 分类Id */
      categoryId: number
      /** 是否删除 */
      delFlag: string
      /** 图片详情 */
      description: string
      /** 展示规格 */
      displayPackage: number
      /** 展示单价 */
      displayUnitPrice: double
      /** 商品Id */
      id: long
      /** 图片列表 */
      imageList: string[]
      /** 是否上下架（0下架，1上架） */
      isOnSale: string
      /** 商品市场价 */
      marketPrice: double
      /** 商品名称 */
      name: string
      /** 第三级-商品分类 */
      ncCategory: string
      /** 商品NC编号 */
      ncCode: string
      /** 材质 */
      ncMaterial: string
      /** nc 计量单位名称 */
      ncMeterageUnitName: string
      /** 商品一箱的件数 */
      ncPackage: string
      /** 商品毛重 */
      ncWeight: string
      /** 外箱规格 */
      outerBoxSpec: string
      /** 商品图片 */
      productImg: string
      /** 商品价格 */
      productPrice: double
      /** 商品规格 */
      productSpec: string
      /** 商品规格型号 */
      productSpecModel: string
      /** 商品规格名称 */
      productSpecName: string
      /** 商品规格编号 */
      productSpecNumber: string
      /** 商品上架库存-后台新 */
      shelvesStock: long
      /** 规格选择 */
      specList: {
        /** 展示排序id */
        displaySortId: number
        /** 商品Id */
        id: long
        /** 商品规格标题 */
        littletitle: string
        /** 商品NC编号 */
        ncCode: string
        /** 外箱规格 */
        outerBoxSpec: string
        /** 商品图片 */
        productImg: string
        /** 商品规格 */
        productSpec: string
        /** 商品规格型号 */
        productSpecModel: string
        /** 商品规格名称 */
        productSpecName: string
        /** 商品规格编号 */
        productSpecNumber: string
        /** 上架库存 */
        putawayStock: long
        /** 商品标题 */
        title: string
      }[]
      /** 商品标题 */
      title: string
    }
    message: string
    success: boolean
  }
  searchProduct: {
    code: string
    data: {
      /** 商品分类id */
      categoryId: number
      /** 商品分类图片 */
      categoryImg: string
      /** 商品分类名称 */
      categoryName: string
      /** 展示单价 */
      displayUnitPrice: double
      /** 分类父级id */
      fatherId: number
      /** 商品Id */
      productInfoId: number
      /** 产品名称 */
      productName: string
      /** 产品包装个数 */
      productPackage: number
      /** 产品价格 */
      productPrice: double
      /** 规格型号 */
      productSpecModel: string
      /** 规格型号集合 */
      productSpecModelList: string[]
      /** 产品标题 */
      productTitle: string
    }[]
    message: string
    success: boolean
  }
  addTheGoodsToTheCart: {
    code: string
    data: {
      itemVO: {
        /** 是否删除（0：未删；1：已删） */
        delFlag: string
        /** 提货方式 */
        deliveryType: string
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 购物车id */
        id: long
        /** 是否上下架（0下架，1上架） */
        isOnSale: string
        /** 是否散卖 */
        isRetailSale: string
        /** NC物料编码 */
        ncCode: string
        /** 商品一件总价 (单价 * 数量（1）* 装数) */
        oneTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料颜色 */
        productInfoNcColour: string
        /** 物料材质 */
        productInfoNcMaterial: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品规格-后台新 */
        productSpec: string
        /** 商品规格型号-后台新 */
        productSpecModel: string
        /** 商品规格名称-后台新 */
        productSpecName: string
        /** 整箱购买数量 */
        quantity: long
        /** 散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 商品上架库存-后台新 */
        shelvesStock: long
        /** 总数量 */
        totalPackage: long
        /** 总价 */
        totalPrice: double
      }[]
      /** 购物车子项信息 */
      items: {
        delFlag: string
        deliveryType: string
        id: long
        isOnSale: string
        isRetailSale: string
        oneTotalPrice: double
        productInfoId: long
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        productInfoPrice: double
        quantity: long
        quantityRetail: long
        retailMinUnit: long
        retailPrice: double
        shelvesStock: long
        totalPackage: long
        totalPrice: double
      }[]
      sumPackage: long
      /** 商品总价 */
      sumPrice: double
      /** 商品总数量-箱 */
      sumQuantity: long
      /** 商品总零售量-个 */
      sumQuantityRetail: long
    }
    message: string
    success: boolean
  }
  comeAgainCart: {
    code: string
    data: {
      itemVO: {
        /** 是否删除（0：未删；1：已删） */
        delFlag: string
        /** 提货方式 */
        deliveryType: string
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 购物车id */
        id: long
        /** 是否上下架（0下架，1上架） */
        isOnSale: string
        /** 是否散卖 */
        isRetailSale: string
        /** NC物料编码 */
        ncCode: string
        /** 商品一件总价 (单价 * 数量（1）* 装数) */
        oneTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料颜色 */
        productInfoNcColour: string
        /** 物料材质 */
        productInfoNcMaterial: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品规格-后台新 */
        productSpec: string
        /** 商品规格型号-后台新 */
        productSpecModel: string
        /** 商品规格名称-后台新 */
        productSpecName: string
        /** 整箱购买数量 */
        quantity: long
        /** 散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 商品上架库存-后台新 */
        shelvesStock: long
        /** 总数量 */
        totalPackage: long
        /** 总价 */
        totalPrice: double
      }[]
      /** 购物车子项信息 */
      items: {
        delFlag: string
        deliveryType: string
        id: long
        isOnSale: string
        isRetailSale: string
        oneTotalPrice: double
        productInfoId: long
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        productInfoPrice: double
        quantity: long
        quantityRetail: long
        retailMinUnit: long
        retailPrice: double
        shelvesStock: long
        totalPackage: long
        totalPrice: double
      }[]
      sumPackage: long
      /** 商品总价 */
      sumPrice: double
      /** 商品总数量-箱 */
      sumQuantity: long
      /** 商品总零售量-个 */
      sumQuantityRetail: long
    }
    message: string
    success: boolean
  }
  deleteShoppingCar: {
    code: string
    data: Record<string, never>
    message: string
    success: boolean
  }
  getCartList: {
    code: string
    data: {
      itemVO: {
        /** 是否删除（0：未删；1：已删） */
        delFlag: string
        /** 提货方式 */
        deliveryType: string
        /** 展示规格 */
        displayPackage: number
        /** 展示单价 */
        displayUnitPrice: double
        /** 购物车id */
        id: long
        /** 是否上下架（0下架，1上架） */
        isOnSale: string
        /** 是否散卖 */
        isRetailSale: string
        /** NC物料编码 */
        ncCode: string
        /** 商品一件总价 (单价 * 数量（1）* 装数) */
        oneTotalPrice: double
        /** 商品id */
        productInfoId: long
        /** 商品名称 */
        productInfoName: string
        /** 物料颜色 */
        productInfoNcColour: string
        /** 物料材质 */
        productInfoNcMaterial: string
        /** 物料计量单位 */
        productInfoNcMeterageUnitName: string
        /** 物料装数 */
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        /** 商品价格 */
        productInfoPrice: double
        /** 商品图片 */
        productInfoProductImg: string
        /** 商品规格 */
        productInfoSpec: string
        /** 商品标题 */
        productInfoTitle: string
        /** 商品规格-后台新 */
        productSpec: string
        /** 商品规格型号-后台新 */
        productSpecModel: string
        /** 商品规格名称-后台新 */
        productSpecName: string
        /** 整箱购买数量 */
        quantity: long
        /** 散卖数量 */
        quantityRetail: long
        /** 散卖最少数量 */
        retailMinUnit: long
        /** 散卖价格 */
        retailPrice: double
        /** 商品上架库存-后台新 */
        shelvesStock: long
        /** 总数量 */
        totalPackage: long
        /** 总价 */
        totalPrice: double
      }[]
      /** 购物车子项信息 */
      items: {
        delFlag: string
        deliveryType: string
        id: long
        isOnSale: string
        isRetailSale: string
        oneTotalPrice: double
        productInfoId: long
        productInfoNcPackage: long
        productInfoNcRetailPrice: double
        productInfoPrice: double
        quantity: long
        quantityRetail: long
        retailMinUnit: long
        retailPrice: double
        shelvesStock: long
        totalPackage: long
        totalPrice: double
      }[]
      sumPackage: long
      /** 商品总价 */
      sumPrice: double
      /** 商品总数量-箱 */
      sumQuantity: long
      /** 商品总零售量-个 */
      sumQuantityRetail: long
    }
    message: string
    success: boolean
  }
  selectProductSumPrice: {
    code: string
    data: {
      /** 商品总价 */
      sumPrice: double
      /** 商品总数量-箱 */
      sumQuantity: long
    }
    message: string
    success: boolean
  }
  updateShoppingCar: {
    code: string
    data: {
      /** 商品id */
      productInfoId: long
      /** 商品总数量-箱 */
      sumQuantity: long
      /** 总数量 */
      totalPackage: long
      /** 总价 */
      totalPrice: double
    }
    message: string
    success: boolean
  }
  productShelvesStockActualTime: {
    code: string
    data: {
      /** 商品信息id */
      productInfoId: long
      /** 上架库存 */
      shelvesStock: long
    }
    message: string
    success: boolean
  }
  getRewardsPointsDetail: {
    code: string
    data: {
      couponExp: {
        /** 有效天数 */
        expDays: long
        /** 有效日期区间 */
        ranges: {
          /** 有效期截止日期 */
          endTime: datetime
          /** 有效期开始日期 */
          startTime: datetime
        }[]
      }
      /** 优惠券有效期 */
      couponExpStr: string
      /** 优惠券id */
      couponId: string
      /** 优惠券名称 */
      couponName: string
      /** 优惠券编号 */
      couponNo: string
      /** 使用规则 */
      couponRule: string
      /** 优惠券副片 */
      couponViceIMG: string
      /** 所需积分 */
      integrate: long
      /** 月兑次数 */
      receiveAmount: long
      /** 兑换方案Id */
      rewardsPointsId: long
      /** 兑换方案编号 */
      rewardsPointsNo: string
    }
    message: string
    success: boolean
  }
  getRewardsPointsList: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        couponExp: {
          /** 有效天数 */
          expDays: long
          /** 有效日期区间 */
          ranges: {
            /** 有效期截止日期 */
            endTime: datetime
            /** 有效期开始日期 */
            startTime: datetime
          }[]
        }
        /** 优惠券有效期 */
        couponExpStr: string
        /** 优惠券id */
        couponId: string
        /** 优惠券图片 */
        couponMainIMG: string
        /** 优惠券名称 */
        couponName: string
        /** 优惠券编号 */
        couponNo: string
        /** 所需积分 */
        integrate: long
        /** 兑换方案Id */
        rewardsPointsId: long
        /** 兑换方案编号 */
        rewardsPointsNo: string
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  getRewardsPointsReceiveRecordDetail: {
    code: string
    data: {
      /** 优惠券id */
      couponId: string
      /** 优惠券名称 */
      couponName: string
      /** 优惠券编号 */
      couponNo: string
      /** 使用规则 */
      couponRule: string
      /** 优惠券副片 */
      couponViceIMG: string
      /** 兑换积分 */
      integrate: long
      /** 是否使用: 0->未使用;1->已使用 */
      isUsed: number
      /** 积分兑换时间 */
      receiveTime: datetime
      /** 兑换记录Id */
      rewardsPointsReceiveRecordId: long
      /** 兑换记录编号 */
      rewardsPointsReceiveRecordNo: string
    }
    message: string
    success: boolean
  }
  getRewardsPointsReceiveRecordList: {
    code: string
    data: {
      pageIndex: long
      pageSize: long
      result: {
        /** 优惠券id */
        couponId: string
        /** 优惠券名称 */
        couponName: string
        /** 优惠券编号 */
        couponNo: string
        /** 优惠券副片 */
        couponViceIMG: string
        /** 消耗积分 */
        integrate: long
        /** 是否使用: 0->未使用;1->已使用 */
        isUsed: number
        /** 积分兑换时间 */
        receiveTime: datetime
        /** 兑换方案Id */
        rewardsPointsId: long
        /** 兑换方案编号 */
        rewardsPointsNo: string
        /** 兑换记录Id */
        rewardsPointsReceiveRecordId: long
        /** 兑换记录编号 */
        rewardsPointsReceiveRecordNo: string
      }[]
      totalCount: long
      totalPageCount: long
    }
    message: string
    success: boolean
  }
  rewardsPointsReceiveCoupon: {
    code: string
    data: long
    message: string
    success: boolean
  }
}
