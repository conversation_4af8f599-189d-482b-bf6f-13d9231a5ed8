<template>
  <button
    class="v-button"
    :style="style"
    :class="{
      disabled: disabled,
      shadow: shadow,
      primary: type === 'primary',
      danger: type === 'danger'
    }"
    :disabled="disabled"
    :open-type="openType"
    :send-message-title="sendMessageTitle"
    :send-message-path="sendMessagePath"
    :send-message-img="sendMessageImg"
    :show-message-card="showMessageCard"
    @click="clickHandle"
    @contact="contactHandle"
    @getphonenumber="getphonenumberHandle"
    @error="errorHandle"
    @opensetting="opensettingHandle"
    @launchapp="launchappHandle"
    @chooseavatar="chooseavatarHandle"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
import { isString } from "lodash-es"

const props = withDefaults(
  defineProps<{
    disabled?: boolean
    type?: string
    height?: string
    width?: string
    margin?: string
    padding?: string
    fontSize?: string
    color?: string
    border?: boolean | string
    borderColor?: string
    backgroundImage?: string
    backgroundColor?: string
    round?: boolean | string
    shadow?: boolean | string
    openType?: string
    sendMessageTitle?: string
    sendMessagePath?: string
    sendMessageImg?: string
    showMessageCard?: boolean
  }>(),
  {
    disabled: false,
    type: undefined,
    height: "80rpx",
    width: "100%",
    margin: "0",
    padding: "0 20rpx",
    fontSize: "32rpx",
    color: undefined,
    border: true,
    borderColor: undefined,
    backgroundImage: undefined,
    backgroundColor: undefined,
    round: true,
    shadow: false,
    openType: undefined,
    sendMessageTitle: undefined,
    sendMessagePath: undefined,
    sendMessageImg: undefined,
    showMessageCard: false
  }
)

interface Emits {
  (event: "click"): void
  (event: "contact"): void
  (event: "getphonenumber", value: ButtonEvent.GetPhoneNumber): void
  (event: "error"): void
  (event: "opensetting"): void
  (event: "launchapp"): void
  (event: "chooseavatar"): void
}
const emits = defineEmits<Emits>()

const clickHandle = () => emits("click")
const contactHandle = () => emits("contact")
const getphonenumberHandle = (event: ButtonEvent.GetPhoneNumber) =>
  emits("getphonenumber", event)
const errorHandle = () => emits("error")
const opensettingHandle = () => emits("opensetting")
const launchappHandle = () => emits("launchapp")
const chooseavatarHandle = () => emits("chooseavatar")

const type = toRef(props, "type")
const height = toRef(props, "height")
const width = toRef(props, "width")
const fontSize = toRef(props, "fontSize")
const color = toRef(props, "color")
const margin = toRef(props, "margin")
const padding = toRef(props, "padding")
const border = toRef(props, "border")
const borderColor = toRef(props, "borderColor")
const backgroundImage = toRef(props, "backgroundImage")
const backgroundColor = toRef(props, "backgroundColor")
const round = toRef(props, "round")
const shadow = toRef(props, "shadow")

const style = computed(() => {
  const styleList = [] as string[]
  if (height.value) {
    styleList.push(`height: ${height.value}; line-height: ${height.value}`)
  }
  if (width.value) styleList.push(`width: ${width.value}`)
  if (fontSize.value) styleList.push(`font-size: ${fontSize.value}`)
  if (margin.value) styleList.push(`margin: ${margin.value}`)
  if (padding.value) styleList.push(`padding: ${padding.value}`)

  switch (true) {
    case !!color.value:
      styleList.push(`color: ${color.value}`)
      break
  }

  switch (true) {
    case isString(border.value):
      styleList.push(`border: ${border.value}`)
      break
    case border.value === false:
      styleList.push("border: none")
      break
    case !!borderColor.value:
      styleList.push(`border: 1px solid ${borderColor.value}`)
      break
  }

  switch (true) {
    case !!backgroundImage.value:
      styleList.push(`background-image: ${backgroundImage.value}`)
      break
    case !!backgroundColor.value:
      styleList.push(`background-color: ${backgroundColor.value}`)
      break
  }

  switch (true) {
    case isString(round.value):
      styleList.push(`border-radius: ${round.value}`)
      break
    case round.value:
      styleList.push("border-radius: 100vh")
      break
  }

  switch (true) {
    case isString(shadow.value):
      styleList.push(`box-shadow: ${shadow.value}`)
      break
  }
  return styleList.join(";")
})
</script>

<style lang="scss" scoped>
.v-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  color: #666666;
  border: 1px solid #999999;
  background-color: transparent;
  border-radius: 0;
  &.primary {
    color: white;
    background-color: rgb(0, 91, 171);
    border: 1px solid rgb(0, 91, 171);
  }
  &.danger {
    color: white;
    background-color: rgb(240, 102, 113);
    border: 1px solid rgb(240, 102, 113);
  }
  &.shadow {
    box-shadow: 0 5px 12px rgba(35, 24, 21, 0.3);
    &.primary {
      box-shadow: 0 5px 12px rgba(0, 91, 171, 0.3);
    }
    &.danger {
      box-shadow: 0 5px 12px rgba(240, 102, 113, 0.3);
    }
  }
  &::after {
    display: none;
  }
  &.disabled {
    // &.primary {
    //   background-color: rgb(148, 186, 218);
    //   border: 1px solid rgb(148, 186, 218);
    // }
    // &.danger {
    //   background-color: rgb(212, 146, 151);
    //   border: 1px solid rgb(212, 146, 151);
    // }
    background-color: rgb(204, 204, 204);
    border: 1px solid rgb(204, 204, 204);
  }
}
</style>
