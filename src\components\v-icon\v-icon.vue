<template>
  <image
    class="v-icon"
    :src="active ? activeSrc : src"
    :mode="mode"
    :style="{
      height: height || size,
      width: width || size,
      padding: padding,
      margin: margin,
      marginLeft: marginLeft,
      marginTop: marginTop,
      marginRight: marginRight,
      marginBottom: marginBottom
    }"
    @click="$emit('click')"
  />
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    active?: boolean
    src: string
    activeSrc?: string
    size?: string
    height?: string
    width?: string
    mode?: string
    padding?: string
    margin?: string
    marginLeft?: string
    marginTop?: string
    marginRight?: string
    marginBottom?: string
  }>(),
  {
    active: false,
    src: "",
    activeSrc: "",
    size: "20rpx",
    height: undefined,
    width: undefined,
    mode: "scaleToFill",
    padding: "",
    margin: "",
    marginLeft: "",
    marginTop: "",
    marginRight: "",
    marginBottom: ""
  }
)

interface Emits {
  (event: "click"): void
}
defineEmits<Emits>()
</script>

<style lang="scss" scoped>
.v-icon {
  display: block;
}
</style>
