<template>
  <view class="step-container">
    <v-button
      v-if="checkbox"
      type="primary"
      height="94rpx"
      width="540rpx"
      font-size="32rpx"
      margin="0 0 10rpx"
      shadow
      open-type="getPhoneNumber"
      @getphonenumber="getPhoneNumber"
    >
      手机号快捷登录
    </v-button>

    <v-button
      v-if="!props.checkbox"
      type="primary"
      height="94rpx"
      width="540rpx"
      font-size="32rpx"
      margin="0 0 10rpx"
      shadow
      @click="clickGetPhoneNumberBtn"
    >
      手机号快捷登录
    </v-button>
    <v-button
      class="button"
      height="94rpx"
      color="#999999"
      font-size="32rpx"
      border="none"
      @click="otherPhoneNumber"
    >
      其他号码登录/注册
    </v-button>
  </view>
</template>

<script setup lang="ts">
import { refreshToken } from "@/utils/auth/token"
import { str2num } from "@/utils/type-transfer"

const props = defineProps<{
  checkbox: boolean
}>()
const checkbox = toRef(props, "checkbox")

interface Emits {
  (event: "update", val: number): void
  (event: "checked"): void
}
const emits = defineEmits<Emits>()

const { safeNavigateBack } = usePageLayout()

const clickGetPhoneNumberBtn = async () => {
  const result = await new Promise<boolean>((resolve) => {
    uni.showModal({
      title: "确认",
      content: "已阅读并同意用户协议和隐私政策",
      success: (res) => resolve(res.confirm)
    })
  })
  if (!result) return
  emits("checked")
}
const getPhoneNumber = async (event: GetPhoneNumberEvent) => {
  if (!event?.detail?.code) return
  uni.showLoading({ title: "加载中", mask: true })
  const openIdCode = await new Promise<string>((resolve) => {
    uni.login({
      success: (res) => resolve(res.code),
      fail: () => resolve("")
    })
  })
  if (!openIdCode) return
  const response = await $api.signUpOrRegisterByCode({
    wxCode: event.detail.code,
    code: openIdCode
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      if (response.code === "23014") {
        uni.showModal({
          title: "您的账号异常",
          content: "请通过公众号联系客服处理",
          showCancel: false
        })
      } else {
        refreshToken(response.data.token, str2num(response.data.tokenExpires))
        safeNavigateBack()
        uni.$emit("loginFinish")
      }
  }
}

const otherPhoneNumber = () => {
  emits("update", 1)
  setTimeout(() => {
    emits("update", 2)
  }, 300)
}
</script>

<style lang="scss" scoped>
.step-container {
  padding: 60rpx 12px 0;
}
</style>
