<template>
  <view>
    <view class="page-background-white" />
    <view class="page-header hide-shadow">
      <v-navbar title="商品搜索" back />
    </view>
    <scroll-view scroll-y :style="{ height: `${pageHeight}px` }">
      <view class="search-input-container">
        <v-search-input v-model="searchInputValue" focus @confirm="searchConfirm" />
      </view>
      <v-search-input-history ref="historyRef" @click="clickSearchHistory" />
    </scroll-view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { pageHeight } = usePageLayout()

/** 搜索栏 */
const searchInputValue = ref("")
const searchConfirm = () => {
  if (!searchInputValue.value) return
  uni.redirectTo({
    url: `/package-product/product/product-search?keyword=${searchInputValue.value}`
  })
}

/** 搜搜历史 */
const historyRef = ref()
const clickSearchHistory = (searchHistory: string) => {
  searchInputValue.value = searchHistory
  searchConfirm()
}
</script>

<style lang="scss" scoped>
.search-input-container {
  padding: 10rpx 24rpx;
  .search-input {
    background-color: #f5f5f5;
    border-radius: 100vh;
    padding: 0 8rpx;
    height: 70rpx;
    box-sizing: border-box;
  }
}
</style>
