/** https://uniapp.dcloud.net.cn/collocation/pages.html */
{
	"pages": [
		{
			"path": "pages/index/index-page",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/product/product-page",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/product/product-search-input-page",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/cart/cart-page",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/cart/cart-list",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/user/user-page",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/user/user-config",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/user/user-info",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/user/sign-up",
			"style": {
				"disableScroll": true
			}
		},
		{
			"path": "pages/customer/customer-service",
			"style": {
				"disableScroll": true
			}
		}
	],
	"subPackages": [
		{
			"root": "package-activity",
			"pages": [
				{
					"path": "activity/activity-page",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "activity/activity-details-share",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "activity/activity-details",
					"style": {
						"disableScroll": true
					}
				}
			]
		},
		{
			"root": "package-aftersales",
			"pages": [
				{
					"path": "aftersales/aftersales-delivery-bill",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "aftersales/aftersales-details",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "aftersales/aftersales-page",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "aftersales/aftersales-refund",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "aftersales/aftersales-result",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "aftersales/aftersales-select-product",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "aftersales/aftersales-select-type",
					"style": {
						"disableScroll": true
					}
				}
			]
		},
		{
			"root": "package-coupon",
			"pages": [
				{
					"path": "coupon/hisitory-coupon",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "coupon/my-coupon",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "coupon/receive-coupon",
					"style": {
						"disableScroll": true
					}
				}
			]
		},
		{
			"root": "package-invoice",
			"pages": [
				{
					"path": "invoice/invoice-detail",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "invoice/invoice-form",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "invoice/invoice-msg-management",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "invoice/invoice-page",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "invoice/invoicing-form",
					"style": {
						"disableScroll": true
					}
				}
			]
		},
		{
			"root": "package-order",
			"pages": [
				{
					"path": "order/order-confirm",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "order/order-details",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "order/order-page",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "order/order-pay",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "order/pay-result",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "order/refund-page",
					"style": {
						"disableScroll": true
					}
				}
			]
		},
		{
			"root": "package-points",
			"pages": [
				{
					"path": "points/points-center-exchange",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "points/points-center",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "points/points-exchange-record-details",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "points/points-page",
					"style": {
						"disableScroll": true
					}
				}
			]
		},
		{
			"root": "package-product",
			"pages": [
				{
					"path": "product/product-details",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "product/product-search",
					"style": {
						"disableScroll": true
					}
				}
			]
		},
		{
			"root": "package-user",
			"pages": [
				{
					"path": "user/address-create-and-edit",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "user/address-list",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "user/corporate-create-and-edit",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "user/corporate-list",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "user/customer-service",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "user/customer-service-detail",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "user/user-collection",
					"style": {
						"disableScroll": true
					}
				},
				{
					"path": "user/user-feedback",
					"style": {
						"disableScroll": true
					}
				}
			]
		}
	],
	"tabBar": {
		"color": "#FFF",
		"selectedColor": "#FFF",
		"backgroundColor": "#FFFFFF",
		"borderStyle": "white",
		"list": [
			{
				"pagePath": "pages/index/index-page",
				"iconPath": "static/icons/empty.png",
				"selectedIconPath": "static/icons/empty.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/product/product-page",
				"iconPath": "static/icons/empty.png",
				"selectedIconPath": "static/icons/empty.png",
				"text": "分类"
			},
			{
				"pagePath": "pages/cart/cart-page",
				"iconPath": "static/icons/empty.png",
				"selectedIconPath": "static/icons/empty.png",
				"text": "购物车"
			},
			{
				"pagePath": "pages/user/user-page",
				"iconPath": "static/icons/empty.png",
				"selectedIconPath": "static/icons/empty.png",
				"text": "我的"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": " ",
		"navigationBarBackgroundColor": "#FFF",
		"backgroundColor": "#F5F5F5",
		"navigationStyle": "custom",
		"mp-alipay": {
			"titlePenetrate": "YES",
			"transparentTitle": "always"
		}
	}
}