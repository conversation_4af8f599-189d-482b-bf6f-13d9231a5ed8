<template>
  <view>
    <view class="page-header">
      <v-navbar title="确认订单" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <view class="v-block">
        <view class="deliver-info flex-center">
          <view class="deliver-label required">
            配送方式
          </view>
          <view class="flex-1">
            <v-select
              v-model="isShowPicker"
              :value="pickerLabel"
              placeholder="请选择配送方式"
              text-align="right"
            />
          </view>
        </view>
        <template v-if="deliveryType === 1 && webName === 'welshine'">
          <view class="divider" />
          <view class="deliver-info">
            <view style="padding-bottom: 16rpx; padding-left: 16rpx">
              自提地址
            </view>
            <view>
              广东省 广州市 海珠区 江南大道南411号 香江家居C馆(海珠店)
              5楼惠而信仓库 (15013386204)
            </view>
          </view>
        </template>
        <template v-if="deliveryType === 1 && webName === 'cheerray'">
          <view class="divider" />
          <view class="deliver-info">
            <view style="padding-bottom: 16rpx; padding-left: 16rpx">
              自提地址
            </view>
            <view>
              广东省 广州市 海珠区 江南大道南411号 香江家居C馆(海珠店)
              5楼惠而信仓库 (15013386204)
            </view>
          </view>
        </template>
        <template v-if="deliveryType === 1 && webName === 'sh.welshine'">
          <view class="divider" />
          <view class="deliver-info">
            <view style="padding-bottom: 16rpx; padding-left: 16rpx">
              自提地址
            </view>
            <view>
              上海市松江区申徐二路东侧8号顺丰旁边，倪主管18964661972
            </view>
          </view>
        </template>
      </view>
      <view v-if="loading" class="v-block">
        <v-loading-block padding="42rpx 0" />
      </view>
      <template v-else-if="deliveryType !== 1">
        <view v-if="!address" class="v-block">
          <view class="address-select" @click="navigateToAddressList">
            点击选择收货地址
          </view>
        </view>
        <view v-else class="v-block" @click="navigateToAddressList">
          <view class="address-info flex-center-between">
            <view class="flex-center">
              <v-tag
                v-if="address.isDefault === 1"
                background-color="rgba(230,0,18,0.6)"
                margin="0 8rpx 0 0"
              >
                默认
              </v-tag>
              <view class="address-name text-32 text-bold text-1-row">
                {{ address.name }}
              </view>
              <view class="text-32 text-bold text-1-row">
                {{ address.mobile }}
              </view>
            </view>
            <v-icon size="24rpx" src="/static/icons/right-arrow.png" />
          </view>
          <view class="address-info">
            <view class="text-sub text-1-row">
              {{ address.address }}
            </view>
          </view>
        </view>
      </template>
      <view v-if="deliveryType === 2" class="v-block">
        <view class="delivery-info flex-center-evenly">
          <view
            class="flex-center"
            style="gap: 6rpx"
            @click="deliverySelect = true"
          >
            <v-checkbox :checked="deliverySelect === true" />
            <view>指定物流</view>
          </view>
          <view
            class="flex-center"
            style="gap: 6rpx"
            @click="deliverySelect = false"
          >
            <v-checkbox :checked="deliverySelect === false" />
            <view>非指定物流</view>
          </view>
        </view>
        <view class="divider" />
        <template v-if="deliverySelect">
          <view class="delivery-info flex-center">
            <view class="delivery-label">
              物流公司
            </view>
            <view class="flex-1">
              <v-input
                v-model="deliveryName"
                maxlength="10"
                placeholder="请输入您的物流公司"
                :custom-style="{ textAlign: 'right' }"
              />
            </view>
          </view>
          <view class="divider" />
          <view class="delivery-info flex-center">
            <view class="delivery-label">
              物流电话
            </view>
            <view class="flex-1">
              <v-input
                v-model="deliveryTel"
                maxlength="20"
                placeholder="请输入您的物流电话"
                :custom-style="{ textAlign: 'right' }"
              />
            </view>
          </view>
          <view class="divider" />
          <view class="delivery-info flex-center">
            <view class="delivery-label">
              物流地址
            </view>
            <view class="flex-1">
              <v-input
                v-model="deliveryAddress"
                maxlength="30"
                placeholder="请输入您的物流地址"
                :custom-style="{ textAlign: 'right' }"
              />
            </view>
          </view>
        </template>
        <template v-else>
          <view class="delivery-info flex-center">
            提交订单后请联系客服选择快递物流公司。
          </view>
        </template>
      </view>
      <view class="v-block">
        <view class="enterprise-info flex-center">
          <view class="enterprise-label">
            公司名称
          </view>
          <view class="flex-1">
            <v-input
              v-model="enterpriseAbbr"
              maxlength="20"
              placeholder="请输入您的公司名称"
              :custom-style="{ textAlign: 'right' }"
            />
          </view>
        </view>
      </view>
      <view class="v-block">
        <template
          v-for="(product, productIndex) in productList"
          :key="product.id"
        >
          <view v-if="productIndex !== 0" class="divider" />
          <view class="product-item">
            <v-product-item :product="product">
              <template #name>
                {{ product.name }}
              </template>
              <template #spec>
                {{ product.productInfoSpec }}
              </template>
              <template #default>
                <view class="flex-center-between">
                  <view class="text-24 text-light">
                    {{ `装箱规格: ${product.displayAmount}pcs/箱` }}
                  </view>
                  <view class="text-24 text-light">
                    {{ `订购数量: ${product.number}` }}
                  </view>
                </view>
              </template>
            </v-product-item>
            <view class="product-count flex-baseline-between text-sub text-28">
              <view class="flex-baseline">
                <text> 单价: </text>
                <v-price :price="product.displayPrice" size="28rpx">
                  /pcs
                </v-price>
              </view>
              <view class="flex-baseline">
                <text> 小计: </text>
                <v-price
                  :price="
                    product.number * product.unitAmount * product.unitPrice
                  "
                  size="28rpx"
                />
              </view>
            </view>
          </view>
        </template>
        <view class="divider" />
        <view class="deliver-info">
          <view class="deliver-label" style="padding-bottom: 16rpx">
            备注
          </view>
          <v-textarea
            v-model="remark"
            maxlength="60"
            custom-style="height: 100rpx; width: auto"
            :cursor-spacing="75"
            placeholder="请输入备注"
          />
        </view>
      </view>
      <view class="v-block">
        <view class="payment-info flex-center-between">
          <view> 商品总价 </view>
          <v-price :price="totalPrice" color="#231815" />
        </view>
        <view class="payment-info" @click="showCouponPopover">
          <view class="flex-center-between">
            <view> 优惠券 </view>
            <view class="flex flex-center-between">
              <view v-if="!discount" style="color: #ccc">
                {{ couponCount ? "未使用" : "暂无可用" }}
              </view>
              <view v-else class="flex" style="color: #ccc">
                {{ selectedCoupon?.couponType === 0 ? "满减" : "打折" }}
              </view>
              <v-icon size="14rpx" src="/static/icons/cart/right-icon.png" />
            </view>
          </view>
        </view>
        <view v-if="discount" class="payment-info flex-1 flex-center-end">
          <v-price :price="-discount" />
        </view>
        <view class="divider" />
        <view class="payment-info flex-center-between">
          <view> 应付金额 </view>
          <v-price :price="totalPrice - discount" color="#231815" />
        </view>
      </view>
      <view class="count-pad-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view v-if="pageHeight" class="count-pad-container">
      <view class="count-pad flex-center-between box-shadow">
        <view class="flex-1 flex-col flex-start-center">
          <v-price :price="totalPrice - discount" size="36rpx" />
          <view
            v-if="discount"
            class="flex-center"
            style="font-size: 20rpx; color: #999999; margin-top: 5rpx"
          >
            共优惠<v-price :price="discount" size="20rpx" />
          </view>
        </view>
        <v-button
          type="primary"
          height="100rpx"
          width="220rpx"
          font-size="30rpx"
          :disabled="
            !productList || !orderToken || (!address && deliveryType !== 1)
          "
          @click="submit"
        >
          提交订单
        </v-button>
      </view>
      <view class="page-padding-bottom" />
    </view>
    <v-picker
      ref="pickerRef"
      v-model="isShowPicker"
      :options="[
        {
          label: '场内配送',
          value: 0
        },
        {
          label: '仓库自提',
          value: 1
        },
        {
          label: '自费物流',
          value: 2
        }
      ]"
      @confirm="changeType"
    />
    <v-toast />
    <coupon-popover
      ref="couponPopoverRef"
      :selected-coupon="selectedCoupon"
      :address="address"
      :total-price="totalPrice"
      :page-height="pageHeight * 0.7"
      :delivery-type="deliveryType ?? 0"
      @update-selected="updateSelectedCoupon"
      @update-count="updateCouponCount"
    />
  </view>
</template>

<script setup lang="ts">
import CouponPopover from "./modules/coupon-popover.vue"
import { num2str, str2num } from "@/utils/type-transfer"
import { webName } from '@/apis/config' 

const { pageHeight } = usePageLayout()

// 弹窗组件
const couponPopoverRef = ref() as Ref<InstanceType<typeof CouponPopover>>

/** 加载地址 */
const loading = ref(false)
/** 地址 */
const address = computed(() => pageStore.selectedAddress)
const getAddressList = async () => {
  loading.value = true
  await pageStore.getAddressList()
  pageStore.selectDefaultAddress()
  loading.value = false
}

const deliveryType = ref<number>()
const deliverySelect = ref(true)
const deliveryAddress = ref("")
const deliveryName = ref("")
const deliveryTel = ref("")
const enterpriseAbbr = ref("")
const isShowPicker = ref(false)
const pickerLabel = ref<string>()
const changeType = (list: [{ label: string; value: number }]) => {
  isShowPicker.value = false
  deliveryType.value = list[0].value
  pickerLabel.value = list[0].label
}
onLoad(() => {
  getAddressList()
  deliveryAddress.value = pageStore.deliveryAddress
  deliveryName.value = pageStore.deliveryName
  deliveryTel.value = pageStore.deliveryTel
  enterpriseAbbr.value = pageStore.enterpriseAbbr
})

// 选择的优惠券
const selectedCoupon = ref<CouponItem>()
const couponCount = ref(0)
// 打开
const showCouponPopover = () => {
  couponPopoverRef.value.showPopover()
}
const updateCouponCount = (count: number) => {
  couponCount.value = count
}
const updateSelectedCoupon = (coupon?: CouponItem) => {
  selectedCoupon.value = coupon
}
// 选择的优惠券金额
const discount = computed(() => {
  if (!selectedCoupon.value) return 0
  return parseFloat(selectedCoupon.value.couponValue)
})

const navigateToAddressList = () => {
  uni.navigateTo({ url: "/package-user/user/address-list?select=true" })
}

const pageStore = $store.page()
const productList = computed(() => pageStore.confirmOrder?.productList ?? [])
const orderToken = computed(() => pageStore.confirmOrder?.token)

const remark = ref("")
let orderNo: string

const totalPrice = computed(() => {
  if (!pageStore.confirmOrder?.sumPrice) return 0
  return str2num(pageStore.confirmOrder.sumPrice)
})

onLoad(() => pageStore.getWXTemplateId())
const submit = async () => {
  if (!orderToken.value || !productList.value) return
  if (deliveryType.value === undefined) {
    uni.showToast({ title: "请选择配送方式", icon: "none" })
    return false
  }
  if (!address.value && deliveryType.value !== 1) {
    uni.showToast({ title: "请选择收货地址", icon: "none" })
    return false
  }
  if (webName === "welshine"){  //广州惠而信总部
    switch (true) {
      case deliveryType.value !== 0:
        // 非场内配送
        break
      case address.value?.provinceCode !== 440000:  
      case address.value?.cityCode !== 440100: // 广州市
      case address.value?.districtCode !== 440105 &&
        address.value?.districtCode !== 440103: // 海珠区 、荔湾区
        uni.showToast({
          title: "场内配送目前只支持海珠区和荔湾区配送",
          icon: "none"
        })
        return false
    }
  }else if (webName === "sh.welshine"){  //上海惠而信总部
    switch (true) {
      case deliveryType.value !== 0:
        // 非场内配送
        break
      case address.value?.provinceCode !== 310000:  
      case address.value?.cityCode !== 310100: // 上海市
        uni.showToast({
          title: "场内配送目前只支持上海市内配送",
          icon: "none"
        })
        return false
    }
  }else if (webName === "cheerray"){
     //千芮场内配送暂时不限制
  }

  if (deliveryType.value === 2 && deliverySelect.value) {
    if (!deliveryName.value) {
      uni.showToast({ title: "请输入物流公司", icon: "none" })
      return false
    } else if (/[^\da-zA-Z\u4e00-\u9fa5]/.test(deliveryName.value)) {
      uni.showToast({ title: "物流公司只能输入中文,英文和数字", icon: "none" })
      return false
    }
    if (!deliveryTel.value) {
      uni.showToast({ title: "请输入物流电话", icon: "none" })
      return false
    } else if (!/^\d+$/.test(deliveryTel.value)) {
      uni.showToast({ title: "请输入正确的物流电话", icon: "none" })
      return false
    }
    if (!deliveryAddress.value) {
      uni.showToast({ title: "请输入物流地址", icon: "none" })
      return false
    } else if (/[^\da-zA-Z\u4e00-\u9fa5]/.test(deliveryAddress.value)) {
      uni.showToast({ title: "物流地址只能输入中文,英文和数字", icon: "none" })
      return false
    }
  }
  if (!enterpriseAbbr.value) {
    uni.showToast({ title: "请输入公司名称", icon: "none" })
    return false
  } else if (/[^\da-zA-Z\u4e00-\u9fa5]/.test(enterpriseAbbr.value)) {
    uni.showToast({ title: "公司名称只能输入中文,英文和数字", icon: "none" })
    return false
  }
  await pageStore.requestSubscribeMessage()
  uni.showLoading({ title: "提交中", mask: true })
  if (!orderNo) await submitOrder()
  if (orderNo) {
    if (totalPrice.value === 0) {
      return uni.redirectTo({
        url: `/package-order/order/pay-result?orderno=${orderNo}`
      })
    }
    uni.redirectTo({
      url: `/package-order/order/order-pay?orderno=${orderNo}`
    })
  }
  uni.hideLoading()
}

const submitOrder = async () => {
  if (
    !orderToken.value ||
    deliveryType.value === undefined ||
    !productList.value ||
    (!address.value && deliveryType.value !== 1)
  ) {
    return false
  }
  const response = await $api.submitOrder({
    couponMemberRelationId: selectedCoupon.value?.couponMemberRelationId,
    isBuyNow: pageStore.isBuyImmediate,
    memberAddressId:
      deliveryType.value === 1 ? "0" : (address.value?.id ?? "0"),
    orderToken: orderToken.value,
    remark: remark.value,
    deliveryType: deliveryType.value,
    enterpriseAbbr: enterpriseAbbr.value,
    deliveryAddress:
      deliveryType.value === 2 && deliverySelect.value
        ? deliveryAddress.value
        : undefined,
    deliveryName:
      deliveryType.value === 2 && deliverySelect.value
        ? deliveryName.value
        : undefined,
    deliveryTel:
      deliveryType.value === 2 && deliverySelect.value
        ? deliveryTel.value
        : undefined,
    shoppingCartList: productList.value.map((product) => ({
      productInfoId: product.id,
      quantity: num2str(product.serverNumber),
      quantityRetail: "0"
    }))
  })
  switch (response.__error) {
    case undefined:
      // 订单创建后去刷新一下购物车
      pageStore.refreshProductList()
      orderNo = response.data.orderNo
      return true
    default:
      /** TODO: 换个兜底方法 */
      pageStore.refreshToken()
      return false
  }
}
</script>

<style lang="scss" scoped>
.address-select {
  height: 80rpx;
  width: 328rpx;
  margin: 40rpx auto;
  border-radius: 40rpx;
  background-color: rgba(0, 91, 172, 0.1);
  line-height: 80rpx;
  text-align: center;
  color: #005bac;
  font-size: 24rpx;
  font-weight: bold;
}
.address-info {
  margin: 10rpx 0;
  .address-name {
    margin-right: 10rpx;
  }
}
.product-item {
  padding: 10rpx 0;
}
.enterprise-info {
  padding: 16rpx 0;
  .enterprise-label {
    position: relative;
    padding-left: 16rpx;
    &::before {
      position: absolute;
      content: "*";
      left: 0;
      color: $text-color-red;
      font-weight: bold;
    }
  }
}
.deliver-info {
  padding: 16rpx 0;
  .deliver-label {
    padding-left: 16rpx;
    &.required {
      position: relative;
      &::before {
        position: absolute;
        content: "*";
        left: 0;
        color: $text-color-red;
        font-weight: bold;
      }
    }
  }
}
.delivery-info {
  padding: 16rpx 0;
  .delivery-label {
    position: relative;
    padding-left: 16rpx;
    &::before {
      position: absolute;
      content: "*";
      left: 0;
      color: $text-color-red;
      font-weight: bold;
    }
  }
}
.payment-info {
  padding: 16rpx 0;
  & + & {
    padding-top: 0;
  }
}
.count-pad-blank {
  height: 100rpx;
}
.product-count {
  padding: 10rpx 0;
}
.count-pad-container {
  position: fixed;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  z-index: 10;
  .count-pad {
    padding-left: 40rpx;
    height: 100rpx;
    border-radius: 100vw;
    background-color: white;
  }
}
</style>
