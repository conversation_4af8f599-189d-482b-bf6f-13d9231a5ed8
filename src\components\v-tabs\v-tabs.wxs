var threshold = 20
var tabIndex = 0
var itemWidth = "var(--v-tabs-item-width)"
var itemCount = "var(--v-tabs-item-count)"

function transition (event, ins) {
  var instance = ins || event.instance
  var state = instance.getState()
  var offset = event.detail.dx
  var block = instance.selectComponent(".v-tab-block")
  moveBlock(offset, block, instance)
}

function transitionFinish (event, ins) {
  var instance = ins || event.instance
  var state = instance.getState()
  tabIndex = event.detail.current
  var block = instance.selectComponent(".v-tab-block")
  moveEnd(block, instance)
}

function moveBlock (offset, block, instance) {
  instance.requestAnimationFrame(function () {
    var tabIndexOffset = "(" + itemWidth + " * " + tabIndex + ")"
    var currentOffset = "(" + offset + "px / " + itemCount + ")"
    block.setStyle({
      transition: "none",
      transform: "translateX(calc(" + tabIndexOffset + " + " + currentOffset + "))"
    })
  })
}

function moveEnd (block, instance) {
  instance.requestAnimationFrame(function () {
    var tabIndexOffset = "(" + itemWidth + " * " + tabIndex + ")"
    block.setStyle({
      transition: "transform 0.1s linear",
      transform: "translateX(calc(" + tabIndexOffset + "))"
    })
  })
}

module.exports = {
  transition: transition,
  transitionFinish: transitionFinish
}