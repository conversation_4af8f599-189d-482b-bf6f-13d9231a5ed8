<template>
  <v-popover v-model="isExpand">
    <view class="coupon-popover">
      <view class="close-icon" @click="isExpand = false">
        <v-icon size="22rpx" src="/static/icons/cart/close-icon.png" />
      </view>
      <view class="title">
        选择优惠券
      </view>
      <view>
        <scroll-view
          v-if="pageHeight"
          scroll-y
          :style="{ height: `${pageHeight}px` }"
          enable-back-to-top
          refresher-enabled
          :refresher-triggered="refreshing"
          @refresherrefresh="refresh"
        >
          <v-empty
            v-if="!discount"
            src="/static/image/empty-coupon.png"
          >
            暂无可用优惠券
          </v-empty>
          <view v-else>
            <coupon-item
              v-for="(item, itemIndex) in couponSortList"
              :key="itemIndex"
              :coupon="item"
              :checked="
                selectingCoupon?.couponMemberRelationId ===
                  item.couponMemberRelationId
              "
              :is-show="itemIndex === showRuleIndex"
              @change-rules="changeRules(itemIndex)"
              @change-coupon="changeCoupon(item)"
            />
          </view>
          <view class="page-padding-bottom" />
        </scroll-view>
      </view>
      <view class="bottom">
        <v-button type="primary" @click="confirmCoupon">
          确定
        </v-button>
      </view>
      <view class="popper-padding-bottom" />
    </view>
  </v-popover>
</template>

<script setup lang="ts">
import CouponItem from "./coupon-item.vue"

const props = defineProps<{
  pageHeight: number
  totalPrice: number
  deliveryType: number
  selectedCoupon?: CouponItem
  address?: Address
}>()

interface Emits {
  (event: "update-selected", coupon?: CouponItem): void
  (event: "update-count", count: number): void
}
const emits = defineEmits<Emits>()

const totalPrice = toRef(props, "totalPrice")
const address = toRef(props, "address")
const deliveryType = toRef(props, "deliveryType")
const selectedCoupon = toRef(props, "selectedCoupon")

// 选择优惠券开关
const isExpand = ref(false)

const showRuleIndex = ref(-1)
const changeRules = (index: number) => {
  if (showRuleIndex.value === index) {
    showRuleIndex.value = -1
  } else {
    showRuleIndex.value = index
  }
}
/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  refreshCartList()
}
/** 刷新购物车 */
const refreshCartList = async () => {
  if (loading.value) return
  loading.value = true
  const result = await getAllCouponListByUserId()
  loading.value = false
  refreshing.value = false
  return result
}
onShow(() => refreshCartList())
// 选择中
const selectingCoupon = ref<CouponItem>()
const changeCoupon = (item: CouponItem) => {
  if (
    selectingCoupon.value?.couponMemberRelationId === item.couponMemberRelationId
  ) {
    selectingCoupon.value = undefined
  } else {
    selectingCoupon.value = item
  }
}
/** 优惠券列表 */
const couponList = ref<CouponItem[]>([])
// 筛选可用的优惠券(包含地址)
const couponAbleList = computed(() => {
  if (!couponList.value) return []
  return couponList.value.filter((item) => {
    const areaLimit = item.couponAreaLimit?.couponAreaLimitItems
    // 仓库自提不支持区域限制的优惠券
    if (deliveryType.value === 1 && areaLimit) return false
    if (address.value && areaLimit) {
      const cityId = address.value.cityCode
      const provinceId = address.value.provinceCode
      const result = areaLimit.some((area) => {
        if (area.cityId) {
          // 城市符合
          return area.cityId === cityId?.toString()
        }
        return area.provinceId === provinceId?.toString()
      })
      if (!result) return false
    }
    const priceLimit = parseInt(item.couponPriceLimit)
    const discount = parseFloat(item.couponValue)
    if (!discount) return false
    return priceLimit <= totalPrice.value
  })
})
// 优惠券最优排序
const couponSortList = computed(() => {
  const list = couponAbleList.value?.sort((a, b) => {
    // 面额较大在前
    const aValue = parseFloat(a.couponValue)
    const bValue = parseFloat(b.couponValue)
    if (aValue !== bValue) return bValue - aValue
    // 过期时间较后的在前
    const aTime = a.overdueTime
    const bTime = b.overdueTime
    if (aTime !== bTime) return aTime - bTime
    // 有区域限制的在前
    const aLimit = Boolean(a.couponAreaLimit)
    const bLimit = Boolean(b.couponAreaLimit)
    if (aLimit !== bLimit) return !aLimit && bLimit ? 1 : -1
    // 按照id排序
    const aId = parseInt(a.couponMemberRelationId)
    const bId = parseInt(b.couponMemberRelationId)
    return aId - bId
  })
  return list?.slice(0, 100)
})
const getAllCouponListByUserId = async () => {
  const response = await $api.getAllCouponListByUserId({})
  switch (response.__error) {
    case undefined: {
      couponList.value = response.data
    }
  }
  await nextTick()
  refreshing.value = false
}
watch(couponSortList, (value) => {
  emits("update-count", value?.length ?? 0)
  emits("update-selected", value?.[0])
})
const confirmCoupon = () => {
  emits("update-selected", selectingCoupon.value)
  isExpand.value = false
}
// 优惠金额
const discount = computed(() => {
  if (!couponSortList.value?.[0]) return 0
  return parseInt(couponSortList.value[0].couponValue)
})

const showPopover = () => {
  showRuleIndex.value = -1
  selectingCoupon.value = selectedCoupon.value
  isExpand.value = true
}
defineExpose({
  showPopover
})
</script>

<style lang="scss" scoped>
.coupon-popover {
  position: relative;
  padding-top: 28rpx;
  background-color: #f5f5f5;
  border-radius: 24rpx 24rpx 0 0;
  .close-icon {
    position: absolute;
    top: 39rpx;
    right: 24rpx;
  }
  .title {
    white-space: nowrap;
    font-size: 32rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 28rpx;
  }
  .bottom {
    margin: 20rpx 34rpx 0;
  }
}
</style>
