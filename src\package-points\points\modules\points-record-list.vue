<template>
  <view>
    <view class="filter-list flex-center">
      <view
        class="filter-item"
        :class="{ active: pointsStatus === undefined }"
        @click="() => changeFilter()"
      >
        全部
      </view>
      <view
        class="filter-item"
        :class="{ active: pointsStatus === 0 }"
        @click="() => changeFilter(0)"
      >
        到账
      </view>
      <view
        class="filter-item"
        :class="{ active: pointsStatus === 1 }"
        @click="() => changeFilter(1)"
      >
        消耗
      </view>
    </view>
    <scroll-view
      v-if="pageHeight"
      scroll-y
      :style="{ height: `calc(${pageHeight}px - 100rpx)` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
      @scrolltolower="findMemberPointsDetailList"
    >
      <template v-if="!refreshing">
        <v-loading-block v-if="loading" />
        <v-empty
          v-else-if="status === 'nomore' && pointsRecordList.length === 0"
          src="/static/image/empty-coupon.png"
        >
          <view> 暂无积分记录 </view>
        </v-empty>
        <template v-else>
          <view
            v-for="(record, recordIndex) in pointsRecordList"
            :key="recordIndex"
          >
            <view class="month-gap">
              {{ `${record.month}月` }}
            </view>
            <view
              v-for="(item, index) in record.recordList"
              :key="item.memberPointsDetailId"
            >
              <view v-if="index !== 0" class="divider" />
              <view class="record-item">
                <view class="header flex-center-between">
                  <view class="left-part flex-center-center">
                    <view class="title">
                      {{
                        ["消费积分", "积分过期", "积分兑换"][item.pointsType]
                      }}
                    </view>
                    <view v-if="item.pointsStatus === 0" class="tag primary">
                      到账
                    </view>
                    <view
                      v-else-if="item.pointsStatus === 1"
                      class="tag danger"
                    >
                      消耗
                    </view>
                  </view>
                  <view class="right-part">
                    <view class="value">
                      {{ item.pointsStatus === 0 ? "+" : "-" }}
                      {{ str2num(item.points) }}
                    </view>
                  </view>
                </view>
                <template v-if="item.pointsType === 0">
                  <view
                    v-if="item.orderPayTime"
                    class="time flex-center-between"
                  >
                    <view class="text">
                      {{ `订单支付时间：${formateUtc(item.orderPayTime)}` }}
                    </view>
                    <view
                      v-if="!item.isShowMore"
                      class="more flex-center"
                      @click="item.isShowMore = true"
                    >
                      <view>更多</view>
                      <view class="icon">
                        <v-icon size="17rpx" src="/static/icons/up-arrow.png" />
                      </view>
                    </view>
                  </view>
                  <v-transition
                    type="collapse"
                    :show="item.isShowMore || !item.orderPayTime"
                    timing="ease-out"
                  >
                    <view class="time">
                      {{ `积分到账时间：${formateUtc(item.createdTime)}` }}
                    </view>
                    <view
                      v-if="item.serviceNo"
                      class="details flex-center"
                      @click="() => navigateToOrderDetails(item.serviceNo)"
                    >
                      <view class="text">
                        {{ `订单编号：${item.serviceNo}` }}
                      </view>
                      <view class="button flex-center">
                        <view class="label">
                          查看
                        </view>
                        <v-icon
                          size="17rpx"
                          margin-left="10rpx"
                          src="/static/icons/right-arrow.png"
                        />
                      </view>
                    </view>
                  </v-transition>
                </template>
                <template v-else-if="item.pointsType === 1">
                  <view class="time">
                    {{ `积分过期时间：${formateUtc(item.createdTime)}` }}
                  </view>
                </template>
                <template v-else>
                  <view class="time flex-center-between">
                    <view class="text">
                      {{ `积分兑换时间：${formateUtc(item.createdTime)}` }}
                    </view>
                    <view
                      v-if="!item.isShowMore && item.serviceNo"
                      class="more flex-center"
                      @click="item.isShowMore = true"
                    >
                      <view>更多</view>
                      <view class="icon">
                        <v-icon size="17rpx" src="/static/icons/up-arrow.png" />
                      </view>
                    </view>
                  </view>
                  <v-transition
                    v-if="item.serviceNo"
                    type="collapse"
                    :show="item.isShowMore"
                    timing="ease-out"
                  >
                    <view class="details flex-center">
                      <view class="text">
                        {{ `兑换流水号：${item.serviceNo}` }}
                      </view>
                      <view
                        class="button flex-center"
                        @click="() => navigateToExchangeRecord(item.serviceId)"
                      >
                        <view class="label">
                          查看
                        </view>
                        <v-icon
                          size="17rpx"
                          margin-left="10rpx"
                          src="/static/icons/right-arrow.png"
                        />
                      </view>
                    </view>
                  </v-transition>
                </template>
              </view>
            </view>
          </view>
          <v-loadmore
            v-if="!refreshing"
            :status="status"
            @click="findMemberPointsDetailList"
          />
        </template>
      </template>
      <view class="page-padding-bottom" />
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
import { str2num } from "@/utils/type-transfer"

defineProps<{
  pageHeight: number
}>()

const pointsRecordList = ref<
{
  year: number
  month: number
  recordList: PointsRecord[]
}[]
>([])

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  initPointsRecordList()
}
const initPointsRecordList = async () => {
  loading.value = true
  pageIndex.value = 1
  total.value = 0
  pointsRecordList.value = []
  status.value = "loadmore"
  await findMemberPointsDetailList()
}
/** 分页数据 */
const pageIndex = ref(1)
const total = ref(0)
const pointsStatus = ref<number>()
const changeFilter = async (value?: number) => {
  if (value === pointsStatus.value) return
  pointsStatus.value = value
  await initPointsRecordList()
}
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")

/** 获取积分记录列表 */
const findMemberPointsDetailList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  const response = await $api.findMemberPointsDetailList({
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    requestParams: {
      pointsStatus: pointsStatus.value
    },
    orderField: "createdTime",
    sortType: "desc"
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      const list = response.data.result
      for (let i = 0; i < list.length; i += 1) {
        const item = list[i]
        const date = new Date(item.createdTime)
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        const record = pointsRecordList.value.find(
          (item) => item.year === year && item.month === month
        )
        if (record) {
          record.recordList.push(item)
        } else {
          pointsRecordList.value.push({
            year,
            month,
            recordList: [item]
          })
        }
        total.value += 1
      }
      const totalCount = str2num(response.data.totalCount)
      status.value = total.value < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
  loading.value = false
}

const navigateToOrderDetails = (orderNo: string) => {
  uni.navigateTo({
    url: "/package-order/order/order-details?orderno=" + orderNo
  })
}
const navigateToExchangeRecord = (id: string) => {
  uni.navigateTo({
    url: `/package-points/points/points-exchange-record-details?id=${id}`
  })
}
defineExpose({ initPointsRecordList })
</script>

<style lang="scss" scoped>
.filter-list {
  background-color: white;
  height: 100rpx;
  padding: 0 24rpx;
  .filter-item {
    margin-right: 24rpx;
    padding: 0 26rpx;
    line-height: 54rpx;
    border-radius: 26rpx;
    font-size: 24rpx;
    color: #666666;
    transition: background-color 0.15s, color 0.15s;
    &.active {
      background-color: rgba(0, 91, 172, 0.12);
      color: #005bac;
    }
  }
}
.month-gap {
  position: sticky;
  top: 0;
  padding: 24rpx;
  font-weight: bold;
  color: #666666;
  background-color: #f5f5f5;
}
.record-item {
  padding: 20rpx 40rpx;
  background-color: white;
  .header {
    margin-bottom: 10rpx;
    .title {
      line-height: 42rpx;
      color: #231815;
      font-weight: bold;
    }
    .tag {
      margin-left: 12rpx;
      height: 34rpx;
      width: 65rpx;
      border-radius: 6rpx;
      font-size: 20rpx;
      line-height: 34rpx;
      text-align: center;
      &.primary {
        background-color: rgba(0, 91, 172, 0.12);
        color: #005bac;
      }
      &.danger {
        background-color: rgba(240, 103, 113, 0.12);
        color: #f06771;
      }
    }
    .right-part .value {
      font-size: 32rpx;
      font-weight: bold;
      color: #000;
    }
  }
  .time {
    line-height: 42rpx;
    font-size: 24rpx;
    color: #999;
    .more {
      .icon {
        margin-left: 10rpx;
        transform-origin: center;
        transform: rotate(180deg);
      }
    }
  }
  .details {
    line-height: 42rpx;
    font-size: 24rpx;
    color: #999;
    .button {
      margin-left: 20rpx;
    }
  }
}
</style>
