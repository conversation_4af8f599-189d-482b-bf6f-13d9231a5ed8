<template>
  <view class="v-product-item flex-center">
    <view v-if="$slots.checkbox" class="checkbox flex-center">
      <slot name="checkbox" />
    </view>
    <view class="image-style">
      <v-image
        :height="imageSize"
        :width="imageSize"
        :src="product.image"
        border
        mode="aspectFill"
        @click="clickImage
        "
      />
      <view
        v-if="markUrl"
        class="watermark flex-center-center"
        @click="clickImage
        "
      >
        <v-image
          :height="`calc(${imageSize} * 0.8)`"
          :width="`calc(${imageSize} * 0.8)`"
          :src="markUrl"
        />
      </view>
    </view>
    <view class="product-info">
      <view class="flex-center-between">
        <view class="flex-1">
          <view
            :class="nameClass"
            @click="clickName
            "
          >
            <slot name="name" />
          </view>
          <view
            class="spec"
            :class="specClass"
            @click="clickSpec
            "
          >
            <slot name="spec" />
          </view>
        </view>
        <view class="sub-button">
          <slot name="sub-button" />
        </view>
      </view>
      <view>
        <slot />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    /** 商品 */
    product: ProductItem
    /** 是否在售，值为false出现失效水印 */
    isOnSale?: boolean
    /** 是否有货，值为false出现缺货水印 */
    isInStock?: boolean
    /** 是否已被删除，值为true出现删除水印 */
    isDeleted?: boolean
    /** 大小 */
    size?: string
  }>(),
  {
    isOnSale: undefined,
    isInStock: undefined,
    isDeleted: undefined,
    size: undefined
  }
)

interface Emits {
  (event: "click"): void
  (event: "click-image"): void
  (event: "click-name"): void
  (event: "click-spec"): void
}
const emits = defineEmits<Emits>()

const size = toRef(props, "size")
const product = toRef(props, "product")

const imageSize = computed(() => {
  switch (size.value) {
    case "small":
      return "140rpx"
    default:
      return "180rpx"
  }
})

const nameClass = computed(() => {
  switch (size.value) {
    case "small":
      return "text-24 text-bold text-1-row flex-1"
    default:
      return "text-bold text-1-row flex-1"
  }
})

const isDeleted = toRef(props, "isDeleted")
const isOnSale = toRef(props, "isOnSale")
const isInStock = toRef(props, "isInStock")
const markUrl = computed(() => {
  switch (true) {
    case isDeleted.value:
      return "/static/icons/product/lose-efficacy.png"
    case isOnSale.value === false:
      return "/static/icons/product/lose-efficacy.png"
    case isInStock.value === false:
      return "/static/icons/product/stockout.png"
  }
})

const specClass = computed(() => {
  switch (size.value) {
    case "small":
      return "text-light text-22 text-1-row"
    default:
      return "text-light text-24 text-1-row"
  }
})

const clickImage = () => {
  emits("click")
  emits("click-image")
}
const clickName = () => {
  emits("click")
  emits("click-name")
}
const clickSpec = () => {
  emits("click")
  emits("click-spec")
}
</script>

<style lang="scss" scoped>
.v-product-item {
  .image-style {
    position: relative;
    .watermark {
      background-color: rgba(0, 0, 0, 0.35);
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      z-index: 10;
      border-radius: 14rpx;
    }
  }
  .product-info {
    flex: 1;
    margin-left: 16rpx;
    padding: 6rpx 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-self: stretch;
  }
  .sub-button {
    width: fit-content;
  }
  .count {
    margin-left: 8rpx;
  }
  .v-swipe-hide {
    display: var(--v-swipe-hide-display);
  }
  .count-bottom {
    margin-top: 2rpx;
  }
  .checkbox {
    margin-right: 18rpx;
  }
}
</style>
