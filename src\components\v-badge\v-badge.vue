<template>
  <view v-if="count" class="v-badge flex-center-center">
    <text>
      {{ count > 99 ? "99+" : count }}
    </text>
  </view>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    count: number
  }>(),
  {
    count: 0
  }
)
</script>

<style lang="scss" scoped>
.v-badge {
  position: absolute;
  right: 0;
  height: 32rpx;
  padding: 0 8rpx;
  min-width: 32rpx;
  text-align: center;
  font-size: 24rpx;
  color: white;
  border-radius: 16rpx;
  background-color: #ff5151;
  box-sizing: border-box;
}
</style>
