<template>
  <view class="v-swipe" :class="customClass" :style="customStyle">
    <view class="v-swipe__button" style="display: none" @click="wxs.close">
      <slot name="button" />
    </view>
    <view
      class="v-swipe__content"
      @touchstart="wxs.touchstart"
      @touchmove="wxs.touchmove"
      @touchend="wxs.touchend"
    >
      <slot />
    </view>
  </view>
</template>

<script lang="ts">
import wxs from "@/utils/wxs"

export default {
  props: {
    margin: {
      type: String,
      default: ""
    },
    padding: {
      type: String,
      default: ""
    },
    customClass: {
      type: String,
      default: ""
    }
  },
  data () {
    return {
      wxs: {
        close: wxs.close,
        touchstart: wxs.touchstart,
        touchmove: wxs.touchmove,
        touchend: wxs.touchend
      }
    }
  },
  computed: {
    customStyle () {
      const list = []
      if (this.margin) list.push(`margin: ${this.margin}`)
      if (this.padding) list.push(`padding: ${this.padding}`)
      return list.join(";")
    }
  }
}
</script>

<script src="./v-swipe.wxs" module="wxs" lang="wxs"></script>

<style lang="scss" scoped>
.v-swipe {
  position: relative;
  overflow: hidden;
  .v-swipe__content {
    position: relative;
  }
  .v-swipe__button {
    position: absolute;
    top: 2rpx;
    right: 2rpx;
    bottom: 2rpx;
    width: fit-content;
  }
}
</style>
