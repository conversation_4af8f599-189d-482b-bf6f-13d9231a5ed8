@for $num from 10 through 16 {
  .text-#{$num * 2} {
    font-size: $num * 2rpx;
  }
}

.text-main {
  color: $text-color-main;
}
.text-sub {
  color: $text-color-sub;
}
.text-light,
.placeholder {
  color: $text-color-light;
}
.text-white {
  color: white;
}
.text-red {
  color: $text-color-red;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-bold {
  font-weight: bold;
}

@each $num in 1, 2, 3 {
  .text-#{$num}-row {
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $num;
    word-break: break-all;
  }
}
