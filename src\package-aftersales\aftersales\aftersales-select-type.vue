<template>
  <view>
    <view class="page-header">
      <v-navbar title="选择售后类型" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <v-loading-block v-if="!order" />
      <view v-else>
        <view class="v-block">
          <view class="order-id flex-center-between">
            <view class="text-light text-24">
              {{ `订单编号:${order.orderNo}` }}
            </view>
          </view>
          <view class="divider" />
          <view class="product-container">
            <template v-if="order.productList.length === 1">
              <view class="product-content flex-1 flex">
                <view class="product-item flex-center">
                  <view style="margin-right: 16rpx">
                    <v-image
                      :src="order.productList[0].image"
                      height="160rpx"
                      width="160rpx"
                      border
                      mode="aspectFill"
                    />
                  </view>
                  <view class="flex-1 flex-center text-bold">
                    {{ order.productList[0].name }}
                  </view>
                </view>
              </view>
            </template>
            <template v-else>
              <view class="product-content">
                <scroll-view scroll-x class="hide-scroll-bar">
                  <view class="scroller-content flex">
                    <view
                      v-for="(product, index) in order.productList"
                      :key="index"
                      class="mutiple-product-item flex-col flex--between"
                    >
                      <v-image
                        height="160rpx"
                        width="160rpx"
                        :src="product.image"
                        border
                      />
                      <view class="product-label text-24 text-bold text-1-row">
                        {{ product.name }}
                      </view>
                    </view>
                  </view>
                </scroll-view>
              </view>
            </template>
            <view class="count-pad flex-center-center">
              <view class="flex-end flex-col">
                <view class="price">
                  <v-price
                    :price="order.actualPayCost"
                    color="#231815"
                    size="26rpx"
                  />
                </view>
                <view class="text-light text-26">
                  {{ `共${order.productList.length}件` }}
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="v-block">
          <view class="action flex-center-between" @click="redirectToRefund()">
            <v-icon
              size="44rpx"
              margin-right="24rpx"
              src="/static/icons/aftersales/refund.png"
            />
            <view class="text-bold">
              退货退款
            </view>
            <view class="time text-light text-24 text-right flex-1">
              {{ formateUtc(deadline, "YYYY.MM.dd前可申请") }}
            </view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
        </view>
        <view class="customer-service">
          <!-- <v-button
            type="primary"
            height="94rpx"
            font-size="32rpx"
            @click="customerService"
          >
            联系客服
          </v-button> -->
          <v-button
            type="primary"
            height="94rpx"
            font-size="32rpx"
            open-type="contact"
            send-message-title="订单详情"
            :send-message-path="`/package-order/order/order-details.html?orderno=${orderNo}&customer=4`"
            :send-message-img="logoSrc"
            show-message-card
          >
            联系客服
          </v-button>
          <view class="info text-24 text-sub">
            如出现漏发，破损等问题，请联系客服
          </view>
        </view>
      </view>
      <view class="page-padding-bottom" />
    </scroll-view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
import { str2num } from "@/utils/type-transfer"

const { safeNavigateBack, pageHeight } = usePageLayout()

const pageStore = $store.page()
const order = ref<AftersalesOrderDetails>()
const orderNo = ref("")

const getOrderDetails = async () => {
  order.value = undefined
  const response = await $api.orderDetail({ orderNo: orderNo.value })
  switch (response.__error) {
    case undefined: {
      const data = response.data
      order.value = {
        orderNo: data.orderNo,
        completeTime: data.receiveTime,
        actualPayCost: str2num(data.actualPayCost),
        totalPrice: str2num(data.sumPrice),
        productList: data.orderProductRelationVOList.map((product) => {
          const item: AftersalesOrderProductItem = {
            id: product.productInfoId,
            image: product.productInfoProductImg,
            name: product.productInfoName,
            unitPrice: str2num(product.productInfoPrice),
            unitAmount: str2num(product.productInfoPackage),
            displayPrice: str2num(product.displayUnitPrice),
            displayAmount: product.displayPackage,
            number: str2num(product.quantity),
            productInfoSpec: product.productInfoSpec,
            quantity: str2num(product.quantity),
            totalPackage: product.totalPackage,
            selected: false
          }
          return item
        })
      }
      break
    }
    default:
      navigateBack()
  }
}
let unmounted = false
const navigateBack = () => {
  if (unmounted) return
  safeNavigateBack()
}
onUnmounted(() => (unmounted = true))
onHide(() => (unmounted = true))
onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.orderno) {
    safeNavigateBack()
  } else {
    orderNo.value = query.orderno
    getOrderDetails()
  }
})
const deadline = computed(() => {
  if (!order.value) return NaN
  return order.value.completeTime + 864000000
})

// const customerService = () => {
//   if (!orderNo.value) return
//   uni.openCustomerServiceChat({
//     extInfo: { url: $api.config.customerUrl },
//     corpId: $api.config.corpId,
//     showMessageCard: true,
//     sendMessageTitle: "订单详情",
//     sendMessagePath: `/package-order/order/order-details.html?orderno=${orderNo.value}&customer=4`,
//     sendMessageImg: "/static/image/service-logo.png",
//     fail: (err) => console.log(err)
//   })
// }

const redirectToRefund = () => {
  pageStore.setAftersalesOrder(order.value)
  uni.redirectTo({
    url: "/package-aftersales/aftersales/aftersales-select-product"
  })
}

import { webName } from '@/apis/config' 

const logoSrc = computed(() => {
  if (webName === 'cheerray') {
    return "/static/image/service-logo-cheerray.png"
  } else {
    return "/static/image/service-logo.png"
  }
})
</script>

<style lang="scss" scoped>
.action {
  padding: 24rpx 0;
  .time {
    margin: 0 10rpx;
  }
}
.v-block {
  padding: 4rpx 24rpx;
}
.order-id {
  padding: 24rpx 14rpx;
}
.product-container {
  position: relative;
  .product-content {
    width: calc(100% - 160rpx);
  }
  .count-pad {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 140rpx;
    padding: 0 10rpx;
    box-shadow: -8px 0 6px -6px rgba(0, 0, 0, 0.15);
    background-color: white;
  }
}
.product-content {
  .product-item {
    padding: 14rpx 0;
  }
  .mutiple-product-item {
    padding: 14rpx 14rpx 14rpx 0;
    width: 174rpx;
    .product-label {
      margin-top: 10rpx;
    }
  }
}
.customer-service {
  padding: 24rpx;
  .info {
    margin-top: 20rpx;
    text-align: center;
  }
}
</style>
