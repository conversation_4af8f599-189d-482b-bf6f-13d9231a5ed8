export default () => {
  /** 状态栏高度 */
  const statusBarHeight = computed(() => {
    const { statusBarHeight } = uni.getWindowInfo()
    let statusBar
    // #ifndef H5
    statusBar = statusBarHeight
    // #endif

    // #ifdef H5
    statusBar = 0
    // #endif
    return statusBar
  })
  /** 导航栏高度 */
  const navBarHeight = computed(() => {
    const { statusBarHeight } = uni.getWindowInfo()
    const { platform } = uni.getDeviceInfo()
    let navBar
    if (statusBarHeight === undefined) return 0
    // #ifdef MP
    if (platform === "android") {
      navBar = statusBarHeight + 50
    } else {
      navBar = statusBarHeight + 45
    }
    // #endif

    // #ifdef APP-PLUS
    navBar = statusBarHeight + 45
    // #endif

    // #ifdef H5
    navBar = 44
    // #endif

    // #ifdef MP-WEIXIN
    const { bottom, top } = uni.getMenuButtonBoundingClientRect()
    navBar = bottom - statusBarHeight + top - statusBarHeight
    // #endif
    return navBar
  })
  /** 胶囊按钮 */
  const menuButtonBounding = computed(() => {
    // #ifdef H5
    return {
      height: 0,
      width: 0,
      top: 0,
      left: 0,
      bottom: 0,
      right: 0
    }
    // #endif
    const data = uni.getMenuButtonBoundingClientRect()
    const { windowWidth } = uni.getWindowInfo()
    return {
      height: data?.height ?? 0,
      width: data?.width ?? 0,
      top: data?.top ?? 0,
      left: data?.left ?? 0,
      bottom: data?.bottom ?? 0,
      right: windowWidth - (data?.right ?? 0)
    }
  })
  return { statusBarHeight, navBarHeight, menuButtonBounding }
}
