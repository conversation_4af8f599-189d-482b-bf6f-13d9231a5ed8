<template>
  <v-popover :model-value="isShow" @close="hideSelect">
    <view v-if="!product" class="loading-container">
      <view class="v-loading flex-center-center">
        <v-loading-block />
      </view>
      <view class="popper-padding-bottom" />
      <view class="close-button" @click="hideSelect">
        <v-icon size="44rpx" src="/static/icons/components/close-button.png" />
      </view>
    </view>
    <view v-else class="v-product-select">
      <view class="main flex">
        <view class="img-box">
          <v-image
            height="220rpx"
            width="220rpx"
            :src="product.productImg || ''"
            border
            mode="aspectFill"
          />

          <view
            v-if="(product.shelvesStock as number) <= 0"
            class="watermark flex-center-center"
          >
            <v-image
              height="calc(220rpx * 0.8)"
              width="calc(220rpx * 0.8)"
              src="/static/icons/product/stockout.png"
            />
          </view>
        </view>
        <view class="main-info flex-col flex--evenly flex-1">
          <v-price v-if="product" :price="product.displayPrice" size="36rpx">
            /pcs
          </v-price>
          <view class="product-name text-bold text-2-row">
            {{ product.productSpecName || product.name }}
          </view>
          <view v-if="product" class="text-sub text-2-row">
            {{ product.productSpec }}
          </view>
        </view>
      </view>
      <view class="spec-list-title text-30 text-bold">
        规格选择
      </view>
      <view class="scroller-container">
        <scroll-view
          scroll-y
          :scroll-into-view="scrollId"
          scroll-with-animation
          class="hide-scroll-bar"
          :style="{ height: '340rpx' }"
        >
          <view class="spec-list">
            <view
              v-for="spec in specList"
              :id="'spec-' + spec.id"
              :key="spec.id"
              class="spec-item"
              :class="{ active: productId === spec.id }"
              @click="() => selectProductSpec(spec)"
            >
              {{ spec.productSpecName || spec.littletitle }}
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="product-number flex-center-between">
        <view class="text-light">
          购买数量
        </view>
        <view class="flex-center">
          <v-number-input
            v-model="productNumber"
            color="#005BAC"
            font-size="32rpx"
            height="60rpx"
            width="220rpx"
            :disabled="product.shelvesStock === 0"
            :min="1"
            :max="product.shelvesStock"
            max-text="该商品库存不足"
          />
          <view style="margin-left: 10rpx">
            箱
          </view>
        </view>
      </view>
      <view class="product-package flex-center-between">
        <view class="text-light">
          包装规格
        </view>
        <view>
          {{ `${product.displayAmount}pcs/箱` }}
        </view>
      </view>
      <view class="divider" />
      <view class="package-info flex-center-between text-24 text-light">
        <view>
          {{ `合计: ${productNumber}箱` }}
          {{ `${productNumber * product.displayAmount}pcs` }}
        </view>
        <view class="flex-baseline">
          <view class="price-label">
            商品金额:
          </view>
          <v-price
            :price="
              product.shelvesStock === 0
                ? 0
                : productNumber * product.unitAmount * product.unitPrice
            "
          />
        </view>
      </view>
      <view class="button-list flex-center-between">
        <v-button
          type="danger"
          height="94rpx"
          width="340rpx"
          :disabled="!product || product.shelvesStock === 0"
          @click="addToCart"
        >
          加入购物车
        </v-button>
        <v-button
          type="primary"
          height="94rpx"
          width="340rpx"
          :disabled="!product || product.shelvesStock === 0"
          @click="buyImmediate"
        >
          立即购买
        </v-button>
      </view>
      <view class="popper-padding-bottom" />
      <view class="close-button" @click="hideSelect">
        <v-icon size="44rpx" src="/static/icons/components/close-button.png" />
      </view>
    </view>
  </v-popover>
</template>

<script setup lang="ts">
import { checkToken } from "@/utils/auth/token"
import { num2str, str2num } from "@/utils/type-transfer"

withDefaults(
  defineProps<{
    switchTab?: boolean
  }>(),
  {
    switchTab: false
  }
)

interface Emits {
  (event: "select", product: ProductDetails, number: number): void
}
const emits = defineEmits<Emits>()

const pageStore = $store.page()

const isShow = ref(false)

const productId = ref<string | undefined>()
const product = ref<ProductDetails | undefined>()
const specList = ref<ProductSpecification[]>([])
const productNumber = ref(1)

let hideTimer: number | undefined
const showSelect = (data: {
  id?: string
  details?: ProductDetails
  number?: number
}) => {
  if (hideTimer !== undefined) return
  if (data.details) {
    product.value = data.details
    specList.value = data.details.specList
    const id = data.details.id
    setTimeout(() => {
      productId.value = id
    }, 100)
  } else if (data.id) {
    productId.value = data.id
    product.value = undefined
    specList.value = []
    getProductDetails()
  } else {
    uni.showModal({
      title: "页面错误",
      content: "商品编号丢失",
      showCancel: false
    })
    return
  }
  isShow.value = true
  productNumber.value = data.number ?? 1
}

const hideSelect = async () => {
  if (product.value && productNumber.value) {
    emits("select", product.value, productNumber.value)
  }
  isShow.value = false
  await new Promise((resolve) => {
    hideTimer = setTimeout(() => {
      hideTimer = undefined
      productId.value = undefined
      product.value = undefined
      specList.value = []
      resolve(true)
    }, 250)
  })
}

const scrollId = computed(() => {
  const selectedSpec = specList.value.find(
    (item) => item.id === productId.value
  )
  if (!selectedSpec) return ""
  return "spec-" + selectedSpec.id
})

const selectProductSpec = (specification: ProductSpecification) => {
  if (productId.value === specification.id) return
  productId.value = specification.id
  productNumber.value = 1
  getProductDetails()
}

const getProductDetails = async () => {
  if (productId.value === undefined) {
    await uni.showModal({
      title: "页面错误",
      content: "商品编号丢失",
      showCancel: false
    })
    return hideSelect()
  }
  const response = await $api.productDetail({ productInfoId: productId.value })
  switch (response.__error) {
    case undefined: {
      if (response.code === "50006") {
        await uni.showModal({
          title: "该商品已下架",
          content: "请看看其他商品～",
          showCancel: false
        })
        hideSelect()
      } else {
        const unitAmount = str2num(response.data.ncPackage)
        const stockNum = str2num(response.data.shelvesStock)
        const stockCount = isNaN(stockNum) ? 0 : stockNum
        product.value = {
          ...response.data,
          unitPrice: str2num(response.data.productPrice),
          unitAmount,
          displayPrice: str2num(response.data.displayUnitPrice),
          displayAmount: response.data.displayPackage,
          marketPrice: str2num(response.data.marketPrice),
          isOnSale: response.data.isOnSale === "1",
          shelvesStock: Math.floor(Math.max(stockCount, 0) / unitAmount)
        }
        specList.value = response.data.specList
      }
      break
    }
    default:
      hideSelect()
  }
}

const navigateToSignUp = () => {
  uni.navigateTo({ url: "/pages/user/sign-up" })
}

const addToCart = async () => {
  if (!product.value) return
  if (!checkToken()) return navigateToSignUp()
  const response = await $api.addTheGoodsToTheCart({
    productInfoId: product.value.id,
    quantity: num2str(productNumber.value),
    quantityRetail: "0"
  })
  switch (response.__error) {
    case undefined:
      if (response.code === "30003") {
        $toast.show(["该商品库存不足", "请联系客服"], "200rpx")
      } else {
        $toast.show("添加成功", "200rpx")
        pageStore.addProductSelect(product.value.id)
        hideSelect()
      }
  }
}

const checkShelvesStock = async () => {
  if (!product.value) return
  const response = await $api.productShelvesStockActualTime({
    productInfoId: product.value.id
  })
  switch (response.__error) {
    case undefined: {
      const shelvesStock = str2num(response.data.shelvesStock)
      const shelvesStockNumber = isNaN(shelvesStock) ? 0 : shelvesStock
      if (productNumber.value > shelvesStockNumber) {
        $toast.show(["该商品库存不足", "请联系客服"], "200rpx")
        return false
      } else {
        return true
      }
    }
  }
}
const buyImmediate = async () => {
  if (!product.value) return
  if (!checkToken()) return navigateToSignUp()
  if (!(await checkShelvesStock())) return
  const response = await $api.orderConfirmToken({})
  switch (response.__error) {
    case undefined: {
      const list: ProductCartItem[] = [
        {
          id: product.value.id,
          image: product.value.productImg,
          name: product.value.productSpecName
            ? product.value.productSpecName
            : product.value.name,
          unitPrice: product.value.unitPrice,
          unitAmount: product.value.unitAmount,
          displayPrice: product.value.displayPrice,
          displayAmount: product.value.displayAmount,
          number: productNumber.value,
          productInfoSpec: product.value.productSpec,
          serverNumber: productNumber.value,
          changingNumber: false,
          isOnSale: true,
          shelvesStock: product.value.shelvesStock,
          isDeleted: false
        }
      ]
      const price =
        product.value.unitPrice * product.value.unitAmount * productNumber.value
      pageStore.setConfirmOrder(
        response.data.orderToken,
        response.data.deliveryAddress ?? "",
        response.data.deliveryName ?? "",
        response.data.deliveryTel ?? "",
        response.data.enterpriseAbbr ?? "",
        price.toFixed(2),
        list,
        true
      )
      uni.navigateTo({ url: "/package-order/order/order-confirm" })
      hideSelect()
    }
  }
}

defineExpose({ showSelect })
</script>

<style lang="scss" scoped>
.loading-container {
  position: relative;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  .v-loading {
    height: 1030rpx;
  }
}
.img-box {
  position: relative;
  width: 220rpx;
  height: 220rpx;
  .watermark {
    background-color: rgba(0, 0, 0, 0.35);
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    border-radius: 14rpx;
  }
}
.v-product-select {
  position: relative;
  padding: 24rpx;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  .main {
    padding: 16rpx 0;
    .main-info {
      margin-left: 16rpx;
      padding-top: 30rpx;
      .product-name {
        margin-bottom: 10rpx;
      }
    }
  }
  .spec-list-title,
  .product-number {
    padding-top: 16rpx;
  }
  .spec-list {
    padding: 16rpx 0;
  }
  .product-package {
    padding: 16rpx 0;
  }
  .scroller-container {
    box-shadow: inset 0 0 6rpx rgba(0, 0, 0, 0.1);
    border-radius: 16rpx;
    padding: 0 10rpx;
  }
  .spec-item {
    padding: 20rpx 0;
    margin-bottom: 16rpx;
    border-radius: 8rpx;
    background-color: #f5f5f5;
    border: 1px solid #f5f5f5;
    color: #231815;
    text-align: center;
    &.active {
      color: #005bac;
      border-color: #77a7f8;
      background-color: rgba(119, 167, 248, 0.1);
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .package-info {
    height: 60rpx;
    padding: 16rpx 0;
    line-height: 32rpx;
    text-align: right;
    .price-label {
      margin-right: 10rpx;
    }
  }
}
.close-button {
  position: absolute;
  top: 18rpx;
  right: 18rpx;
  height: fit-content;
  width: fit-content;
  z-index: 2;
}
</style>
