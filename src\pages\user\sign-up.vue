<template>
  <view>
    <view class="page-background-white" />
    <view class="page-header hide-shadow">
      <v-navbar title="登录" :back="back" />
    </view>
    <view
      class="page flex-col flex-center"
      :style="{ height: `${pageHeight}px` }"
    >
      <v-transition
        type="fadeCollapse"
        custom-class="flex-col flex--end"
        :show="!!pageHeight && step === 2"
      >
        <insert-phone-number
          :checkbox="checkboxStatus"
          @checked="changeCheckboxStatus"
        />
      </v-transition>
      <view class="padding-top" />
      <v-transition type="fade" :show="!!pageHeight && step < 1">
        <v-image width="464rpx" :src="logoSrc" />
      </v-transition>
      <v-transition type="fade" :show="!!pageHeight && step === 0">
        <get-phone-number
          :checkbox="checkboxStatus"
          @checked="changeCheckboxStatus"
          @update="(val: number) => (step = val)"
        />
      </v-transition>
      <view class="padding-bottom" />
      <view class="protocol-blank" />
      <view class="page-padding-bottom" />
    </view>
    <view class="page-footer hide-shadow">
      <view class="page-footer-content">
        <view class="protocol text-center" @click="changeCheckboxStatus">
          <v-checkbox :checked="checkboxStatus" />
          <text>我已阅读并同意</text>
          <text class="link" @click.stop="navigateToContent(6)">
            用户协议
          </text>
          <text>、</text>
          <text class="link" @click.stop="navigateToContent(5)">
            隐私协议
          </text>
        </view>
        <view class="page-padding-bottom" />
      </view>
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import GetPhoneNumber from "./modules/get-phone-number.vue"
import InsertPhoneNumber from "./modules/insert-phone-number.vue"
import VCheckbox from "@/components/v-checkbox/v-checkbox.vue"

const { pageHeight } = usePageLayout()

const step = ref(0)
const checkboxStatus = ref(false)
const changeCheckboxStatus = () => {
  checkboxStatus.value = !checkboxStatus.value
}

let timer: number | undefined
const back = () => {
  if (timer !== undefined) return
  if (step.value === 2) {
    step.value = 1
    timer = setTimeout(() => {
      timer = undefined
      step.value = 0
    }, 300)
  } else {
    uni.$emit("loginFinish", false)
    uni.$off("loginFinish")
    const pages = getCurrentPages()
    const prePage = pages[pages.length - 2]
    const route = prePage?.route
    const list = [
      "pages/index/index-page",
      "package-product/product/product-search",
      "package-product/product/product-details",
      "pages/user/user-page",
      "pages/product/product-page",
      "package-activity/activity/activity-details-share"
    ]
    if (route && list.includes(route)) {
      uni.navigateBack()
    } else {
      uni.switchTab({ url: "/pages/index/index-page" })
    }
  }
}

const navigateToContent = async (type: number) => {
  uni.navigateTo({
    url: `/package-user/user/customer-service-detail?type=${type}`
  })
}

import { webName } from '@/apis/config' 

const logoSrc = computed(() => {
  if (webName === 'cheerray') {
    return "/static/image/logo-cheerray.png"
  } else {
    return "/static/image/logo.png"
  }
})
</script>

<style lang="scss" scoped>
.page {
  .padding-top {
    flex: 1;
  }
  .padding-bottom {
    flex: 3;
  }
}
.protocol-blank {
  height: 50rpx;
}
.page-footer-content {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: fit-content;
  .protocol {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50rpx;
    margin-bottom: 150rpx;
    font-size: 24rpx;
    color: #999;
    v-checkbox {
      display: inline-block;
      margin-right: 10rpx;
    }
    .link {
      color: #005bac;
    }
  }
}
</style>
