<template>
  <view class="v-loading-block flex-center-center">
    <view :style="{ padding: padding }">
      <v-loading />
      <view class="text text-light text-center">
        加载中
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    src?: string
    padding?: string
  }>(),
  {
    src: undefined,
    padding: "100rpx 0"
  }
)
</script>

<style lang="scss" scoped>
.text {
  margin-top: 10rpx;
}
</style>
