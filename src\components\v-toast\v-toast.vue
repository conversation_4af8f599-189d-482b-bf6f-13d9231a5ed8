<template>
  <v-transition type="fade" :show="isShow">
    <view class="v-toast" :style="{ bottom: bottomValue }">
      <view v-for="(text, index) in content" :key="index">
        {{ text }}
      </view>
    </view>
  </v-transition>
</template>

<script setup lang="ts">
import { isArray } from "lodash-es"

const isShow = ref(false)
const content = ref<string[]>([])
const bottomValue = ref("360rpx")

let timer: number | undefined

const showToast = ({
  message,
  bottom
}: {
  message: string | string[]
  bottom?: string
}) => {
  if (isArray(message)) {
    content.value = message
  } else if (message) {
    content.value = [message]
  } else {
    content.value = [""]
  }
  isShow.value = true
  bottomValue.value = bottom ?? "360rpx"
  clearTimeout(timer)
  timer = setTimeout(() => {
    isShow.value = false
  }, 3000)
}

onShow(() => {
  uni.$on("showToast", showToast)
})
onHide(() => {
  uni.$off("showToast", showToast)
})
</script>

<style lang="scss" scoped>
.v-toast {
  position: fixed;
  left: 0;
  transform: translateX(calc(50vw - 50%));
  bottom: 360rpx;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 26rpx;
  line-height: 36rpx;
  padding: 14rpx 60rpx;
  border-radius: 16rpx;
  text-align: center;
  z-index: 2000;
}
</style>
