<template>
  <view class="v-tabbar-item flex-col flex-center-center" @click="clickHandle">
    <v-icon v-if="isActive" size="48rpx" :src="activeIcon" />
    <v-icon v-else size="48rpx" :src="icon" />
    <view class="label" :class="{ active: isActive }">
      <text>{{ label }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps<{
  // tabbar序号 判断是否当前页
  isActive: boolean,
  // 跳转路径
  url: string,
  // 名称
  label: string,
  // 非激活图标路径
  icon: string,
  // 激活图标路径
  activeIcon: string
}>()

const url = toRef(props, "url")

const switchTab = inject<(url: string) => void>("switchTab")
const clickHandle = () => {
  if (!switchTab) return
  switchTab(url.value)
}
</script>

<style lang="scss" scoped>
.v-tabbar-item {
  height: 110rpx;
  padding: 10rpx;
  box-sizing: border-box;
  .label {
    font-size: 24rpx;
    color: #bcbcbc;
    &.active {
      color: #005bac;
    }
  }
}
</style>
