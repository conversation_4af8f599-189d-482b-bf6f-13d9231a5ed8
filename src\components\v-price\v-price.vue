<template>
  <view class="flex-baseline" @click="$emit('click')">
    <view v-if="isMinus" :style="signStyle">
      -
    </view>
    <view class="unit-sign" :style="signStyle">
      ￥
    </view>
    <view class="text-1-row">
      <text :style="integerStyle">
        {{ `${integer}.` }}
      </text>
      <text :style="decimalStyle">
        {{ decimal }}
      </text>
    </view>
    <view v-if="$slots.default" class="text-1-row" :style="unitStyle">
      <slot />
    </view>
  </view>
</template>

<script setup lang="ts">
import { str2num } from "@/utils/type-transfer"

const props = withDefaults(
  defineProps<{
    size?: string
    color?: string
    price?: number | string
    unit?: string
    bold?: boolean
  }>(),
  {
    size: "28rpx",
    color: "#E60012",
    price: undefined,
    unit: undefined,
    bold: true
  }
)

interface Emits {
  (event: "click"): void
}
defineEmits<Emits>()

const price = toRef(props, "price")
const size = toRef(props, "size")
const color = toRef(props, "color")
const bold = toRef(props, "bold")

const signStyle = computed(() => {
  const fontSize = `font-size: calc(${size.value} - 4rpx)`
  const fontWeight = bold.value ? ";font-weight: bold" : ""
  return `${fontSize}; color: ${color.value}${fontWeight}`
})
const integer = computed(() => {
  if (price.value === undefined) return "0"
  const num =
    typeof price.value === "string" ? str2num(price.value) : price.value
  if (isNaN(num)) return "0"
  const list = num.toFixed(2)?.split?.(".")
  return list?.[0] ?? "0"
})
const isMinus = computed(() => {
  if (price.value === undefined) return false
  const num =
    typeof price.value === "string" ? str2num(price.value) : price.value
  return num < 0
})
const decimal = computed(() => {
  if (price.value === undefined) return "00"
  const num =
    typeof price.value === "string" ? str2num(price.value) : price.value
  const list = num.toFixed(2)?.split?.(".")
  return list?.[1] ?? "00"
})
const integerStyle = computed(() => {
  const fontSize = `font-size: ${size.value}`
  const fontWeight = bold.value ? ";font-weight: bold" : ""
  return `${fontSize}; color: ${color.value}${fontWeight}`
})
const decimalStyle = computed(() => {
  const fontSize = `font-size: calc(${size.value} - 4rpx)`
  const fontWeight = bold.value ? ";font-weight: bold" : ""
  return `${fontSize}; color: ${color.value}${fontWeight}`
})
const unitStyle = computed(() => {
  const fontSize = `font-size: calc(${size.value} - 4rpx)`
  return `${fontSize}; color: #999999; margin-left: 8rpx`
})
</script>

<style lang="scss" scoped>
.unit-sign {
  margin: 0 -2rpx;
}
</style>
