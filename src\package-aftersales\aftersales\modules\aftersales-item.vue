<template>
  <view class="v-block">
    <view class="order-id flex-center-between" @click="navigateToDetails">
      <view class="text-light text-24">
        {{ `售后单号:${aftersales.aftersalesNo}` }}
      </view>
      <view class="flex-center">
        <!-- <template v-if="aftersales.aftersalesType === 0">
          <v-icon
            size="34rpx"
            margin-right="8rpx"
            src="/static/icons/aftersales/refund-icon.png"
          />
          <view class="text-bold">
            退款
          </view>
        </template> -->
        <v-icon
          size="34rpx"
          margin-right="8rpx"
          src="/static/icons/aftersales/refund-icon.png"
        />
        <view class="text-bold">
          退货退款
        </view>
        <!-- <template v-else>
          <v-icon
            size="34rpx"
            margin-right="8rpx"
            src="/static/icons/aftersales/exchange.png"
          />
          <view class="text-bold">
            换货
          </view>
        </template> -->
      </view>
    </view>
    <view class="divider product-margin-adjust" />
    <view class="aftersales-product" @click="navigateToDetails">
      <v-product-item
        v-for="(product, index) in aftersales.productList"
        :key="product.id"
        :product="product"
        size="small"
      >
        <template #name>
          {{ product.name }}
        </template>
        <template #spec>
          {{ product.productInfoSpec }}
        </template>
        <template #default>
          <view class="text-light text-22">
            {{ `装箱规格: ${product.displayAmount}pcs/箱` }}
          </view>
          <view class="flex-center-between">
            <view class="text-sub text-22">
              {{
                `订购数量: ${product.number}箱 | 申请数量: ${product.returnNumber}箱`
              }}
            </view>
            <view
              v-if="
                aftersales.aftersalesStatus === 9 &&
                  index === aftersales.productList.length - 1
              "
              class="text-red text-22"
            >
              {{ `已退款￥${aftersales.refundAmount.toFixed(2)}` }}
            </view>
          </view>
        </template>
      </v-product-item>
    </view>
    <view class="aftersales-status" @click="navigateToDetails">
      <view class="status-pad flex">
        <view class="text-24 text-bold" style="width: 140rpx">
          {{ status }}
        </view>
        <view class="text-light text-20 flex-1">
          {{ statusInfo }}
        </view>
      </view>
    </view>
    <view class="button-list flex-center-end">
      <v-button
        v-if="aftersales.aftersalesStatus === 1"
        height="64rpx"
        width="150rpx"
        font-size="24rpx"
        margin="0 0 0 10rpx"
        @click="navigateToDeliveryBill"
      >
        上传发货单
      </v-button>
      <!-- <v-button
        v-if="aftersales.aftersalesStatus === '2'"
        height="64rpx"
        width="150rpx"
        font-size="24rpx"
        margin="0 0 0 10rpx"
        @click="navigateToDeliveryBill"
      >
        修改发货单
      </v-button> -->
      <v-button
        v-if="[0, 1].includes(aftersales.aftersalesStatus)"
        height="64rpx"
        width="150rpx"
        font-size="24rpx"
        margin="0 0 0 10rpx"
        @click="cancelAftersales"
      >
        取消售后
      </v-button>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  aftersales: AftersalesItem
}>()

interface Emits {
  (event: "refresh"): void
}
const emits = defineEmits<Emits>()

const aftersales = toRef(props, "aftersales")

const status = computed(() => {
  if (!aftersales.value) return ""
  switch (aftersales.value.aftersalesStatus) {
    case 0:
      return "申请已提交"
    case 1:
      return "审核通过"
    case 2:
      return "审核驳回"
    case 3:
      return "商品已寄回"
    case 4:
    case 5:
    case 6:
    case 7:
      return "退款中"
    case 8:
      return "申请已取消"
    case 9:
      return "退款成功"
    default:
      return "未知状态"
  }
})

const statusInfo = computed(() => {
  if (!aftersales.value) return ""
  switch (aftersales.value.aftersalesStatus) {
    case 0:
      return "请耐心等待审核结果。"
    case 1:
      return "请将产品寄回，并输入寄回快递单号"
    case 2:
      return "审核不通过"
    case 3:
      return "请耐心等待仓库签收"
    case 4:
    case 6:
    case 7:
      return "已收到您的退回商品，请等待退款审核"
    case 5:
      return "退款将在1~3个工作日退回您的支付账户。"
    case 8:
      return "用户已取消"
    default: {
      const price = `￥${aftersales.value.refundAmount.toFixed(2)}`
      return `${price}已成功退还至您的支付账户`
    }
  }
})

const navigateToDetails = () => {
  uni.navigateTo({
    url: `/package-aftersales/aftersales/aftersales-details?id=${aftersales.value.aftersalesId}`
  })
}

/** 取消售后单 */
const cancelAftersales = async () => {
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "提示",
      content: "是否确认取消该售后申请?",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return
  uni.showLoading({ title: "加载中", mask: true })
  const response = await $api.cancelOrderRefund({
    orderBackId: aftersales.value.aftersalesId
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      uni.showToast({ title: "取消成功", mask: true })
      emits("refresh")
  }
}

const pageStore = $store.page()
const navigateToDeliveryBill = () => {
  if (!aftersales.value) return
  pageStore.setAftersalesDelivery({
    orderNo: aftersales.value.orderNo,
    aftersalesNo: aftersales.value.aftersalesNo,
    aftersalesId: aftersales.value.aftersalesId,
    aftersalesTime: aftersales.value.aftersalesTime,
    deliveryName: "",
    deliveryNo: "",
    productList: aftersales.value.productList
  })
  uni.navigateTo({ url: "/package-aftersales/aftersales/aftersales-delivery-bill" })
}
</script>

<style lang="scss" scoped>
.v-block {
  padding: 4rpx 24rpx;
}
.order-id {
  padding: 24rpx 0;
}
.aftersales-number,
.aftersales-status {
  padding: 14rpx 0;
}
.price-label {
  margin-right: 20rpx;
}
.button-list {
  padding-bottom: 14rpx;
}
.status-pad {
  padding: 24rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}
.product-margin-adjust {
  margin-bottom: 10rpx;
}
</style>
