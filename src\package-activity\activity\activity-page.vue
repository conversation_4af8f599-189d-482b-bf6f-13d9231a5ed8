<template>
  <view class="page-header background-white">
    <v-navbar title="活动中心" back />
  </view>
  <scroll-view
    v-if="pageHeight"
    scroll-y
    :style="{ height: `${pageHeight}px` }"
    enable-back-to-top
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="refresh"
    @scrolltolower="getActivityList"
  >
    <view class="page-padding-top" />
    <template v-if="!refreshing">
      <v-loading-block v-if="loading" />
      <v-empty
        v-else-if="status === 'nomore' && activityList.length === 0"
        src="/static/image/empty-order.png"
      >
        暂无活动
      </v-empty>
      <template v-else>
        <v-image
          v-for="item in activityList"
          :key="item.activityId"
          mode="scaleToFill"
          width="702rpx"
          height="320rpx"
          margin="0 auto 28rpx"
          :src="item.activityEntranceUrl"
          @click="navigatoNewActivity(item.activityNo, item.isShare)"
        />
        <v-loadmore v-if="!refreshing" :status="status" />
      </template>
    </template>
    <view class="page-padding-bottom" />
  </scroll-view>
</template>

<script setup lang="ts">
import { str2num } from "@/utils/type-transfer"

const { pageHeight } = usePageLayout()

const activityList = ref<ActivityItem[]>([])

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = () => {
  refreshing.value = true
  pageIndex.value = 1
  activityList.value = []
  status.value = "loadmore"
  getActivityList()
}
onLoad(() => {
  refresh()
})
const initOrderList = async () => {
  loading.value = true
  pageIndex.value = 1
  activityList.value = []
  status.value = "loadmore"
  await getActivityList()
  loading.value = false
}

/** 分页数据 */
const pageIndex = ref(1)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")

/** 获取订单列表 */
const getActivityList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  const response = await $api.getActivityCenterList({
    orderField: undefined,
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    sortType: "desc"
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      const list: ActivityItem[] =
        response.data.result?.map?.((item) => ({
          activityEntranceUrl: item.activityEntranceUrl,
          activityId: item.activityId,
          activityName: item.activityName,
          activityNo: item.activityNo,
          activityType: item.activityType,
          isShare: item.isShare === 1,
          releaseTime: item.releaseTime
        })) ?? []
      activityList.value = [...activityList.value, ...list]
      const totalCount = str2num(response.data.totalCount)
      status.value =
        activityList.value.length < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
}

const navigatoNewActivity = (activityNo: string, isShare: boolean) => {
  if (isShare) {
    uni.navigateTo({
      url: `/package-activity/activity/activity-details-share?activityNo=${activityNo}`
    })
  } else {
    uni.navigateTo({
      url: `/package-activity/activity/activity-details?activityNo=${activityNo}`
    })
  }
}

defineExpose({ initOrderList })
</script>

<style lang="scss" scoped>
// .image-box {
//   display: block;
//   width: 702rpx;
//   margin: 0 auto;
//   margin-bottom: 28rpx;
//   &:last-child {
//     margin-bottom: 0;
//   }
// }
</style>
