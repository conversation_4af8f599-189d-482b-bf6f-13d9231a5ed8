<template>
  <view class="history-pad">
    <view class="title flex-center-between">
      <view class="text-bold">
        搜索历史
      </view>
      <view
        class="clear-button flex-center-center"
        @click="clearSearchHistoryList"
      >
        <v-icon
          size="34rpx"
          src="/static/icons/product/delete-button.png"
        />
        <view class="text-info">
          清空
        </view>
      </view>
    </view>
    <view class="flex flex-warp">
      <view
        v-for="history in searchHistoryList"
        :key="history"
        class="history-item text-1-row"
        @click="() => clickSearchHistory(history)"
      >
        {{ history }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Emits {
  (event: "click", val: string): void
}
const emits = defineEmits<Emits>()

const searchHistoryList = ref<string[]>([])
const getSearchHistoryList = () => {
  const storage = uni.getStorageSync("search_list")
  if (!storage) return
  try {
    searchHistoryList.value = JSON.parse(storage).splice(0, 10)
  } catch {
    searchHistoryList.value = []
  }
}
onShow(() => getSearchHistoryList())

const clearSearchHistoryList = () => {
  searchHistoryList.value = []
  uni.setStorageSync("search_list", JSON.stringify(searchHistoryList.value))
}
const setSearchHistory = async (searchHistory: string) => {
  if (!searchHistory) return
  await new Promise((resolve) => setTimeout(resolve, 300))
  const index = searchHistoryList.value.findIndex(
    (item) => item === searchHistory
  )
  if (index === -1) {
    if (searchHistoryList.value.length === 10) searchHistoryList.value.pop()
    searchHistoryList.value.unshift(searchHistory)
    uni.setStorageSync("search_list", JSON.stringify(searchHistoryList.value))
  } else {
    searchHistoryList.value.splice(index, 1)
    searchHistoryList.value.unshift(searchHistory)
    uni.setStorageSync("search_list", JSON.stringify(searchHistoryList.value))
  }
}
const clickSearchHistory = (searchHistory: string) => {
  emits("click", searchHistory)
}

defineExpose({ setSearchHistory })
</script>

<style lang="scss" scoped>
.history-pad {
  padding: 40rpx 24rpx 24rpx;
  background-color: white;
  border-radius: 0 0 16rpx 16rpx;
  .title {
    margin-bottom: 30rpx;
  }
  .history-item {
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    padding: 0 28rpx;
    color: #231815;
    font-size: 24rpx;
    line-height: 54rpx;
    background-color: #f5f5f5;
    border-radius: 100vh;
  }
}
</style>
