<template>
  <view>
    <view class="page-header">
      <v-navbar :title="title" back />
    </view>
    <scroll-view
      v-if="pageHeight"
      scroll-y
      :scroll-top="scrollTop"
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
      @scroll="scrollHandle"
    >
      <view class="page-padding-top" />
      <template v-if="!refreshing">
        <v-loading-block v-if="loading" />
        <v-empty
          v-else-if="addressList.length === 0"
          src="/static/image/empty-address.png"
        >
          <view> 暂无地址 </view>
          <view class="create-container flex-center-center">
            <v-button
              type="primary"
              height="80rpx"
              font-size="28rpx"
              @click="navigateToAddressCreate"
            >
              + 新增收货地址
            </v-button>
          </view>
        </v-empty>
        <template v-else>
          <v-swipe
            v-for="(address, index) in addressList"
            :key="index"
            margin="0 24rpx 24rpx"
          >
            <template #default>
              <view class="v-block" @click="() => selectAddress(address)">
                <view class="address-info flex-center-between">
                  <view class="flex-center">
                    <v-tag
                      v-if="address.id === defaultAddressId"
                      background-color="rgba(230,0,18,0.6)"
                      margin="0 8rpx 0 0"
                    >
                      默认
                    </v-tag>
                    <view class="address-name text-32 text-bold text-1-row">
                      {{ address.name }}
                    </view>
                    <view class="text-32 text-bold text-1-row">
                      {{ address.mobile }}
                    </view>
                  </view>
                  <template v-if="showSelect">
                    <v-checkbox :checked="selectedAddressId === address.id" />
                  </template>
                </view>
                <view class="address-info">
                  <view class="text-sub text-2-row">
                    {{ address.address }}
                  </view>
                </view>
                <view class="divider" />
                <view class="address-info flex-center-between" @click.stop>
                  <view
                    class="flex-center"
                    @click="() => setDefaultAddress(address)"
                  >
                    <v-checkbox :checked="defaultAddressId === address.id" />
                    <view class="default-address-text text-sub text-24">
                      默认地址
                    </view>
                  </view>
                  <view class="edit-button">
                    <view
                      class="flex-center"
                      @click.stop="() => navigateToAddressEdit(address)"
                    >
                      <view class="edit-button-text text-sub text-24">
                        编辑
                      </view>
                      <v-icon size="34rpx" src="/static/icons/order/edit.png" />
                    </view>
                  </view>
                </view>
              </view>
            </template>
            <template #button>
              <view
                class="delete-button flex-center-center"
                @click="() => delAddress(address)"
              >
                <v-icon
                  size="36rpx"
                  src="/static/icons/components/delete.png"
                />
              </view>
            </template>
          </v-swipe>
          <view class="v-block create-container flex-center-center">
            <v-button
              type="primary"
              height="80rpx"
              font-size="28rpx"
              :disabled="addressList.length >= 10"
              @click="navigateToAddressCreate"
            >
              {{
                addressList.length >= 10 ? "地址数量已达上限" : "+ 新增收货地址"
              }}
            </v-button>
          </view>
        </template>
      </template>
      <view class="submit-button-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        v-if="!loading && showSelect"
        type="primary"
        height="94rpx"
        font-size="32rpx"
        :disabled="!selectedAddress"
        @click="submitSelect"
      >
        确定
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { safeNavigateBack, pageHeight } = usePageLayout()

const showSelect = ref(false)
const title = computed(() => {
  return showSelect.value ? "选择收货地址" : "收货地址列表"
})

const pageStore = $store.page()

const addressList = computed(() => pageStore.addressList)
const defaultAddressId = computed(() => pageStore.defaultAddressId)
const selectedAddressId = ref("")
const selectedAddress = computed(() =>
  addressList.value.find((item) => item.id === selectedAddressId.value)
)

const selectAddress = (address: Address) => {
  if (!showSelect.value) return
  selectedAddressId.value = address.id
}

onLoad(() => {
  uni.$on("select-address", (id: string) => {
    selectedAddressId.value = id
  })
})
onUnload(() => {
  uni.$off("select-address")
})

const delAddress = async (address: Address) => {
  if (addressList.value.length === 1) {
    return uni.showToast({ title: "至少要保留一个收货地址", icon: "none" })
  }
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "提示",
      content: "是否确认删除该地址?",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return
  uni.showLoading({ title: "删除中", mask: true })
  const response = await $api.delAddress({ addressId: address.id })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      uni.showToast({ title: "删除成功", icon: "none" })
      // getAddressList()
      pageStore.deleteAddress(address.id)
  }
}
const setDefaultAddress = async (address: Address) => {
  if (defaultAddressId.value === address.id) return
  uni.showLoading({ title: "切换中", mask: true })
  await pageStore.setDefaultAddress(address)
  uni.hideLoading()
  scrollToTop()
}

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = () => {
  refreshing.value = true
  getAddressList()
}

/** 控制滚动位置 */
let currentOffset = 0
const scrollTop = ref(0)
const scrollToTop = async () => {
  scrollTop.value = currentOffset
  await new Promise((resolve) => nextTick(() => resolve(true)))
  scrollTop.value = 0
}
const scrollHandle = (event: ScrollEvent) => {
  currentOffset = event.detail.scrollTop
}

const getAddressList = async () => {
  if (loading.value) return
  loading.value = true
  await pageStore.getAddressList()
  loading.value = false
  await nextTick()
  refreshing.value = false
}

const submitSelect = () => {
  if (!selectedAddress.value) return
  pageStore.selectAddress(selectedAddress.value.id)
  safeNavigateBack()
}

const navigateToAddressCreate = () => {
  uni.navigateTo({
    url: "/package-user/user/address-create-and-edit"
  })
}

const navigateToAddressEdit = (address: Address) => {
  uni.navigateTo({
    url: `/package-user/user/address-create-and-edit?id=${address.id}`
  })
}

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (query?.select === "true") {
    showSelect.value = true
    selectedAddressId.value = pageStore.selectedAddressId ?? ""
  }
  pageStore.getRegionList()
})
onShow(() => getAddressList())
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0;
}
.address-info {
  margin: 10rpx 0;
  .address-name {
    margin-right: 10rpx;
  }
}
.divider {
  padding: 10rpx 0;
}
.default-address-text {
  margin-left: 10rpx;
}
.edit-button-text {
  margin-right: 10rpx;
}
.delete-button {
  background-color: #f06671;
  border-radius: 0 18rpx 18rpx 0;
  height: 100%;
  width: 124rpx;
  margin-left: -20rpx;
  padding-left: 20rpx;
}
.create-container {
  margin: 0 24rpx 24rpx;
  padding: 30rpx 0;
}
.submit-button-blank {
  height: 94rpx;
}
.submit-button {
  position: fixed;
  padding: 0 24rpx;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
