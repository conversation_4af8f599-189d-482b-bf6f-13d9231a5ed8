function updateOpacity (instance, scrollTop, productContent) {
  var half = productContent / 2
  var percent = (scrollTop - half) / half
  var tabs = instance.selectComponent(".tabs")
  if (percent > 0) {
    tabs.setStyle({
      "pointer-events": "unset",
      opacity: percent > 1 ? 1 : percent
    })
  } else {
    tabs.setStyle({
      "pointer-events": "none",
      opacity: 0
    })
  }
}

function scrollHandle (event, ins) {
  var instance = ins || event.instance
  var state = instance.getState()
  var length = 0
  if (!state.tabHeight) {
    var container = instance.selectComponent(".product-details").getBoundingClientRect().height
    var content = instance.selectComponent(".product-details-content").getBoundingClientRect().height
    state.tabHeight = container - content
  } 
  var scrollTop = event.detail.scrollTop + state.tabHeight * 1.5
  var productContent = instance.selectComponent(".product-content").getBoundingClientRect().height
  updateOpacity(instance, scrollTop, productContent)
  length += productContent
  if (scrollTop < length) {
    if (state.current !== "product") {
      state.current = "product"
      instance.callMethod("changeTab", "product")
    }
    return
  }
  length += instance.selectComponent(".product-info-content").getBoundingClientRect().height
  var relatedGoodsList = instance.selectComponent(".related-goods-list")
  if(relatedGoodsList){
     length += relatedGoodsList.getBoundingClientRect().height
  }
  if (scrollTop < length) {
    if (state.current !== "product-info") {
      state.current = "product-info"
      instance.callMethod("changeTab", "product-info")
    }
    return
  }
  length += instance.selectComponent(".product-details-content").getBoundingClientRect().height
  if (scrollTop < length) {
    if (state.current !== "product-details") {
      state.current = "product-details"
      instance.callMethod("changeTab", "product-details")
    }
  } else {
    if (state.current !== "delivery-explain") {
      state.current = "delivery-explain"
      instance.callMethod("changeTab", "delivery-explain")
    }
  }
}

function changeTab (val, _, ownerInstance, eventInstance) {
  var instance = ownerInstance || eventInstance
  var state = instance.getState()
  state.current = val
}

module.exports = {
  scrollHandle: scrollHandle,
  changeTab: changeTab
}