/** 转换规格名为规格列表 */
export const categoryNameToList = (categoryName: string) => {
  const list = categoryName.match?.(/[(（](.+)[)|）]/)?.[1]?.split("/") ?? []
  return list.filter(item => item.length !== 0)
}

/** 获取支付描述 */
export const getProductInfoDescription = (productList: ProductCartItem[]) => {
  if (productList.length === 0) {
    return "-"
  } else if (productList.length === 1) {
    return productList[0].name
  } else {
    return `${productList[0].name} 等 共${productList.length}件`
  }
}
