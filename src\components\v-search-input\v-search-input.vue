<template>
  <view
    class="v-search-input-container flex-center-center"
    :style="{ height: height, fontSize: fontSize }"
  >
    <view class="input-container flex-1 flex-center-center">
      <v-icon
        size="32rpx"
        padding="0 0 0 16rpx"
        src="/static/icons/search.png"
      />
      <view class="search-input">
        <v-input
          v-model="searchValue"
          :focus="autoFocus"
          :maxlength="maxlength"
          @input="inputHandle"
          @blur="blurHandle"
          @confirm="confirmHandle"
        />
      </view>
      <v-button
        type="primary"
        :height="`calc(${height} - 16rpx)`"
        :font-size="fontSize"
        :disabled="!searchValue && !isFocus && !focus"
        @click="confirmHandle"
      >
        搜索
      </v-button>
    </view>
    <template v-if="!isFocus && !focus">
      <view class="placeholder-container flex--center flex-col" @click="focusHandle">
        <view v-if="!searchValue" class="placeholder flex-center-center">
          <v-icon
            size="32rpx"
            margin-right="8rpx"
            src="/static/icons/search.png"
          />
          <view class="placeholder">
            {{ placeholder }}
          </view>
        </view>
        <view v-else class="search-block flex-center">
          <view
            class="search-value"
            :style="{
              height: `calc(${height} - 16rpx)`,
              lineHeight: `calc(${height} - 16rpx)`
            }"
            @click.stop="focusHandle"
          >
            {{ searchValue }}
          </view>
          <v-icon
            size="20rpx"
            padding="16rpx"
            src="/static/icons/components/close-icon.png"
            @click.stop="clearInput"
          />
        </view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    modelValue?: string
    placeholder?: string
    maxlength?: number | string
    height?: string
    fontSize?: string
    focus?: boolean
  }>(),
  {
    modelValue: "",
    placeholder: "请输入内容",
    maxlength: 20,
    height: "70rpx",
    fontSize: "28rpx",
    focus: false
  }
)

interface Emits {
  (event: "update:model-value", val: string): void
  (event: "focus"): void
  (event: "blur"): void
  (event: "confirm", val: string): void
}
const emits = defineEmits<Emits>()

const searchValue = ref("")
const modelValue = toRef(props, "modelValue")
watch(modelValue, (val) => {
  if (val !== searchValue.value) {
    searchValue.value = val
  }
})

const focus = toRef(props, "focus")
const init = async () => {
  if (focus.value) {
    autoFocus.value = false
    await new Promise((resolve) => nextTick(() => resolve(true)))
    autoFocus.value = true
  }
}
onMounted(() => init())

const isFocus = ref(false)
const autoFocus = ref(false)
const focusHandle = async () => {
  isFocus.value = true
  await new Promise(resolve => nextTick(() => resolve(true)))
  autoFocus.value = true
  emits("focus")
}
const blurHandle = () => {
  isFocus.value = false
  autoFocus.value = false
  emits("blur")
}

const clearInput = async () => {
  searchValue.value = ""
  emits("update:model-value", "")
  await new Promise((resolve) => nextTick(() => resolve(true)))
  focusHandle()
}
const inputHandle = () => {
  emits("update:model-value", searchValue.value)
}
const confirmHandle = () => {
  emits("confirm", searchValue.value)
}
</script>

<style lang="scss" scoped>
.v-search-input-container {
  position: relative;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 100vh;
  padding: 0 8rpx;
  box-sizing: border-box;
  .search-block {
    position: absolute;
    left: 8rpx;
    background-color: #999999;
    border-radius: 100vh;
    color: white;
    padding-right: 8rpx;
    padding-left: 20rpx;
    z-index: 1;
  }
  .input-container {
    .search-input {
      padding: 0 20rpx;
      flex: 1;
    }
  }
  .placeholder-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transition: opacity 0.2s linear;
    background-color: #f5f5f5;
    border-radius: 100vh;
    z-index: 10;
    .placeholder {
      padding: 0 10rpx;
    }
  }
}
</style>
