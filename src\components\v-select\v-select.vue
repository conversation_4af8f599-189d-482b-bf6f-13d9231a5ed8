<template>
  <view clsas="v-select">
    <view
      class="flex-center"
      :class="{ padding: padding }"
      @click="$emit('update:model-value', true)"
    >
      <view
        v-if="value"
        class="value flex-1 text-1-row"
        :style="{ fontSize: fontSize, textAlign: textAlign }"
      >
        {{ value }}
      </view>
      <view
        v-else
        class="value flex-1 placeholder"
        :style="{
          fontSize: fontSize,
          marginRight: '20rpx',
          textAlign: textAlign
        }"
      >
        {{ placeholder }}
      </view>
      <view class="select-icon" :class="{ show: modelValue }">
        <v-icon size="22rpx" src="/static/icons/up-arrow.png" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    modelValue: boolean
    value: string
    padding?: string
    fontSize?: string
    placeholder?: string
    textAlign?: "left" | "center" | "right"
  }>(),
  {
    modelValue: false,
    value: "",
    padding: "0",
    fontSize: "28rpx",
    placeholder: "请选择",
    textAlign: "left"
  }
)

interface Emits {
  (event: "update:model-value"): void
}
defineEmits<Emits>()
</script>

<style lang="scss" scoped>
.value {
  margin-right: 20rpx;
}
.select-icon {
  transform-origin: 50% 50%;
  transform: rotate(180deg);
  transition: transform 0.2s linear;
  &.show {
    transform: rotate(0);
  }
}
</style>
