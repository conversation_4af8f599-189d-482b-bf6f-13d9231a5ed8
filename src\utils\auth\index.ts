let logining = false

export const login = async () => {
  if (!logining) {
    logining = true
    await uni.showModal({ title: "登录已过期", content: "请重新登录", showCancel: false })
    uni.navigateTo({ url: "/pages/user/sign-up" })
    await new Promise((resolve) => {
      uni.$once("loginFinish", (result: boolean) => resolve(result))
    })
    logining = false
  } else {
    await new Promise((resolve) => {
      uni.$once("loginFinish", (result: boolean) => resolve(result))
    })
  }
}
