<template>
  <view class="product-item" :class="{ disabled: disabledProduct }">
    <!-- <v-swipe>
      <template #default> -->
    <view class="v-block">
      <view class="flex-center">
        <view class="checkbox flex-center-center" @click="selectProduct">
          <v-checkbox :checked="isSelected" />
        </view>
        <view class="flex-1">
          <v-product-item
            :product="product"
            :is-on-sale="product.isOnSale"
            :is-in-stock="!!product?.shelvesStock"
            :is-deleted="product.isDeleted"
            @click-image="navigateToDetails"
            @click-name="$emit('select')"
            @click-spec="$emit('select')"
          >
            <template #name>
              {{ product.name }}
            </template>
            <template #spec>
              {{ product.productInfoSpec }}
            </template>
            <template #sub-button>
              <view
                class="sub-button flex-center-center"
                @click="$emit('select')"
              >
                <v-icon
                  size="36rpx"
                  src="/static/icons/cart/select-arrow.svg"
                />
              </view>
            </template>
            <template #default>
              <view class="flex-start-between">
                <view class="flex-1">
                  <view class="text-light text-24">
                    {{ `装箱规格: ${product.displayAmount}pcs/箱` }}
                  </view>
                  <view class="text-24 flex-center" style="margin-top: 6rpx">
                    <text>单价:</text>
                    <v-price
                      :price="product.displayPrice"
                      size="24rpx"
                      :color="
                        !product.isOnSale || product.shelvesStock === 0
                          ? `#999`
                          : `#E60012`
                      "
                    >
                      /pcs
                    </v-price>
                  </view>
                </view>
                <view
                  class="delete-icon-button flex-center-center"
                  @click="deleteHandle"
                >
                  <v-icon
                    size="36rpx"
                    src="/static/icons/components/delete.svg"
                  />
                </view>
              </view>
            </template>
          </v-product-item>
        </view>
      </view>
      <view class="price-and-number flex-center-between">
        <view class="flex-baseline text-sub text-26">
          <view>小计:</view>
          <v-price
            :price="
              product.isDeleted || !product.isOnSale || !product.shelvesStock
                ? 0
                : product.number * product.unitAmount * product.unitPrice
            "
            size="30rpx"
            :color="
              !product.isOnSale || product.shelvesStock === 0
                ? `#999`
                : `#E60012`
            "
          />
        </view>
        <view class="product-number" @click.stop.prevent>
          <view class="flex-center-center">
            <v-number-input
              :model-value="product.number"
              :min="1"
              :max="product.shelvesStock"
              max-text="该商品库存不足"
              :disabled="
                product.isDeleted || !product.isOnSale || !product.shelvesStock
              "
              @change="updateNumber"
            />
            <view style="margin-left: 10rpx">
              箱
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="!disabledProduct && product.number > product.shelvesStock"
        class="tips"
      >
        {{ `该商品因库存不足，现最大下单量为${product.shelvesStock}` }}
      </view>
    </view>
    <!-- </template>
      <template #button>
        <view class="delete-button flex-center-center" @click="deleteHandle">
          <v-icon size="36rpx" src="/static/icons/components/delete.png" />
        </view>
      </template>
    </v-swipe> -->
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  product: ProductCartItem
}>()
interface Emits {
  (event: "refresh"): void
  (event: "select"): void
}
const emits = defineEmits<Emits>()

const product = toRef(props, "product")

const pageStore = $store.page()

const isSelected = computed(() =>
  pageStore.selectedIdList.some((id) => id === product.value.id)
)
const disabledProduct = computed(() => {
  if (product.value.isDeleted) return true // 被删除时
  if (!product.value.isOnSale) return true // 非在售状态
  if (!product.value?.shelvesStock) return true // 没库存时
  return false
})
const selectProduct = () => {
  const shelvesStock = product.value.shelvesStock
  if (product.value.isDeleted) {
    $toast.show("该商品已下架")
    pageStore.toggleProductSelect(product.value.id, false)
    return
  }
  if (!product.value.isOnSale) {
    $toast.show("该商品已下架")
    pageStore.toggleProductSelect(product.value.id, false)
    return
  }
  if (
    shelvesStock === 0 ||
    isNaN(shelvesStock) ||
    product.value.number > shelvesStock
  ) {
    $toast.show("该商品库存不足")
    pageStore.toggleProductSelect(product.value.id, false)
    return
  }
  pageStore.toggleProductSelect(product.value.id)
}
const updateNumber = async (number: number) => {
  if (product.value.isDeleted) return $toast.show("该商品已下架")
  if (!product.value.isOnSale) return $toast.show("该商品已下架")

  // 更新库存数量
  await pageStore.updateProductShelvesStock(product.value.id)

  // 库存为0时
  if (!product.value.shelvesStock) return $toast.show("该商品库存不足")

  // 需要变更的值
  let changeNumber = number
  // 变更数量大于库存数量时
  if (number > product.value.shelvesStock) {
    // 改为变更库存最大值
    changeNumber = product.value.shelvesStock
  }

  // 直接调用接口变更数量
  const changeResult = await pageStore.updateProductNumber(
    product.value.id,
    changeNumber
  )
  // 由于原来变更数量number超过了shelvesStock
  // 这里变更成功时依然提示库存不足，提示用户系统自动变更了购物车商品数量
  if (number > product.value.shelvesStock && changeResult) {
    $toast.show("该商品库存不足")
  }
  // 依然更新失败的话，刷新购物车
  if (changeResult === false) emits("refresh")
}

const deleteHandle = async () => {
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "是否确认删除",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return
  uni.showLoading({ title: "处理中", mask: true })
  const response = await $api.deleteShoppingCar({
    productInfoIdList: [product.value.id]
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      $toast.show("删除成功")
      emits("refresh")
  }
}

const navigateToDetails = () => {
  if (product.value.isDeleted) return $toast.show("该商品已下架")
  if (!product.value.isOnSale) return $toast.show("该商品已下架")
  uni.navigateTo({
    url: `/package-product/product/product-details?productid=${product.value.id}`
  })
}
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0;
  padding-left: 0;
}
.product-item {
  margin: 0 24rpx 24rpx;
  .tips {
    padding-left: 64rpx;
    color: #e60012;
    font-size: 22rpx;
  }
  .checkbox {
    width: 64rpx;
    align-self: stretch;
  }
  .price-and-number {
    padding-top: 16rpx;
    padding-left: 64rpx;
    height: 52rpx;
    .product-number {
      display: var(--v-swipe-hide-display);
    }
  }
  &.disabled {
    color: #999999;
  }
}
.delete-button {
  background-color: #f06671;
  border-radius: 0 18rpx 18rpx 0;
  height: 100%;
  width: 124rpx;
  margin-left: -20rpx;
  padding-left: 20rpx;
}
.delete-icon-button {
  height: 50rpx;
  width: 50rpx;
  margin-right: -8rpx;
}
</style>
