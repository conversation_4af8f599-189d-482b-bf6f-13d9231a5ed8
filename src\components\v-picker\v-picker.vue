<template>
  <v-popover :model-value="modelValue" @update:model-value="updateHandle">
    <view class="v-picker-container">
      <view class="header flex-center-center">
        <view class="cancel-button" @click="hidePicker">
          取消
        </view>
        <view class="confirm-button" @click="confirmHandle">
          确定
        </view>
      </view>
      <view class="divider" />
      <picker-view
        :value="optionsIndexList"
        immediate-change
        class="picker-view"
        @change="changeHandle"
      >
        <picker-view-column
          v-for="(opt, optIndex) in optionsList"
          :key="optIndex"
        >
          <view v-for="(item, index) in opt" :key="index" class="picker-item">
            {{ item[labelKey] }}
          </view>
        </picker-view-column>
      </picker-view>
    </view>
  </v-popover>
</template>

<script setup lang="ts">
import type { Ref } from "vue"

const props = withDefaults(
  defineProps<{
    modelValue?: boolean
    options: Record<string, unknown>[]
    labelKey?: string
    childrenKey?: string
  }>(),
  {
    modelValue: false,
    options: () => [],
    labelKey: "label",
    childrenKey: "children"
  }
)

interface Emits {
  (event: "update:model-value", val: boolean): void
  (event: "change", val: Record<string, unknown>[]): void
  (event: "confirm", val: Record<string, unknown>[]): void
}
const emits = defineEmits<Emits>()

const labelKey = toRef(props, "labelKey")
const childrenKey = toRef(props, "childrenKey")

const modelValue = toRef(props, "modelValue")
let originValue: number[]
watch(modelValue, (val: boolean) => {
  if (val) asyncSetValue(originValue)
})

const asyncSetValue = async (value: number[]) => {
  if (!value.length) return
  // const list: number[] = []
  // for (let i = 0; i < value.length; i += 1) {
  //   const index = value[i]
  //   list.push(index)
  //   // await nextTick()
  //   await new Promise(resolve => setTimeout(resolve, 1000))
  //   console.log("set:", list)
  //   optionsIndexList.value = list
  // }
  await nextTick()
  optionsIndexList.value = value
}

const updateHandle = (val: boolean) => {
  if (!val) hidePicker()
}
const hidePicker = () => {
  emits("update:model-value", false)
}

type Options = Record<string, unknown>

const options = toRef(props, "options") as Ref<Options[]>
const optionsIndexList = ref<number[]>([])

const init = () => {
  if (!options.value?.length) return
  const list: number[] = []
  let rootList = options.value
  let i = 0
  while (rootList !== undefined) {
    let index = optionsIndexList.value[i]
    i += 1
    if (index === undefined || rootList[index] === undefined) {
      index = 0
    }
    list.push(index)
    const nextList = rootList?.[index]?.[childrenKey.value] as
      | Options[]
      | undefined
    if (!nextList) break
    rootList = nextList
  }
  optionsIndexList.value = list
  originValue = optionsIndexList.value
}

const setValue = (targetList: number[]) => {
  const list: number[] = []
  let nextList: Options[] | undefined = options.value
  for (let i = 0; i < targetList.length; i += 1) {
    const index = targetList[i]
    if (nextList?.[index] === undefined) {
      break
    }
    list.push(index)
    nextList = nextList?.[index]?.[childrenKey.value] as Options[] | undefined
  }
  asyncSetValue(list)
  originValue = optionsIndexList.value
}

const optionsList = computed(() => {
  if (!options.value?.length) return []
  const list: Options[][] = []
  let rootList = options.value
  let i = 0
  while (rootList !== undefined) {
    let index = optionsIndexList.value[i]
    i += 1
    list.push(rootList)
    if (index === undefined || rootList[index] === undefined) {
      index = 0
    }
    const nextList = rootList?.[index]?.[childrenKey.value] as
      | Options[]
      | undefined
    if (!nextList) break
    rootList = nextList
  }
  return list
})

const changeHandle = (event: PickerViewChangeEvent) => {
  const list = []
  const itemList = []
  const targetList = event.detail.value
  let nextList: Options[] | undefined = options.value
  for (let i = 0; i < targetList.length; i += 1) {
    const index = targetList[i]
    nextList = nextList?.[index]?.[childrenKey.value] as Options[] | undefined
    if (nextList?.[index]) itemList.push(nextList[index])
    if (optionsIndexList.value[i] === undefined) {
      list.push(index)
    } else if (optionsIndexList.value[i] !== index) {
      list.push(targetList[i])
      break
    } else {
      list.push(index)
    }
  }
  while (nextList !== undefined) {
    list.push(0)
    nextList = nextList?.[0]?.[childrenKey.value] as Options[] | undefined
  }
  optionsIndexList.value = list
  emits("change", itemList)
}

const confirmHandle = () => {
  const itemList: Options[] = []
  let nextList: Options[] | undefined = options.value
  for (let i = 0; i < optionsIndexList.value.length; i += 1) {
    const index = optionsIndexList.value[i]
    const item: Options = nextList[index]
    if (!item) break
    itemList.push(item)
    nextList = item?.[childrenKey.value] as Options[] | undefined
    if (!nextList) break
  }
  emits("confirm", itemList)
  originValue = optionsIndexList.value
  hidePicker()
}

onMounted(() => {
  watch(options, init, { immediate: true })
})

defineExpose({ setValue })
</script>

<style lang="scss" scoped>
.v-picker-container {
  background-color: white;
  border-radius: 16rpx 16rpx 0 0;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    .cancel-button,
    .confirm-button {
      color: #005bac;
      padding: 0 40rpx;
    }
  }
  .picker-view {
    height: 500rpx;
    width: 100vw;
    .picker-item {
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }
  }
}
</style>
