<template>
  <view>
    <view class="page-header background-white">
      <v-navbar title="我的优惠券" back />
      <v-tabs
        v-model="tabIndex"
        :list="['全部', '满减']"
        height="86rpx"
        active-text-style="color:#005BAC;font-weight: bold"
      >
        <view class="v-tab-block" style="transform: translateX(0)" />
      </v-tabs>
    </view>
    <swiper
      :current="tabIndex"
      :style="{ height: `${pageHeight}px` }"
      @change="tabSwiperChange"
      @transition="wxs.transition"
      @animationfinish="wxs.transitionFinish"
    >
      <swiper-item>
        <coupon-list
          ref="couponList0"
          :page-height="pageHeight"
          :type="0"
          @refresh="refreshAll"
        />
      </swiper-item>
      <swiper-item>
        <coupon-list
          ref="couponList1"
          :page-height="pageHeight"
          :type="1"
          @refresh="refreshAll"
        />
      </swiper-item>
    </swiper>

    <view class="page-footer background-white">
      <view class="page-footer-content flex-center">
        <view
          class="flex-1 flex-center-center"
          @click="navigateToHisitoryCoupon"
        >
          <v-icon
            size="50rpx"
            margin-right="10rpx"
            src="/static/icons/coupon/hisitory-icon.png"
          />
          <view> 历史优惠券 </view>
          <v-icon
            size="18rpx"
            margin-left="6rpx"
            src="/static/icons/cart/right-icon.png"
          />
        </view>
        <view class="divider-vertical" />
        <view
          class="flex-1 flex-center-center"
          @click="navigateToReceiveCoupon"
        >
          <v-icon
            size="50rpx"
            margin-right="10rpx"
            src="/static/icons/coupon/more-icon.png"
          />
          <view> 更多好券 </view>
          <v-icon
            size="18rpx"
            margin-left="6rpx"
            src="/static/icons/cart/right-icon.png"
          />
        </view>
      </view>
      <view class="popper-padding-bottom" />
    </view>
  </view>
</template>

<script lang="ts">
import CouponList from "./modules/coupon-list.vue"
import wxs from "@/utils/wxs"

export default {
  components: {
    CouponList
  },
  data () {
    return {
      pageHeight: 0,
      tabIndex: 0,
      initList: [false, false],
      wxs: {
        transition: wxs.transition,
        transitionFinish: wxs.transitionFinish
      }
    }
  },
  onLoad (query: Record<string, string>) {
    const index = parseInt(query.index)
    if (!isNaN(index)) this.tabIndex = index
  },
  onShow () {
    this.refreshAll()
  },
  onReady () {
    this.refreshPageHeight()
  },
  methods: {
    navigateToHisitoryCoupon () {
      uni.navigateTo({ url: "/package-coupon/coupon/hisitory-coupon" })
    },
    navigateToReceiveCoupon () {
      uni.navigateTo({ url: "/package-coupon/coupon/receive-coupon" })
    },
    refreshAll () {
      for (let i = 0; i < this.initList.length; i += 1) {
        this.initList[i] = false
      }
      this.initCouponList(this.tabIndex)
    },
    tabSwiperChange (event: SwiperChangeEvent) {
      this.changeTabIndex(event.detail.current)
    },
    changeTabIndex (index: number) {
      this.tabIndex = index
      this.initCouponList(index)
    },
    initCouponList (index: number) {
      const listRef = this.$refs[`couponList${index}`] as InstanceType<
        typeof CouponList
      >
      if (!listRef) return
      if (this.initList[index]) return
      this.initList[index] = true
      listRef.initCouponList()
    },
    async refreshPageHeight () {
      const system = uni.getWindowInfo()
      let screenHeight
      // #ifdef MP-WEIXIN
      screenHeight = system.screenHeight
      // #endif
      // #ifdef MP-ALIPAY
      screenHeight = system.windowHeight
      // #endif
      // #ifdef H5
      screenHeight = system.screenHeight
      // #endif
      const query = uni.createSelectorQuery()
      const headerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-header")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      const footerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-footer")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      this.pageHeight = screenHeight - headerHeight - footerHeight
    }
  }
}
</script>

<script
  src="../../components/v-tabs/v-tabs.wxs"
  module="wxs"
  lang="wxs"
></script>

<style lang="scss" scoped>
.v-tab-block {
  height: 100%;
  width: 100%;
  background-color: #005bac;
}
.page-footer-content {
  height: 80rpx;
  padding-top: 20rpx;
  .divider-vertical {
    height: 40rpx;
  }
}
</style>
