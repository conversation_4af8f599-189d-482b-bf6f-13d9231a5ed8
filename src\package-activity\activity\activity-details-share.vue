<template>
  <view class="page-header background-white">
    <v-navbar :title="activityTitle" back />
  </view>
  <scroll-view
    scroll-y
    :style="{ height: `${pageHeight}px` }"
    enable-back-to-top
  >
    <view>
      <v-image :src="activityPoster" />
      <view class="btn-box">
        <button
          v-for="item in couponList"
          :key="item.couponNo"
          class="btn"
          @click="getCoupon(item.couponNo)"
        />
      </view>
    </view>
  </scroll-view>
  <view v-if="isSuccess" class="success flex-center-center">
    领券成功
  </view>
</template>

<script setup lang="ts">
import { checkToken } from "@/utils/auth/token"
import { onShareAppMessage } from "@dcloudio/uni-app"

const { safeNavigateBack, pageHeight } = usePageLayout()
const activityNo = ref("")
const couponList = ref<
{
  /** 优惠券总数 */
  couponAmount: long
  /** 优惠券状态 */
  couponApproveStatus: number
  couponExp: {
    /** 有效天数 */
    expDays: long
    /** 有效日期区间 */
    ranges: {
      /** 有效期截止日期 */
      endTime: datetime
      /** 有效期开始日期 */
      startTime: datetime
    }[]
  }
  /** 优惠券编id */
  couponId: long
  /** 优惠券名称 */
  couponName: string
  /** 优惠券编号 */
  couponNo: string
  /** 优惠券类型 */
  couponType: number
}[]
>()
const activityTitle = ref("")
const activityPoster = ref("")
const activityName = ref("")

onShareAppMessage(() => {
  return {
    title: activityName.value,
    imageUrl: activityPoster.value,
    path: `/package-activity/activity/activity-details-share?activityNo=${activityNo.value}`
  }
})

const getActivityDetails = async () => {
  const response = await $api.getActivityCenterDetail({
    activityNo: activityNo.value
  })
  switch (response.__error) {
    case undefined: {
      activityTitle.value = response.data.activityName
      activityPoster.value = response.data.activityPoster
      couponList.value = response.data.couponList
      activityName.value = response.data.activityName
      break
    }
    default:
      safeNavigateBack()
  }
}
onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.activityNo) return
  activityNo.value = query.activityNo
  getActivityDetails()
})
// 显示领券成功
const isSuccess = ref(false)
let timeName: number
const timer = () => {
  clearTimeout(timeName)
  timeName = setTimeout(() => {
    isSuccess.value = false
  }, 1000)
}

const navigateToSignUp = () => {
  uni.navigateTo({ url: "/pages/user/sign-up" })
}
// 领券
const getCoupon = async (couponNo: string) => {
  if (!checkToken()) {
    navigateToSignUp()
    return
  }
  isSuccess.value = false
  const response = await $api.activityReceiveCoupon({
    /** 活动编号 */
    activityNo: activityNo.value,
    /** 优惠券编号 */
    couponNo
  })
  switch (response.__error) {
    case undefined: {
      isSuccess.value = true
      timer()
      break
    }
    default:
    // safeNavigateBack()
  }
}
</script>

<style lang="scss" scoped>
.image-box {
  width: 100%;
  display: block;
}
.success {
  width: 205rpx;
  height: 74rpx;
  background: #313131;
  color: white;
  border-radius: 10rpx;
  position: absolute;
  bottom: 120rpx;
  left: 50%;
  transform: translateX(-50%);
}
.btn-box {
  width: 750rpx;
  position: absolute;
  top: 900rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
  .btn {
    height: 140rpx;
    opacity: 0;
    margin-bottom: 16rpx;
  }
}
</style>
