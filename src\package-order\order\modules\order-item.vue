<template>
  <view class="v-block">
    <view class="order-id flex-center-between" @click="navigateToDetails">
      <view class="text-light text-24">
        {{ `订单编号:${order.orderNo}` }}
      </view>
      <view
        class="text-bold"
        :class="
          ['0', '1', '2', '4'].includes(order.orderStatus)
            ? 'text-red'
            : 'text-light'
        "
      >
        {{ orderStatus(order) }}
      </view>
    </view>
    <view class="divider" />
    <view class="product-container">
      <template v-if="order.productList.length === 1">
        <view class="product-content flex-1 flex" @click="navigateToDetails">
          <view class="product-item flex-center">
            <view style="margin-right: 16rpx">
              <v-image
                :src="order.productList[0].image"
                height="160rpx"
                width="160rpx"
                border
                mode="aspectFill"
              />
            </view>
            <view class="flex-1 flex-center text-bold">
              {{ order.productList[0].name }}
            </view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="product-content" @click="navigateToDetails">
          <scroll-view scroll-x class="hide-scroll-bar">
            <view class="scroller-content flex">
              <view
                v-for="(product, index) in order.productList"
                :key="index"
                class="mutiple-product-item flex-col flex--between"
              >
                <v-image
                  height="160rpx"
                  width="160rpx"
                  :src="product.image"
                  border
                  mode="aspectFill"
                />
                <view class="product-label text-24 text-bold text-1-row">
                  {{ product.name }}
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </template>
      <view class="count-pad flex-center-center" @click="navigateToDetails">
        <view class="flex-end flex-col">
          <view class="price">
            <v-price
              :price="order.shouldPayPrice"
              color="#231815"
              size="26rpx"
            />
          </view>
          <view class="text-light text-26">
            {{ `共${countProductNum(order.productList)}件` }}
          </view>
        </view>
      </view>
    </view>
    <view class="divider" />
    <view class="button-list flex-center-end">
      <view class="flex-center">
        <!-- 退款失败、退款成功、退款中 显示 -->
        <template v-if="['2', '3', '5'].includes(order.payStatus)">
          <v-button
            type="primary"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="navigateToRefundPage"
          >
            退款查询
          </v-button>
        </template>
        <template v-if="order.orderStatus === '4' && order.payStatus === '0'">
          <!-- 待支付 -->
          <v-button
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="cancelOrderHandle"
          >
            取消订单
          </v-button>
          <v-button
            type="primary"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="submitPay"
          >
            去支付
          </v-button>
        </template>
        <template v-if="order.orderStatus === '4' && order.payStatus === '1'">
          <!-- 已支付 可取消状态 -->
          <v-button
            type="primary"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="cancelOrderHandle"
          >
            取消订单
          </v-button>
        </template>
        <template v-else-if="order.orderStatus === '0'">
          <!-- 待发货 -->
          <v-button
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="cancelOrderHandle"
          >
            取消订单
          </v-button>
          <v-button
            type="primary"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="navigateToDetails"
          >
            出库中
          </v-button>
        </template>
        <template v-else-if="order.orderStatus === '1'">
          <!-- 待发货 -->
          <v-button
            v-if="order.deliveryType === 2"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="openWaybill"
          >
            查看物流
          </v-button>
          <v-button
            type="primary"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="receiveHandle"
          >
            确认收货
          </v-button>
        </template>
        <template v-else-if="order.orderStatus === '2'">
          <!-- 已完成 -->
          <v-button
            v-if="!order.invoiceNo"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="goToSelectNotIssueInvoicePage"
          >
            开具发票
          </v-button>
          <v-button
            v-if="order.ableAfterSale"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="navigateToSelectProduct"
          >
            售后申请
          </v-button>
          <v-button
            v-else-if="[0, 1].includes(order.afterSaleStatus)"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="cancelAftersales"
          >
            取消售后
          </v-button>
          <v-button
            v-if="order.orderAfterSaleId"
            type="primary"
            height="62rpx"
            width="150rpx"
            margin="0 0 0 16rpx"
            font-size="24rpx"
            @click="navigateToafterSalesDetails"
          >
            售后查询
          </v-button>
        </template>
        <template v-else-if="['3', '5'].includes(order.orderStatus)">
          <!-- 已取消 强制关闭 -->
          <view class="del-btn" @click="deleteOrderHandle">
            <v-icon
              size="34rpx"
              src="/static/icons/product/delete-button.png"
            />
            <text>删除</text>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import usePayment from "@/composables/use-payment"
import useOrder from "@/composables/use-order"

const props = defineProps<{ order: OrderItem }>()

interface Emits {
  (event: "refresh"): void
  (event: "pay"): void
  (event: "paid"): void
}
const emits = defineEmits<Emits>()

const { payOrder } = usePayment()
const { orderStatus, deleteOrder, cancelOrder, receive } = useOrder()

const order = toRef(props, "order")

const countProductNum = (product: ProductOrderItem[]) => {
  let count = 0
  product.forEach((item) => {
    count += item.number
  })
  return count
}

const pageStore = $store.page()
const submitPay = async () => {
  emits("pay")
  await pageStore.requestSubscribeMessage()
  uni.showLoading({ title: "提交中", mask: true })
  const result = await payOrder(order.value.orderNo)
  uni.hideLoading()
  if (result) {
    uni.navigateTo({
      url: `/package-order/order/pay-result?orderno=${order.value.orderNo}`
    })
  } else {
    emits("paid")
    emits("refresh")
  }
}

const openWaybill = async () => {
  const response = await $api.getOrderFollowWaybill({
    orderNo: order.value.orderNo
  })
  switch (response.__error) {
    case undefined: {
      const waybillToken = response.data?.waybillToken
      if (waybillToken) {
        const plugin = requirePlugin("logisticsPlugin")
        plugin.openWaybillTracking({
          waybillToken
        })
      } else {
        uni.showModal({
          title: "查看物流状态失败",
          content: "获取物流单信息时发生了错误",
          showCancel: false
        })
      }
      break
    }
    default:
      uni.showModal({
        title: "查看物流状态失败",
        content: "获取物流单信息时发生了错误",
        showCancel: false
      })
  }
}

const navigateToafterSalesDetails = () => {
  if (!order.value?.orderAfterSaleId) return
  uni.navigateTo({
    url: `/package-aftersales/aftersales/aftersales-details?id=${order.value.orderAfterSaleId}`
  })
}
const goToSelectNotIssueInvoicePage = () => {
  pageStore.setNotIssueInvoiceList([{ ...order.value, selected: true }])
  uni.navigateTo({ url: "/package-invoice/invoice/invoice-form" })
}
const deleteOrderHandle = async () => {
  const result = await deleteOrder(order.value.orderNo)
  if (result) {
    emits("refresh")
    uni.showToast({ title: "删除成功", icon: "none" })
  }
}

const cancelOrderHandle = async () => {
  const result = await cancelOrder(order.value.orderNo)
  if (result) {
    emits("refresh")
    uni.showToast({ title: "取消成功", icon: "none" })
  }
}

/** 取消售后单 */
const cancelAftersales = async () => {
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "提示",
      content: "是否确认取消该售后申请?",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return
  uni.showLoading({ title: "加载中", mask: true })
  const response = await $api.cancelOrderRefund({
    orderBackId: order.value?.orderAfterSaleId
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      emits("refresh")
      uni.showToast({ title: "取消成功", mask: true })
  }
}

const receiveHandle = async () => {
  const result = await receive(order.value.orderNo, order.value?.payTransactionId)
  if (result) {
    emits("refresh")
    uni.showToast({ title: "收货成功", icon: "none" })
  }
}
const navigateToDetails = () => {
  uni.navigateTo({
    url: "/package-order/order/order-details?orderno=" + order.value.orderNo
  })
}

const navigateToRefundPage = () => {
  if (!order.value) return
  uni.navigateTo({
    url: `/package-order/order/refund-page?orderno=${order.value.orderNo}`
  })
}
const navigateToSelectProduct = () => {
  if (!order.value) return
  uni.navigateTo({
    url: `/package-aftersales/aftersales/aftersales-select-type?orderno=${order.value.orderNo}`
  })
}
</script>

<style lang="scss" scoped>
.v-block {
  padding: 4rpx 24rpx;
}
.order-id {
  padding: 24rpx 14rpx;
}
.product-container {
  position: relative;
  .product-content {
    width: calc(100% - 160rpx);
  }
  .count-pad {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 140rpx;
    padding: 0 10rpx;
    box-shadow: -8px 0 6px -6px rgba(0, 0, 0, 0.15);
    background-color: white;
  }
}
.product-content {
  .product-item {
    padding: 14rpx 0;
  }
  .mutiple-product-item {
    padding: 14rpx 14rpx 14rpx 0;
    width: 174rpx;
    .product-label {
      margin-top: 10rpx;
    }
  }
}
.order-info {
  padding-top: 14rpx;
}
.button-list {
  padding: 14rpx 0;
}
.del-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  margin-left: 16rpx;
  color: #999999;
}
</style>
