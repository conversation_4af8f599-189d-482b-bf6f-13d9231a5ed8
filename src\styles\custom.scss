/** 灰色背景 */
.page-background-gray {
  background-color: #f5f5f5;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}
.page-background-white {
  background-color: white;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}
.box-shadow {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
}
.page-header {
  position: relative;
  z-index: 10;
  box-shadow: 0 4px 4px -4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s linear;
  &.hide-shadow {
    box-shadow: 0 4px 4px -4px rgba(0, 0, 0, 0);
  }
}
.page-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 0 -4px 4px -4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s linear;
  z-index: 10;
  &.hide-shadow {
    box-shadow: 0 -4px 4px -4px rgba(0, 0, 0, 0);
  }
}
.background-gray {
  background-color: #f5f5f5;
}
.background-white {
  background-color: white;
}
/** 隐藏滚动条 */
.hide-scroll-bar {
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }
}
/* 页面顶部留空 */
.page-padding-top {
  height: 24rpx;
}
/* 页面底部留空 */
.page-padding-bottom {
  height: calc(30rpx + constant(safe-area-inset-bottom));
  height: calc(30rpx + env(safe-area-inset-bottom));
}
/* 弹窗底部留空 */
.popper-padding-bottom {
  height: calc(10rpx + constant(safe-area-inset-bottom));
  height: calc(10rpx + env(safe-area-inset-bottom));
}

.divider {
  position: relative;
  height: 1px;
  &::after {
    position: absolute;
    content: " ";
    height: 1px;
    left: 0;
    right: 0;
    background-color: $divider-color;
  }
}

.divider-vertical {
  position: relative;
  width: 1px;
  &::after {
    position: absolute;
    content: " ";
    width: 1px;
    top: 0;
    bottom: 0;
    background-color: $divider-color;
  }
}

.v-block {
  padding: 16rpx 24rpx;
  margin: 0 24rpx 24rpx;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  &.plan {
    border-radius: 0;
    padding-top: 8rpx;
    padding-bottom: 8rpx;
  }
}

.v-form-item {
  padding: 20rpx 0;
  .label {
    min-width: 160rpx;
    &.required {
      position: relative;
      padding-left: 16rpx;
      &::before {
        position: absolute;
        content: "*";
        left: 0;
        color: $text-color-red;
        font-weight: bold;
      }
    }
  }
}
