import Request from "./request"
import type {
  RequestInterceptor,
  TransformRequestData,
  ResponseInterceptor
} from "./types"
import {
  requestInterceptorCatcher,
  responseInterceptorCatcher,
  errorStatusCodeCatcher,
  errorApiCodeCatcher
} from "./error-catchers"
import { isArray, isObject, cloneDeep } from "lodash-es"
import { checkToken, getToken, clearToken } from "@/utils/auth/token"
import { login } from "@/utils/auth/index"
import { baseUrl } from "@/apis/config"

/** 删除所有空字符串 */
const trim = (data: unknown): unknown => {
  if (isArray(data)) {
    const list = data as unknown[]
    return list.map((item) => trim(item))
  } else if (isObject(data)) {
    const object = {}
    const objectData = data as Record<string, unknown>
    for (const key in objectData) {
      const item = trim(objectData[key])
      Reflect.set(object, key, item)
    }
    return object
  } else if (data === "") {
    return undefined
  } else {
    return data
  }
}

/** 处理请求头 */
const getHeaders = async (
  options: RequestOptions
): Promise<Record<string, string>> => {
  // 不需要的话直接输出
  if (options.noNeedToken === true) return { ...options.headers }
  // 获取token
  const token = await getToken()
  return {
    ...options.headers,
    Authorization: token
  }
}

/** 请求拦截器 */
const requestInterceptor: RequestInterceptor = async (
  options: RequestOptions
) => {
  const opt = { ...options }
  // 用于重试的参数，如果存在该项则无视任何处理，直接请求
  if (opt?.requestOptions) return opt.requestOptions
  if (!options.noNeedToken) {
    // 检查缓存token是否可用，不可用刷新
    if (!checkToken()) await login()
    if (!checkToken()) return
  }
  // 设置请求header
  const headers = await getHeaders(opt)
  Reflect.set(opt, "headers", headers)
  return opt
}

/** 请求前数据处理 */
const transformRequestData: TransformRequestData = async (
  options: RequestOptions
) => {
  const opt = cloneDeep(options)
  // 用于重试的参数，如果存在该项则无视任何处理，直接请求
  if (opt?.requestOptions) return opt.requestOptions
  opt.data = trim(opt.data) as Record<string, unknown>
  // GET请求 删除时间戳参数
  if (opt.method === "GET") {
    if (isObject(opt.data)) {
      Reflect.deleteProperty(opt.data, "_t")
    }
  }
  // 拼接params
  if (isObject(opt.params)) {
    for (const key in opt.params) {
      const sign = opt.url.includes("?") ? "&" : "?"
      const encodeKey = encodeURIComponent(key)
      const encodeValue = encodeURIComponent(String(opt.params[key]))
      Reflect.set(opt, "url", `${opt.url}${sign}${encodeKey}=${encodeValue}`)
    }
  }
  // GET请求加时间戳防止缓存
  if (opt.method === "GET") {
    // 添加时间戳
    const time = new Date().getTime()
    const sign = opt.url.includes("?") ? "&" : "?"
    Reflect.set(opt, "url", `${opt.url}${sign}_t=${time}`)
  }
  return opt
}

/** 响应拦截器 */
const responseInterceptor: ResponseInterceptor = async (
  options: RequestOptions,
  result: Result<AnyResult>,
  response: UniNamespace.RequestSuccessCallbackResult
) => {
  // 捕获请求错误
  const statusCodeError = await errorStatusCodeCatcher(
    options,
    response.statusCode
  )
  if (statusCodeError) return statusCodeError
  // 授权问题去重新登录后直接返回一个新请求
  if ([401, 403].includes(response.statusCode)) {
    clearToken()
    await login()
    const loginResult = checkToken()
    if (!loginResult) {
      return {
        __error: "请求错误",
        __errMsg: "获取token失败",
        __requestOptions: options
      }
    }
    const headers = await getHeaders(options)
    const retryResult = await requestInstance.request({
      ...options,
      headers,
      requestOptions: { ...options, headers },
      authed: true // 避免登录循环
    })
    return retryResult
  }
  // 捕获接口错误
  const apiCodeError = await errorApiCodeCatcher(
    options,
    result as RegularResponseResult<AnyResult>,
    response
  )
  if (apiCodeError) return apiCodeError

  const data = response.data as AnyResult
  result = {
    ...data,
    __requestOptions: options,
    __error: undefined
  } as Result<AnyResult>
  return result
}

/** 请求实例 */
const requestInstance = new Request(
  {
    transformRequestData,
    requestInterceptor,
    requestInterceptorCatcher,
    responseInterceptor,
    responseInterceptorCatcher
  },
  { baseUrl, timeout: 30 * 1000 }
)
export default requestInstance
