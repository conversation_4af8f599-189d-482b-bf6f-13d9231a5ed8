<template>
  <view
    class="v-loading flex-center-center"
    :style="{
      '--v-loading-size': size,
      '--v-loading-color': color,
      '--v-loading-duration': duration
    }"
  >
    <view class="dot dot-1 flex-center-center" />
    <view class="dot dot-2 flex-center-center" />
    <view class="dot dot-3 flex-center-center" />
  </view>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  size?: string
  color?: string
  duration?: string
}>(), {
  size: "28rpx",
  color: "#005bac",
  duration: "0.6s"
})
</script>

<style lang="scss" scoped>
.v-loading {
  height: fit-content;
  width: fit-content;
  overflow: hidden;
  .dot {
    height: var(--v-loading-size);
    width: var(--v-loading-size);
    padding: calc(var(--v-loading-size) * 0.1);
    box-sizing: border-box;
    &::after {
      content: " ";
      height: 100%;
      width: 100%;
      background-color: var(--v-loading-color);
      border-radius: 50%;
      transform: scale(0%, 0%);
    }
  }
  .dot-1 {
    &::after {
      animation: loading var(--v-loading-duration) linear infinite alternate;
    }
  }
  .dot-2 {
    &::after {
      animation: loading var(--v-loading-duration)
        calc(var(--v-loading-duration) * 2 / 3) linear infinite alternate;
    }
  }
  .dot-3 {
    &::after {
      animation: loading var(--v-loading-duration)
        calc(var(--v-loading-duration) * 4 / 3) linear infinite alternate;
    }
  }
}

@keyframes loading {
  from {
    transform: scale(0%, 0%);
  }
  to {
    transform: scale(100%, 100%);
  }
}
</style>
