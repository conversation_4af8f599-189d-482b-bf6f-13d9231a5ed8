<template>
  <view class="v-textarea-container" :style="{ backgroundColor: backgroundColor }">
    <textarea
      v-model="inputValue"
      :style="customStyle"
      :type="type"
      :password="password"
      :placeholder="placeholder"
      :placeholder-style="placeholderStyle"
      :placeholder-class="placeholderClass"
      :disabled="disabled"
      :focus="focus"
      :cursor-spacing="cursorSpacing"
      @input="inputHandle"
      @focus="(event: Event) => $emit('focus', event)"
      @blur="(event: Event) => $emit('blur', event)"
      @confirm="(event: Event) => $emit('confirm', event)"
    />
    <view v-if="maxlength" class="counter">
      {{ `${inputValue.length}/${maxlength}` }}
    </view>
  </view>
</template>

<script setup lang="ts">
/** https://uniapp.dcloud.net.cn/component/input.html#input */
const props = withDefaults(defineProps<{
  modelValue: string
  replace?: RegExp
  customStyle?: string
  type?: string
  password?: boolean
  placeholder?: string
  placeholderStyle?: string
  placeholderClass?: string
  disabled?: boolean
  maxlength?: number | string
  focus?: boolean
  confirmType?: string
  backgroundColor?: string
  /** 获得焦点上划高度，单位px */
  cursorSpacing: number
}>(), {
  modelValue: "",
  replace: undefined,
  customStyle: "",
  type: undefined,
  password: false,
  placeholder: "",
  placeholderStyle: "",
  placeholderClass: "placeholder",
  disabled: false,
  maxlength: undefined,
  focus: false,
  confirmType: undefined,
  backgroundColor: "#F5F5F5"
})

interface Emits {
  (event: "update:model-value", val: string): void,
  (event: "input", val: string): void,
  (event: "focus"): void,
  (event: "blur"): void,
  (event: "confirm"): void
}
const emits = defineEmits<Emits>()

const inputValue = ref("")

const modelValue = toRef(props, "modelValue")
const replace = toRef(props, "replace")
const maxlength = toRef(props, "maxlength")

watch(modelValue, (val) => {
  if (val !== inputValue.value) {
    inputValue.value = val
  }
})

const length = computed(() => {
  switch (typeof maxlength.value) {
    case "string": {
      const length = parseInt(maxlength.value)
      return isNaN(length) ? undefined : length
    }
    case "number":
      return maxlength.value
    default:
      return undefined
  }
})
const inputHandle = async (event: Event) => {
  const value = (event as InputEvent).detail.value
  inputValue.value = value
  let formated = replace.value ? value.replace(replace.value, "") : value
  if (length.value !== undefined) formated = formated.slice(0, length.value)
  await new Promise((resolve) => nextTick(() => resolve(true)))
  inputValue.value = formated
  emits("update:model-value", formated)
  await new Promise((resolve) => nextTick(() => resolve(true)))
  emits("input", formated)
}
</script>

<style lang="scss" scoped>
.v-textarea-container {
  position: relative;
  padding: 12rpx 12rpx 24rpx;
  border-radius: 16rpx;
  .counter {
    position: absolute;
    right: 12rpx;
    bottom: 0;
    color: #999;
    font-size: 20rpx;
  }
}
</style>
