<template>
  <view class="v-product-block">
    <v-image
      height="340rpx"
      width="340rpx"
      :src="product.image"
      mode="aspectFill"
      lazy-load
      @click="navigateToDetails"
    />
    <view v-if="show" class="content">
      <view class="product-name text-1-row" @click="navigateToDetails">
        {{ product.name || product.title }}
      </view>
      <view class="category-list" @click="navigateToDetails">
        <v-category-list :list="product.categoryList" />
      </view>
      <view class="product-info flex-center-between">
        <v-price :price="product.displayPrice">
          /pcs
        </v-price>
        <v-icon
          size="48rpx"
          src="/static/icons/product/cart-button.png"
          @click.stop="clickHandle"
        />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { Ref } from "vue"

const props = withDefaults(
  defineProps<{
    product: ProductBlock
    show?: boolean
  }>(),
  {
    show: true
  }
)

interface Emits {
  (event: "select"): void
}
const emits = defineEmits<Emits>()

const product = toRef(props, "product") as Ref<ProductBlock>

const clickHandle = () => {
  emits("select")
}

const navigateToDetails = () => {
  uni.navigateTo({
    url: `/package-product/product/product-details?productid=${product.value.id}`
  })
}
</script>

<style lang="scss" scoped>
.v-product-block {
  width: 340rpx;
  height: 540rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #fff;
  .content {
    height: calc(100% - 340rpx);
    width: 100%;
    overflow: hidden;
    .product-name {
      margin: 16rpx;
    }
    .category-list {
      margin: 0 16rpx;
      height: 34rpx;
    }
    .product-info {
      height: 96rpx;
      margin: 0 16rpx;
    }
  }
}
</style>
