<template>
  <view
    class="v-number-input flex"
    :style="{ height: height, width: width }"
    @click.stop.prevent
  >
    <view
      class="decrease-button flex-center-center"
      :style="{ width: buttonWidth }"
      @click="decreaseHandle"
    >
      <view
        class="text"
        :class="{ disabled: disabled || decreaseDisabled }"
        :style="{ lineHeight: buttonSize, fontSize: buttonSize }"
      >
        -
      </view>
    </view>
    <view class="input-container flex-center">
      <input
        v-if="!disabled"
        v-model="value"
        class="text-center"
        :style="{
          fontSize: fontSize,
          color: color,
          fontWeight: bold ? 'bold' : 'normal'
        }"
        :type="type"
        @input="inputHandle"
        @blur="inputBlur"
      >
      <view
        v-else
        class="disalbed-placeholder"
        :style="{ fontSize: fontSize }"
      >
        0
      </view>
    </view>
    <view
      class="increase-button flex-center-center"
      :style="{ width: buttonWidth }"
      @click="increaseHandle"
    >
      <view
        class="text"
        :class="{ disabled: disabled || increaseDisabled }"
        :style="{ lineHeight: buttonSize, fontSize: buttonSize }"
      >
        +
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    modelValue: number
    type?: string
    height?: string
    width?: string
    buttonWidth?: string
    color?: string
    bold?: boolean
    fontSize?: string
    max?: number
    min?: number
    buttonSize?: string
    disabled?: boolean
    maxText?: string
    minText?: string
  }>(),
  {
    modelValue: 0,
    type: "number",
    height: "52rpx",
    width: "200rpx",
    buttonWidth: "58rpx",
    color: "#231815",
    bold: false,
    fontSize: "28rpx",
    max: undefined,
    min: undefined,
    buttonSize: "40rpx",
    disabled: false,
    maxText: undefined,
    minText: undefined
  }
)

interface Emits {
  (event: "update:model-value", val: number): void
  (event: "change", val: number): void
}
const emits = defineEmits<Emits>()

const value = ref<string | number>(0)

const max = toRef(props, "max")
const min = toRef(props, "min")
const maxText = toRef(props, "maxText")
const minText = toRef(props, "minText")

const disabled = toRef(props, "disabled")

const inputHandle = async (event: Event) => {
  const string =
    typeof value.value === "string" ? value.value : value.value.toString()
  const formated = string.replace(/[^\d-]/g, "")
  if (string !== formated) {
    value.value = (event as InputEvent).detail.value
    await new Promise((resolve) => nextTick(() => resolve(true)))
    value.value = formated
  }
}

const inputBlur = async () => {
  const number =
    typeof value.value === "string" ? parseInt(value.value) : value.value
  switch (true) {
    case isNaN(number):
      break
    case max.value !== undefined && number > max.value:
      await new Promise((resolve) => nextTick(() => resolve(true)))
      value.value = (max.value as number).toString()
      if (maxText.value) $toast.show(maxText.value)
      emits("update:model-value", max.value as number)
      emits("change", max.value as number)
      break
    case min.value !== undefined && number < min.value:
      value.value = (min.value as number).toString()
      if (minText.value) $toast.show(minText.value)
      emits("update:model-value", min.value as number)
      emits("change", min.value as number)
      break
    default:
      emits("update:model-value", number)
      emits("change", number)
  }
}

const modelValue = toRef(props, "modelValue")
watch(
  modelValue,
  (val) => {
    value.value = val
  },
  { immediate: true }
)

const decreaseDisabled = computed(
  () => min.value !== undefined && modelValue.value <= min.value
)

const increaseDisabled = computed(
  () => max.value !== undefined && modelValue.value >= max.value
)

const decreaseHandle = () => {
  if (disabled.value) return
  if (decreaseDisabled.value) {
    if (minText.value) $toast.show(minText.value)
    return
  }
  emits("update:model-value", modelValue.value - 1)
  emits("change", modelValue.value - 1)
}
const increaseHandle = () => {
  if (disabled.value) return
  if (increaseDisabled.value) {
    if (maxText.value) $toast.show(maxText.value)
    return
  }
  emits("update:model-value", modelValue.value + 1)
  emits("change", modelValue.value + 1)
}

const forceUpdate = (val: number) => {
  value.value = val
}

defineExpose({ forceUpdate })
</script>

<style lang="scss" scoped>
.v-number-input {
  box-sizing: border-box;
  border: 1px solid $border-color-light;
  border-radius: 100vh;
  .decrease-button {
    box-sizing: border-box;
    text-align: center;
    .text {
      transform: translateY(-7%);
      &.disabled {
        color: #d0d0d0;
      }
    }
  }
  .input-container {
    flex: 1;
    padding: 0 10rpx;
    border-right: 1px solid $border-color-light;
    border-left: 1px solid $border-color-light;
    .disalbed-placeholder {
      width: 100%;
      text-align: center;
      color: #ccc;
    }
  }
  .increase-button {
    box-sizing: border-box;
    text-align: center;
    .text {
      color: #231815;
      transform: translateY(-7%);
      &.disabled {
        color: #d0d0d0;
      }
    }
  }
}
</style>
