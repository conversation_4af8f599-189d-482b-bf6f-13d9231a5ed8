<template>
  <view
    class="v-uploader flex-center flex-warp"
    :style="`--v-uploader-size: ${size}`"
  >
    <VUploaderItem
      v-for="(image, imageIndex) in imageList"
      :key="imageIndex"
      :status="image.status"
      :url="image.url"
      :src="image.src"
      :image-index="imageIndex"
    />
    <VUploaderInsert v-if="imageList.length < maxLength" />
    <view v-if="maxLength" class="counter">
      {{ `${imageList.length}/${maxLength}` }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { checkToken, getToken } from "@/utils/auth/token"
import type { UploaderStatus, UploaderItem } from "./modules/types"
import VUploaderInsert from "./modules/v-uploader-insert.vue"
import VUploaderItem from "./modules/v-uploader-item.vue"

const props = withDefaults(
  defineProps<{
    size?: string
    maxSize?: number
    maxLength?: number
    limitType?: string[]
  }>(),
  {
    size: "140rpx",
    maxSize: 5 * 1024 * 1024,
    maxLength: 3,
    limitType: () => ["png", "jpg", "jpeg", "image"]
  }
)

interface Emits {
  (event: "change", list: UploaderItem[]): void
}
const emits = defineEmits<Emits>()

const maxSize = toRef(props, "maxSize")
const maxLength = toRef(props, "maxLength")
const limitType = toRef(props, "limitType")

const imageList = ref<UploaderItem[]>([])

const chooseImage = async (count: number) => {
  const choosenFile = await new Promise<
  UniApp.ChooseImageSuccessCallbackResult | undefined
  >((resolve) =>
    uni.chooseImage({
      count,
      sourceType: ["album", "camera"],
      extension: limitType.value,
      sizeType: ["compressed"],
      success: resolve,
      fail: () => resolve(undefined)
    })
  )

  // let tempFiles: { path: string; size: number }[] | undefined

  // #ifdef MP-WEIXIN
  const tempFiles = choosenFile?.tempFiles as
    | { path: string; size: number }[]
    | undefined
  // #endif
  if (!tempFiles) return
  for (let i = tempFiles.length - 1; i >= 0; i -= 1) {
    const { path, size } = tempFiles[i]
    const fileExt = path.replace(/.+\./, "").toLowerCase()
    if (!limitType.value.some((ext) => ext.toLowerCase() === fileExt)) {
      uni.showToast({ title: `不允许选择${fileExt}格式的文件`, icon: "none" })
      return
    }
    if (size > maxSize.value) {
      uni.showToast({ title: "超出上传大小", icon: "none" })
      tempFiles.splice(i, 1)
    }
  }
  return tempFiles
}

const insertImage = async () => {
  const insertList = await chooseImage(maxLength.value - imageList.value.length)
  if (!insertList?.length) return
  imageList.value.push(
    ...insertList.map((image) => ({
      status: "before-upload" as UploaderStatus,
      src: image.path,
      url: "",
      id: ""
    }))
  )
}

const changeImage = async (imageIndex: number) => {
  const { status } = imageList.value[imageIndex]
  if (status === "loading") {
    return uni.showToast({ title: "上传中，请稍后", icon: "none" })
  }
  const insertList = await chooseImage(1)
  if (!insertList?.length) return
  imageList.value.splice(
    imageIndex,
    1,
    ...insertList.map((image) => ({
      status: "before-upload" as UploaderStatus,
      src: image.path,
      url: "",
      id: ""
    }))
  )
}

const deleteImage = (imageIndex: number) => {
  imageList.value.splice(imageIndex, 1)
}

const upload = async (imageIndex: number) => {
  const item = imageList.value[imageIndex]
  item.status = "loading"
  if (!checkToken()) {
    item.status = "error"
    return
  }
  const token = await getToken()
  uni.uploadFile({
    url: $api.config.baseUrl + "/wxapp/member/tempUploadFile",
    filePath: item.src,
    fileType: "image",
    name: "fileList",
    header: { Authorization: token },
    timeout: 15000,
    success: (res) => {
      const response = JSON.parse(res.data)
      if (response.code === "2000" && response.data?.[0]) {
        item.status = "success"
        item.url = response.data[0].fileUrl
        item.id = response.data[0].fileId
      } else {
        item.status = "error"
      }
    },
    fail: (err) => {
      console.log("uploader err", err)
      item.status = "error"
    }
  })
}

const setImageList = (list: string[]) => {
  imageList.value = list.map((image) => ({
    status: "success",
    src: image,
    url: image,
    id: ""
  }))
}

onMounted(() => {
  watch(
    imageList,
    () => {
      imageList.value.forEach((item, index) => {
        if (item.status === "before-upload") {
          upload(index)
        }
        emits("change", imageList.value)
      })
    },
    { immediate: true, deep: true }
  )
})

provide("insertImage", insertImage)
provide("changeImage", changeImage)
provide("deleteImage", deleteImage)

defineExpose({ setImageList })
</script>

<style lang="scss" scoped>
.v-uploader {
  position: relative;
  padding-bottom: 24rpx;
  .counter {
    position: absolute;
    right: 4rpx;
    bottom: 0;
    color: #999;
    font-size: 20rpx;
  }
}
</style>
