<template>
  <view>
    <view class="page-header hide-shadow">
      <v-navbar title="商品详情" back />
    </view>
    <v-loading-block v-if="!product" />
    <template v-else>
      <ProductDetailsContent
        :page-height="pageHeight"
        :product="product"
        :related-goods-list="relatedGoodsList as RelatedGoods[]"
        @show-select="showSelect"
      />
    </template>
    <v-product-select ref="selectRef" @select="selectHandle" />
    <view class="page-footer">
      <view class="page-footer-container">
        <view class="page-footer-content flex-center">
          <view class="menu-list flex-1 flex-center-between">
            <!-- <view class="flex-col flex-center-center" @click="customerService">
              <v-icon
                size="44rpx"
                src="/static/icons/product/client-service-icon.png"
              />
              <view class="menu-label text-20">
                客服
              </view>
            </view> -->
            <button
              class="flex-col flex-center-center plain-button"
              open-type="contact"
              send-message-title="商品详情"
              :send-message-path="`/package-product/product/product-details.html?productid=${productId}&productno=${product?.productSpecNumber}&customer=1`"
              :send-message-img="logoSrc"
              show-message-card
            >
              <v-icon
                size="44rpx"
                src="/static/icons/product/client-service-icon.png"
              />
              <view class="menu-label text-20">
                客服
              </view>
            </button>
            <view
              class="flex-col flex-center-center"
              @click="navigateToCartList"
            >
              <v-icon size="44rpx" src="/static/icons/product/cart-icon.png" />
              <view class="menu-label text-20">
                购物车
              </view>
            </view>
            <view class="flex-col flex-center-center" @click="toggleCollection">
              <v-icon
                size="44rpx"
                :active="isCollected"
                src="/static/icons/product/collection-icon.png"
                active-src="/static/icons/product/collection-icon-active.png"
              />
              <view class="menu-label text-20">
                收藏
              </view>
            </view>
          </view>
          <view class="button-list flex-center">
            <v-button
              type="danger"
              height="82rpx"
              width="206rpx"
              font-size="28rpx"
              margin="0 8rpx 0 0"
              @click="showSelect"
            >
              加入购物车
            </v-button>
            <v-button
              type="primary"
              height="82rpx"
              width="206rpx"
              font-size="28rpx"
              @click="showSelect"
            >
              立即购买
            </v-button>
          </view>
        </view>
        <view class="page-padding-bottom" />
      </view>
      <view class="page-footer-blank" />
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import ProductDetailsContent from "./modules/product-details-content.vue"
import { str2num } from "@/utils/type-transfer"
import { checkToken } from "@/utils/auth/token"

const { safeNavigateBack, pageHeight } = usePageLayout()

/** 商品信息 */
const productId = ref<string>("")
const product = ref<ProductDetails | undefined>()
const relatedGoodsList = ref<RelatedGoods[] | undefined>()
const number = ref(1)
const getProductDetails = async () => {
  if (productId.value === undefined) {
    await uni.showModal({
      title: "页面错误",
      content: "商品编号丢失",
      showCancel: false
    })
    return safeNavigateBack()
  }
  const response = await $api.productDetail({ productInfoId: productId.value })
  switch (response.__error) {
    case undefined: {
      if (response.code === "50006") {
        await uni.showModal({
          title: "该商品已下架",
          content: "请看看其他商品～",
          showCancel: false
        })
        safeNavigateBack()
      } else {
        const res = await $api.getCategoryRelationList({
          categoryId: response.data.categoryId
        })
        switch (res.__error) {
          case undefined: {
            relatedGoodsList.value = res.data.map((item) => ({ ...item }))
          }
        }
        const unitAmount = str2num(response.data.ncPackage)
        const stockNum = str2num(response.data.shelvesStock)
        const stockCount = isNaN(stockNum) ? 0 : stockNum
        product.value = {
          ...response.data,
          unitPrice: str2num(response.data.productPrice),
          unitAmount,
          displayPrice: str2num(response.data.displayUnitPrice),
          displayAmount: response.data.displayPackage,
          marketPrice: str2num(response.data.marketPrice),
          isOnSale: response.data.isOnSale === "1",
          shelvesStock: Math.floor(Math.max(stockCount, 0) / unitAmount)
        }
        productId.value = response.data.id
      }
      break
    }
    default:
      safeNavigateBack()
  }
}

/** 点击规格选择 */
const selectRef = ref()
const showSelect = () => {
  if (!product.value) return
  // 检查用户是否已登录，如果未登录则跳转到登录页面
  if (!checkToken()) return navigateToSignUp()
  selectRef.value.showSelect({ details: product.value, number: number.value })
}
const selectHandle = (
  selectedProduct: ProductDetails,
  selectedNumber: number
) => {
  product.value = selectedProduct
  productId.value = selectedProduct.id
  number.value = selectedNumber
}

const navigateToSignUp = () => {
  uni.navigateTo({ url: "/pages/user/sign-up" })
}

/** 获取商品是否被收藏 */
const isCollected = ref(false)
const getCollection = async () => {
  if (!productId.value) return
  if (!checkToken()) return
  const response = await $api.isCollectProduct({
    productInfoId: productId.value
  })
  switch (response.__error) {
    case undefined:
      isCollected.value = response.data.isCollected
  }
}
let changingCollection = false
const toggleCollection = async () => {
  if (!product.value) return
  if (!checkToken()) return navigateToSignUp()
  if (changingCollection) return
  changingCollection = true
  if (isCollected.value) {
    await delCollection()
  } else {
    await setCollection()
  }
  changingCollection = false
}
const setCollection = async () => {
  if (!productId.value) return
  const response = await $api.saveMemberCollect({
    productInfoId: productId.value
  })
  switch (response.__error) {
    case undefined:
      uni.showToast({ title: "收藏成功", icon: "none" })
      isCollected.value = true
  }
}
const delCollection = async () => {
  if (!productId.value) return
  const response = await $api.deleteMemberCollect({
    productInfoId: productId.value
  })
  switch (response.__error) {
    case undefined:
      uni.showToast({ title: "取消收藏成功", icon: "none" })
      isCollected.value = false
  }
}

/** 进入时获取详情 */
onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.productid) {
    safeNavigateBack()
  } else if (query?.customer && !checkToken()) {
    const number = query.productno || productId.value
    uni.redirectTo({
      url: `/pages/customer/customer-service?source=${query.customer}&number=${number}`
    })
  } else {
    productId.value = query.productid
    getProductDetails()
  }
})

let unwatch: () => void
onShow(() => {
  unwatch?.()
  unwatch = watch(productId, getCollection, { immediate: true })
})
onHide(() => {
  unwatch?.()
})
onUnload(() => {
  unwatch?.()
})

// const customerService = () => {
//   if (!productId.value) return
//   const number = product.value?.productSpecNumber ?? ""
//   uni.openCustomerServiceChat({
//     extInfo: { url: $api.config.customerUrl },
//     corpId: $api.config.corpId,
//     showMessageCard: true,
//     sendMessageTitle: product.value?.name,
//     sendMessagePath: `/package-product/product/product-details.html?productid=${productId.value}&productno=${number}&customer=1`,
//     sendMessageImg: product.value?.productImg,
//     fail: (err) => console.log(err)
//   })
// }

/** 跳转购物车 */
const navigateToCartList = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/pages/cart/cart-list" })
}


import { webName } from '@/apis/config'

const logoSrc = computed(() => {
  if (webName === 'cheerray') {
    return "/static/image/service-logo-cheerray.png"
  } else {
    return "/static/image/service-logo.png"
  }
})
</script>

<style lang="scss" scoped>
.page-footer {
  background-color: white;
  .page-footer-blank {
    height: 106rpx;
  }
  .page-footer-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    .page-footer-content {
      height: 106rpx;
      padding-right: 24rpx;
      .menu-list {
        padding: 0 36rpx;
        .menu-label {
          margin-top: 4rpx;
        }
      }
    }
  }
}
</style>
