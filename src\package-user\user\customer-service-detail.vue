<template>
  <view>
    <view class="page-header">
      <v-navbar :title="title" back />
    </view>
    <view class="background" />
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <v-parse :content="contentData" />
      <view class="page-padding-bottom" />
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
const { safeNavigateBack, pageHeight } = usePageLayout()
const contentData = ref("")
const contentType = ref<number>()

const title = computed(() => {
  if (contentType.value === undefined) return ""
  switch (contentType.value) {
    case 0:
      return "第三方配送服务说明"
    case 1:
      return "价格说明"
    case 2:
      return "订购说明"
    case 3:
      return "售后政策说明"
    case 4:
      return "售后退款说明"
    case 5:
      return "隐私协议"
    case 6:
      return "用户服务协议"
    case 7:
      return "发票说明"
    case 8:
      return "积分规则"
  }
  return ""
})

const getContent = async () => {
  const response = await $api.getSysContentManage({
    contentType: contentType.value
  })
  switch (response.__error) {
    case undefined: {
      contentData.value = response.data.content
    }
  }
}

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.type) {
    safeNavigateBack()
  } else {
    contentType.value = parseInt(query.type)
    getContent()
  }
})
</script>

<style lang="scss" scoped>
.background {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: white;
  z-index: -1;
}
</style>
