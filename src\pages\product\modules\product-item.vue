<template>
  <view class="flex-center">
    <v-image
      height="140rpx"
      width="140rpx"
      :src="product.image"
      border
      mode="aspectFill"
      @click="navigateToDetails"
    />
    <view class="product-info flex-1">
      <view class="product-name text-1-row" @click="navigateToDetails">
        {{ product.name }}
      </view>
      <view class="category-list" @click="navigateToDetails">
        <v-category-list :list="product.categoryList" />
      </view>
      <view class="flex-center-between">
        <v-price :price="product.displayPrice" @click="navigateToDetails">
          /pcs
        </v-price>
        <v-icon
          size="46rpx"
          src="/static/icons/product/cart-button.png"
          @click="$emit('select')"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  product: ProductCategoryItem
}>()

interface Emits {
  (event: "select"): void
}
defineEmits<Emits>()

const product = toRef(props, "product")

const navigateToDetails = () => {
  uni.navigateTo({
    url: `/package-product/product/product-details?productid=${product.value.id}`
  })
}
</script>

<style lang="scss" scoped>
.product-info {
  margin-left: 14rpx;
  .product-name {
    margin-bottom: 12rpx;
  }
  .category-list {
    margin-bottom: 20rpx;
    height: 34rpx;
  }
}
</style>
