<template>
  <view>
    <view class="page-header">
      <v-navbar title="售后申请" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <v-loading-block v-if="!order" />
      <view v-else class="v-block order-item">
        <view class="order-id text-light text-24">
          {{ `订单编号:${order.orderNo}` }}
        </view>
        <view class="divider" />
        <template
          v-for="(product, productIndex) in order.productList"
          :key="product.id"
        >
          <view v-if="productIndex !== 0" class="divider" />
          <view class="product-item" @click="selectedSingle(product)">
            <v-product-item :product="product" size="small">
              <template #checkbox>
                <v-checkbox :checked="product.selected" />
              </template>
              <template #name>
                {{ product.name }}
              </template>
              <template #spec>
                {{ product.productInfoSpec }}
              </template>
              <template #default>
                <view class="text-light text-22">
                  {{ `装箱规格: ${product.displayAmount}pcs/箱` }}
                </view>
                <view class="text-sub text-22">
                  {{ `订购数量: ${product.quantity}箱` }}
                </view>
              </template>
            </v-product-item>
          </view>
        </template>
      </view>
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="aftersales-select-handle">
      <view class="handle-select">
        <view class="select-all flex-center" @click="selectAll">
          <v-checkbox
            :checked="checkboxStatus === 'all'"
            :indeterminate="checkboxStatus === 'indeterminate'"
          />
          <view class="selected-all-style">
            全选
          </view>
        </view>
        <view class="selected-product-notice flex-baseline">
          <view>已选</view>
          <view style="margin: 0 4rpx">
            {{ selectedProductCount }}
          </view>
          <view>件商品</view>
        </view>
      </view>
      <view class="next-btn" @click="redirectToRefund">
        下一步
      </view>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { pageHeight } = usePageLayout()
const order = computed(() => pageStore.afterSalesOrder)
const pageStore = $store.page()
const checkboxStatus = ref("none")
const selectedProductCount = ref(0)
const selectedSingle = async (product: AftersalesOrderProductItem) => {
  product.selected = !product.selected
  let count = 0
  order.value?.productList.forEach((item) => {
    if (item.selected === true) count++
  })
  if (count === order.value?.productList.length) {
    checkboxStatus.value = "all"
  } else {
    checkboxStatus.value = "none"
  }
  selectedProductCount.value = count
}
const selectAll = async () => {
  checkboxStatus.value = checkboxStatus.value === "none" ? "all" : "none"
  order.value?.productList.forEach((item) => {
    item.selected = checkboxStatus.value !== "none"
  })
  if (checkboxStatus.value === "none") {
    selectedProductCount.value = 0
  } else {
    selectedProductCount.value = order.value?.productList.length as number
  }
}
onMounted(() => {
  let count = 0
  order.value?.productList.forEach((item) => {
    if (item.selected === true) count++
  })
  if (count === order.value?.productList.length) {
    checkboxStatus.value = "all"
  } else {
    checkboxStatus.value = "none"
  }
  selectedProductCount.value = count
})
const redirectToRefund = () => {
  if (selectedProductCount.value === 0) {
    return $toast.show("至少选择一件商品进行退货退款")
  }
  uni.redirectTo({ url: "/package-aftersales/aftersales/aftersales-refund" })
}
</script>

<style lang="scss" scoped>
.order-item {
  padding: 4rpx 24rpx;
}
.order-id,
.product-item {
  padding: 14rpx 0;
  .tag-container {
    height: 62rpx;
  }
}
.aftersales-select-handle {
  position: fixed;
  left: 24rpx;
  right: 24rpx;
  bottom: 0;
  z-index: 10;
  .next-btn {
    display: flex;
    margin-top: 32rpx;
    justify-content: center;
    align-items: center;
    height: 94rpx;
    font-size: 32rpx;
    border-radius: 47rpx 47rpx;
    background-color: #005bac;
    color: #fff;
  }
}
.spec-style {
  color: #999999;
  font-size: 24rpx;
}
.count-bottom-style {
  color: #666666;
  font-size: 24rpx;
}
.handle-select {
  display: flex;
  justify-content: space-between;
  .selected-product-notice {
    font-size: 24rpx;
    color: #666;
  }
  .selected-all-style {
    font-size: 24rpx;
    color: #666;
    padding: 0 10rpx;
  }
}
</style>
