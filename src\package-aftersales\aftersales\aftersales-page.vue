<template>
  <view>
    <view class="page-header background-white">
      <v-navbar title="售后/退换" back />
      <v-tabs v-model="tabIndex" :list="['处理中', '已完成']" height="86rpx">
        <view class="v-tab-block" style="transform: translateX(0)" />
      </v-tabs>
    </view>
    <swiper
      :current="tabIndex"
      :style="{ height: `${pageHeight}px` }"
      @change="tabSwiperChange"
      @transition="wxs.transition"
      @animationfinish="wxs.transitionFinish"
    >
      <swiper-item>
        <aftersales-list
          ref="AftersalesList0"
          :page-height="pageHeight"
          :order-status="[0, 1, 3, 4, 5, 6, 7]"
        />
      </swiper-item>
      <swiper-item>
        <aftersales-list
          ref="AftersalesList1"
          :page-height="pageHeight"
          :order-status="[2, 8, 9]"
        />
      </swiper-item>
    </swiper>
    <v-toast />
  </view>
</template>

<script lang="ts">
import AftersalesList from "./modules/aftersales-list.vue"
import wxs from "@/utils/wxs"

export default {
  components: {
    AftersalesList
  },
  data () {
    return {
      pageHeight: 0,
      tabIndex: 0,
      wxs: {
        transition: wxs.transition,
        transitionFinish: wxs.transitionFinish
      }
    }
  },
  onReady () {
    this.refreshPageHeight()
  },
  onShow () {
    const listRef = this.$refs.AftersalesList0 as InstanceType<
      typeof AftersalesList
    >
    listRef.initAftersalesList()
  },
  methods: {
    tabSwiperChange (event: SwiperChangeEvent) {
      this.tabIndex = event?.detail?.current
      const listRef = this.$refs[
        `AftersalesList${event?.detail?.current}`
      ] as InstanceType<typeof AftersalesList>
      listRef.initAftersalesList()
    },
    async refreshPageHeight () {
      const system = uni.getWindowInfo()
      let screenHeight
      // #ifdef MP-WEIXIN
      screenHeight = system.screenHeight
      // #endif
      // #ifdef MP-ALIPAY
      screenHeight = system.windowHeight
      // #endif
      // #ifdef H5
      screenHeight = system.screenHeight
      // #endif
      const query = uni.createSelectorQuery()
      const headerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-header")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      this.pageHeight = screenHeight - headerHeight
    }
  }
}
</script>

<script
  src="../../components/v-tabs/v-tabs.wxs"
  module="wxs"
  lang="wxs"
></script>

<style lang="scss" scoped>
.v-tab-block {
  height: 100%;
  width: 100%;
  background-color: #005bac;
}
</style>
