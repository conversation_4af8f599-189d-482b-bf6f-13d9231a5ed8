<template>
  <view class="v-counter">
    <template v-if="restHour">
      <view class="hour">
        {{ restHour }}
      </view>
      <view class="sign">
        :
      </view>
    </template>
    <view class="minute">
      {{ restMinute }}
    </view>
    <view class="sign">
      :
    </view>
    <view class="second">
      {{ restSecond }}
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  time: number
}>()
interface Emits {
  (event: "finish"): void
}
const emits = defineEmits<Emits>()

const time = toRef(props, "time")
onMounted(() => {
  watch(time, (val) => {
    restTime.value = val > 0 ? val : 0
    startInterval()
  }, { immediate: true })
})

const restTime = ref(0)
const restHour = computed(() =>
  restTime.value <= 3600 ? undefined : String(Math.floor(restTime.value / 3600))
)
const restMinute = computed(() =>
  String(Math.floor((restTime.value % 3600) / 60)).replace(/(?=\b\d\b)/g, "0")
)
const restSecond = computed(() =>
  String(restTime.value % 60).replace(/(?=\b\d\b)/g, "0")
)

let interval: number | undefined
const startInterval = () => {
  if (interval) {
    clearInterval(interval)
  }
  interval = setInterval(() => {
    if (restTime.value <= 0) {
      clearInterval(interval)
      interval = undefined
      emits("finish")
    } else {
      restTime.value -= 1
    }
  }, 1000)
}
const startAt = (time: number) => {
  restTime.value = time > 0 ? time : 0
  startInterval()
}

defineExpose({ startAt })
</script>

<style lang="scss" scoped>
.v-counter {
  display: flex;
  align-items: baseline;
  font-size: 40rpx;
  color: white;
  .hour,
  .minute,
  .second {
    line-height: 60rpx;
    width: 60rpx;
    text-align: right;
    background-image: linear-gradient(to top, #0e5da4, #3c86c7);
    border-radius: 8rpx;
    border: 1px solid #00529b;
    box-sizing: border-box;
    text-align: center;
  }
  .sign {
    margin: 0 8rpx;
    transform: translateY(-6%);
  }
}
</style>
