<template>
  <view>
    <view class="page-header">
      <v-navbar back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <div class="row flex-center">
        <div class="label">
          来源：
        </div>
        <div class="value">
          {{ source }}
        </div>
      </div>
      <div class="row flex-center">
        <div class="label">
          编号：
        </div>
        <div class="value flex-center">
          {{ number }}
          <view class="flex-center" style="margin-left: 20px" @click="copyValue">
            <v-icon size="28rpx" src="/static/icons/order/copy.png" />
            <view class="text-24" style="color: #005bac">
              复制
            </view>
          </view>
        </div>
      </div>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
const { pageHeight } = usePageLayout()

const source = ref("")
const number = ref("")

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query) return
  switch (query.source) {
    case "1":
      source.value = "商品详情"
      break
    case "2":
      source.value = "订单详情"
      break
    case "3":
      source.value = "退款查询"
      break
    case "4":
      source.value = "选择售后"
      break
    case "5":
      source.value = "开票管理"
      break
    case "6":
      source.value = "售后详情"
      break
  }
  number.value = query.number
})

const copyValue = () => {
  uni.setClipboardData({
    data: number.value,
    success: () => {
      uni.showToast({ title: "复制成功", icon: "none" })
    },
    fail: (err) => {
      console.log({ err })
      uni.showToast({ title: "复制失败", icon: "none" })
    }
  })
}
</script>

<style lang="scss" scoped>
.row {
  margin: 20rpx;
}
</style>
