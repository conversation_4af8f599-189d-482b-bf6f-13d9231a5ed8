<template>
  <view>
    <view class="page-header">
      <v-navbar
        :title="invoicingMsg.makeInvoiceId ? `编辑开票信息` : `添加开票信息`"
        back
      />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <view class="v-block plain">
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            抬头类型
          </view>
          <view class="flex-center flex-1">
            <v-checkbox :checked="true" />&nbsp;&nbsp;企业
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold required label">
            发票抬头
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceHeader"
              class="flex-1"
              placeholder="请输入发票抬头(必填)"
              placeholder-class="placeholder"
            >
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold required label">
            税&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceDutyParagraph"
              class="flex-1"
              placeholder="请输入税号(必填)"
              placeholder-class="placeholder"
            >
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            公司地址
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceCompanyAddress"
              class="flex-1"
              placeholder="请输入公司地址(非必填)"
              placeholder-class="placeholder"
            >
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            公司电话
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceCompanyTel"
              class="flex-1"
              placeholder="请输入公司电话(非必填)"
              placeholder-class="placeholder"
            >
          </view>
        </view>
        <!-- <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            开户银行
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceBank"
              class="flex-1"
              placeholder="请输入开户银行(非必填)"
              placeholder-class="placeholder"
            >
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            银行账号
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceBankAccount"
              class="flex-1"
              placeholder="请输入银行账号(非必填)"
              placeholder-class="placeholder"
            >
          </view>
        </view> -->
      </view>
      <view class="v-block plain">
        <view class="v-form-item flex-center">
          <view class="text-bold required label">
            电子邮箱
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="invoicingMsg.invoiceEmail"
              class="flex-1"
              placeholder="请输入发票接收邮箱地址"
              placeholder-class="placeholder"
            >
          </view>
        </view>
      </view>
      <view class="submit-button-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        type="primary"
        height="94rpx"
        font-size="32rpx"
        @click="submit"
      >
        保存
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { safeNavigateBack, pageHeight } = usePageLayout()

const pageStore = $store.page()

const invoicingMsg = ref<InvoiceList>({
  makeInvoiceId: "",
  invoiceBank: "",
  // 开户银行
  invoiceBankAccount: "",
  // 银行账号
  invoiceCompanyAddress: "",
  // 公司地址
  invoiceCompanyTel: "",
  // 公司电话
  invoiceDutyParagraph: "",
  // 税号
  invoiceEmail: "",
  // 电子邮箱
  invoiceHeader: "",
  // 发票抬头
  invoiceHeaderType: 0
  // 抬头类型，0-企业
})
const validate = () => {
  switch (true) {
    case !invoicingMsg.value.invoiceHeader:
      uni.showToast({ title: "请输入发票抬头", icon: "none" })
      return false
    case !/^(?:(?![-·()（）_, \u3000]{2})[-\u4e00-\u9fa5a-zA-Z0-9·()（）_& /,\u3000]){1,70}$/.test(invoicingMsg.value.invoiceHeader):
      uni.showToast({
        title: "发票抬头格式不正确或长度超过70个字符",
        icon: "none"
      })
      return false
    case !invoicingMsg.value.invoiceDutyParagraph:
      uni.showToast({ title: "请输入税号", icon: "none" })
      return false
    case !/^[0-9a-zA-Z]{10,20}$/.test(invoicingMsg.value.invoiceDutyParagraph):
      uni.showToast({
        title: "税号仅限英文字母与数字，长度限制在10-20以内",
        icon: "none"
      })
      return false
    case invoicingMsg.value.invoiceCompanyAddress.length > 40:
      uni.showToast({
        title: "公司地址长度限制在40个字符以内",
        icon: "none"
      })
      return false
    case !/^[0-9`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'’，。、]{0,13}$/.test(
      invoicingMsg.value.invoiceCompanyTel
    ):
      uni.showToast({
        title: "公司电话仅限特殊字符与数字，长度限制在13位以内",
        icon: "none"
      })
      return false
    // case !/^[\u4E00-\u9FA5]{0,20}$/.test(invoicingMsg.value.invoiceBank):
    //   uni.showToast({
    //     title: "开户银行仅限中文，长度限制在1-20以内",
    //     icon: "none"
    //   })
    //   return false
    // case invoicingMsg.value.invoiceBankAccount.length > 40:
    //   uni.showToast({
    //     title: "银行账号长度限制在40个字符以内",
    //     icon: "none"
    //   })
    //   return false
    case !invoicingMsg.value.invoiceEmail:
      uni.showToast({
        title: "请输入电子邮箱",
        icon: "none"
      })
      return false
    case !/^[0-9A-Za-z`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'’，。、]{1,50}$/.test(
      invoicingMsg.value.invoiceEmail
    ):
      uni.showToast({
        title: "电子邮箱仅限数字、英文字母、特殊字符，长度限制在1-50以内",
        icon: "none"
      })
      return false
    default:
      return true
  }
}

const submit = async () => {
  if (!validate()) return
  uni.showLoading({ title: "提交中", mask: true })
  if (invoicingMsg.value.makeInvoiceId) {
    const response = await $api.invoicingUpdate({
      ...invoicingMsg.value
    })
    uni.hideLoading()
    switch (response.__error) {
      case undefined:
        await uni.showModal({ title: "提交成功", showCancel: false })
        safeNavigateBack()
    }
  } else {
    const response = await $api.invoicingInsert({ ...invoicingMsg.value })
    uni.hideLoading()
    switch (response.__error) {
      case undefined:
        await uni.showModal({ title: "提交成功", showCancel: false })
        safeNavigateBack()
    }
  }
}
onLoad(() => {
  const msg = pageStore.invoicingMsg
  if (!msg) {
    safeNavigateBack()
  } else {
    invoicingMsg.value = {
      invoiceHeader: msg.invoiceHeader ?? "",
      makeInvoiceId: msg.makeInvoiceId ?? "",
      invoiceDutyParagraph: msg.invoiceDutyParagraph ?? "",
      invoiceBank: msg.invoiceBank ?? "",
      invoiceBankAccount: msg.invoiceBankAccount ?? "",
      invoiceCompanyAddress: msg.invoiceCompanyAddress ?? "",
      invoiceCompanyTel: msg.invoiceCompanyTel ?? "",
      invoiceEmail: msg.invoiceEmail ?? "",
      invoiceHeaderType: msg.invoiceHeaderType ?? 0
    }
  }
})
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0 0 24rpx;
}
.v-block-product {
  margin: 0 24rpx 24rpx;
}
.v-block.plain {
  padding: 8rpx 24rpx;
}
.package-title {
  padding-top: 20rpx;
}
.package-info-title {
  padding: 20rpx 0;
}
.product-title {
  padding-top: 20rpx;
}
.product-number {
  padding: 20rpx 0;
}
.submit-button-blank {
  height: 94rpx;
}
.submit-button {
  position: fixed;
  padding: 0 24rpx;
  left: 0;
  right: 0;
  bottom: 0;
}
.tooltip-container {
  position: relative;
}
.tooltip {
  position: absolute;
  top: 0;
  right: 50%;
  height: fit-content;
  width: fit-content;
  transform: translateY(-100%);
  .text {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 20%;
    left: 0;
    color: white;
    font-size: 24rpx;
  }
}

.red {
  color: #e60012;
}
.aftersales-select-handle {
  position: fixed;
  left: 24rpx;
  right: 24rpx;
  bottom: 0;
  z-index: 10;
  .next-btn {
    display: flex;
    margin-top: 32rpx;
    justify-content: center;
    align-items: center;
    height: 94rpx;
    font-size: 32rpx;
    border-radius: 47rpx 47rpx;
    background-color: #005bac;
    color: #fff;
  }
}
.spec-style {
  color: #999999;
  font-size: 24rpx;
}
.count-bottom-style {
  color: #666666;
  font-size: 24rpx;
}
.handle-select {
  display: flex;
  justify-content: space-between;
  .selected-product-notice {
    font-size: 24rpx;
    color: #666;
  }
  .selected-all-style {
    font-size: 24rpx;
    color: #666;
  }
}
.handle-count {
  margin-top: 14rpx;
}
.price-notice {
  color: #999999;
  font-size: 22rpx;
}
.product-info {
  margin: 0 24rpx 24rpx;
}
.back-info-style {
  margin: 7rpx 0;
}
.delivery-hendle-margin {
  margin-bottom: 20rpx;
}
.order-no {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}
.order-headline-margin-adjust {
  margin-bottom: 12rpx;
}
</style>
