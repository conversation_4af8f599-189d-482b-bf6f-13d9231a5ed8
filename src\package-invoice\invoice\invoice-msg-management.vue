<template>
  <view>
    <view class="page-header background-white">
      <v-navbar title="开票信息" back />
    </view>
    <invoiceMsgList ref="invoiceMsgList" :page-height="pageHeight" />
    <view class="bottom-btn-group">
      <v-button
        :border="false"
        height="94rpx"
        background-color="#005BAC"
        @click="goToInvoicingFormPage"
      >
        <view style="font-size: 32rpx; font-weight: 800; color: #fff">
          添加
        </view>
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script lang="ts">
import wxs from "@/utils/wxs"
import invoiceMsgList from "./modules/invoice-msg-list.vue"

export default {
  components: {
    invoiceMsgList
  },
  data () {
    return {
      isShowBtnGroup: true,
      pageHeight: 0,
      tabIndex: 0,
      wxs: {
        transition: wxs.transition,
        transitionFinish: wxs.transitionFinish
      }
    }
  },
  onReady () {
    this.refreshPageHeight()
  },
  onShow () {
    const listRef = this.$refs.invoiceMsgList as InstanceType<
      typeof invoiceMsgList
    >
    listRef.initInvoiceList()
  },
  methods: {
    goToInvoicingFormPage () {
      const pageStore = $store.page()
      pageStore.setInvoicingMsg({
        makeInvoiceId: "",
        invoiceBank: "",
        // 开户银行
        invoiceBankAccount: "",
        // 银行账号
        invoiceCompanyAddress: "",
        // 公司地址
        invoiceCompanyTel: "",
        // 公司电话
        invoiceDutyParagraph: "",
        // 税号
        invoiceEmail: "",
        // 电子邮箱
        invoiceHeader: "",
        // 发票抬头
        invoiceHeaderType: 0
        // 抬头类型，0-企业
      })
      uni.navigateTo({ url: "/package-invoice/invoice/invoicing-form" })
    },
    async refreshPageHeight () {
      const system = uni.getWindowInfo()
      let screenHeight
      // #ifdef MP-WEIXIN
      screenHeight = system.screenHeight
      // #endif
      // #ifdef MP-ALIPAY
      screenHeight = system.windowHeight
      // #endif
      // #ifdef H5
      screenHeight = system.screenHeight
      // #endif
      const query = uni.createSelectorQuery()
      const headerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-header")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      this.pageHeight = screenHeight - headerHeight
    }
  }
}
</script>

<script
  src="../../components/v-tabs/v-tabs.wxs"
  module="wxs"
  lang="wxs"
></script>

<style lang="scss" scoped>
.v-tab-block {
  height: 100%;
  width: 100%;
  background-color: #005bac;
}

.bottom-btn-group {
  padding-top: 28rpx;
  position: fixed;
  left: 34rpx;
  right: 34rpx;
  bottom: 0;
  z-index: 10;
}
.delete-button {
  background-color: #f06671;
  border-radius: 0 18rpx 18rpx 0;
  height: 100%;
  width: 124rpx;
  margin-left: -20rpx;
  padding-left: 20rpx;
}
</style>
