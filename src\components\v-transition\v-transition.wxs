function fadeIn (instance, duration, timing) {
  var container = instance.selectComponent(".v-transition")
  var content = instance.selectComponent(".v-transition-content")
  content.setStyle({ display: "block" })
  container.setStyle({ opacity: "0" })
  instance.requestAnimationFrame(function () {
    container.setStyle({
      transition: "opacity " + duration + "ms " + timing,
      opacity: "1"
    })
    var state = instance.getState()
    state.timer = instance.setTimeout(function () {
      state.timer = undefined
      container.setStyle({})
      if (!state.show) fadeOut(instance, duration)
    }, duration)
  })
}

function fadeOut (instance, duration, timing) {
  var container = instance.selectComponent(".v-transition")
  var content = instance.selectComponent(".v-transition-content")
  content.setStyle({ display: "block" })
  container.setStyle({ opacity: "1" })
  instance.requestAnimationFrame(function () {
    container.setStyle({
      transition: "opacity " + duration + "ms " + timing,
      opacity: "0"
    })
    var state = instance.getState()
    state.timer = instance.setTimeout(function () {
      state.timer = undefined
      content.setStyle({ display: "none" })
      container.setStyle({})
      if (state.show) fadeIn(instance, duration)
    }, duration)
  })
}

function expand (instance, duration, timing) {
  var container = instance.selectComponent(".v-transition")
  var content = instance.selectComponent(".v-transition-content")
  content.setStyle({ display: "block" })
  container.setStyle({ 'max-height': "0" })
  instance.requestAnimationFrame(function () {
    var height = content.getBoundingClientRect().height
    container.setStyle({
      transition: "max-height " + duration + "ms " + timing,
      'max-height': height + "px"
    })
    var state = instance.getState()
    state.timer = instance.setTimeout(function () {
      state.timer = undefined
      container.setStyle({})
      if (!state.show) collapse(instance, duration)
    }, duration)
  })
}

function collapse (instance, duration, timing) {
  var container = instance.selectComponent(".v-transition")
  var content = instance.selectComponent(".v-transition-content")
  content.setStyle({ display: "block" })
  var height = content.getBoundingClientRect().height
  container.setStyle({ 'max-height': height + "px" })
  instance.requestAnimationFrame(function () {
    container.setStyle({
      transition: "max-height " + duration + "ms " + timing,
      'max-height': "0"
    })
    var state = instance.getState()
    state.timer = instance.setTimeout(function () {
      state.timer = undefined
      content.setStyle({ display: "none" })
      container.setStyle({})
      if (state.show) expand(instance, duration)
    }, duration)
  })
}

function fadeExpand (instance, duration, timing) {
  var container = instance.selectComponent(".v-transition")
  var content = instance.selectComponent(".v-transition-content")
  content.setStyle({ display: "block" })
  container.setStyle({ 'max-height': "0", opacity: "0" })
  instance.requestAnimationFrame(function () {
    var height = content.getBoundingClientRect().height
    var maxHeight = "max-height " + duration * 0.4 + "ms " + timing
    var opacity = "opacity " + duration * 0.8 + "ms " + duration * 0.2 + "ms " + timing
    container.setStyle({
      transition: maxHeight + ", " + opacity,
      'max-height': height + "px",
      opacity: "1"
    })
    var state = instance.getState()
    state.timer = instance.setTimeout(function () {
      state.timer = undefined
      container.setStyle({})
      if (!state.show) fadeCollapse(instance, duration)
    }, duration)
  })
}

function fadeCollapse (instance, duration, timing) {
  var container = instance.selectComponent(".v-transition")
  var content = instance.selectComponent(".v-transition-content")
  content.setStyle({ display: "block" })
  var height = content.getBoundingClientRect().height
  container.setStyle({ 'max-height': height + "px", opacity: "1" })
  instance.requestAnimationFrame(function () {
    var opacity = "opacity " + duration * 0.8 + "ms " + timing
    var maxHeight = "max-height " + duration * 0.4 + "ms " + duration * 0.6 + "ms " + timing
    container.setStyle({
      transition: opacity + ", " + maxHeight,
      'max-height': "0", 
      opacity: "0"
    })
    var state = instance.getState()
    state.timer = instance.setTimeout(function () {
      state.timer = undefined
      content.setStyle({ display: "none" })
      container.setStyle({})
      if (state.show) fadeExpand(instance, duration)
    }, duration)
  })
}

function changeData (val,  _, ownerInstance, eventInstance) {
  if (!val) return
  var instance = ownerInstance || eventInstance
  var state = instance.getState()
  if (state.show === undefined) {
    state.type = val.type
    state.duration = val.duration - 25
    state.timing = val.timing
    state.show = val.show
    var content = instance.selectComponent(".v-transition-content")
    if (val.show) {
      content.setStyle({ display: "block" })
    } else {
      content.setStyle({ display: "none" })
    }
  } else {
    state.show = val.show
    if (state.timer) return
    if (state.type === "fade" && val.show) {
      return fadeIn(instance, val.duration, val.timing)
    } else if (state.type === "fade" && !val.show) {
      return fadeOut(instance, val.duration, val.timing)
    } else if (state.type === "collapse" && val.show) {
      return expand(instance, val.duration, val.timing)
    } else if (state.type === "collapse" && !val.show) {
      return collapse(instance, val.duration, val.timing)
    }else if (state.type === "fadeCollapse" && val.show) {
      return fadeExpand(instance, val.duration, val.timing)
    } else if (state.type === "fadeCollapse" && !val.show) {
      return fadeCollapse(instance, val.duration, val.timing)
    }
  }
}

module.exports = {
  changeData: changeData
}
