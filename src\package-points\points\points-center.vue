<template>
  <view>
    <view class="page-header hide-shadow flex--center flex-col">
      <view class="background">
        <image class="background-image" src="@/package-points/static/image/points-center.png" />
      </view>
      <v-navbar
        type="white"
        title="积分兑换"
        back
        background-color="transparent"
      />
      <view class="points-container">
        <view class="points-rule flex-center" @click="navigateToService">
          <view class="text">
            积分规则
          </view>
          <v-icon size="17rpx" src="/static/icons/points/right-arrow.png" />
        </view>
        <view class="points-value">
          {{
            availablePoints !== undefined && outstandingPoints !== undefined
              ? availablePoints + outstandingPoints
              : "-"
          }}
        </view>
        <view class="points-content">
          {{
            `可使用积分:${
              typeof availablePoints === "number" ? availablePoints : "-"
            } 未到账积分:${
              typeof outstandingPoints === "number" ? outstandingPoints : "-"
            }`
          }}
        </view>
      </view>
      <view class="filter-list flex-center">
        <view
          class="filter-item"
          :class="{ active: rewardsPointsType === undefined }"
          @click="() => changeFilter()"
        >
          全部
        </view>
        <view
          class="filter-item"
          :class="{ active: rewardsPointsType === 0 }"
          @click="() => changeFilter(0)"
        >
          优惠券
        </view>
      </view>
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
      @scrolltolower="getRewardsPointsList"
    >
      <view class="page-padding-top" />
      <v-loading-block v-if="loading" />
      <v-empty
        v-else-if="status === 'nomore' && exchangeList.length === 0"
        src="/static/image/empty-coupon.png"
      >
        <view> 暂无可兑换内容 </view>
      </v-empty>
      <template v-else>
        <view class="exchange-list flex--between flex-warp">
          <view
            v-for="(item, itemIndex) in exchangeList"
            :key="itemIndex"
            class="exchange-item"
            @click="() => navigateToDetails(item)"
          >
            <v-image
              height="337rpx"
              width="337rpx"
              :src="item.couponMainIMG"
              mode="aspectFit"
            />
            <view class="item-info flex--evenly flex-col">
              <view class="item-name text-1-row">
                {{ item.couponName }}
              </view>
              <view class="item-price">
                {{ `${item.integrate}积分` }}
              </view>
            </view>
          </view>
        </view>
        <v-loadmore
          v-if="!refreshing"
          :status="status"
          @click="getRewardsPointsList"
        />
      </template>
      <view class="page-padding-bottom" />
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { str2num } from "@/utils/type-transfer"

const { pageHeight } = usePageLayout()

const availablePoints = ref<number>()
const outstandingPoints = ref<number>()
const getUserInfo = async () => {
  const response = await $api.findMemberInfoWeChat({})
  switch (response.__error) {
    case undefined:
      availablePoints.value = parseInt(response.data.availablePoints)
      outstandingPoints.value = parseInt(response.data.outstandingPoints)
  }
}
onShow(() => {
  getUserInfo()
})

const exchangeList = ref<ExchangeItem[]>([])

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  initExchangeList()
}
const initExchangeList = async () => {
  loading.value = true
  pageIndex.value = 1
  exchangeList.value = []
  status.value = "loadmore"
  await getRewardsPointsList()
}
onLoad(() => {
  initExchangeList()
})
/** 分页数据 */
const pageIndex = ref(1)
const rewardsPointsType = ref<number>()
const changeFilter = async (value?: number) => {
  if (value === rewardsPointsType.value) return
  rewardsPointsType.value = value
  await initExchangeList()
}
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")

const getRewardsPointsList = async () => {
  const response = await $api.getRewardsPointsList({
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    orderField: "createdTime",
    sortType: "desc",
    requestParams: {
      rewardsPointsType: rewardsPointsType.value
    }
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      exchangeList.value = [...exchangeList.value, ...response.data.result]
      const totalCount = str2num(response.data.totalCount)
      status.value =
        exchangeList.value.length < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
  loading.value = false
}

const navigateToService = async () => {
  uni.navigateTo({
    url: "/package-user/user/customer-service-detail?type=8"
  })
}
const navigateToDetails = (item: ExchangeItem) => {
  uni.navigateTo({
    url: `/package-points/points/points-center-exchange?coupon=${item.couponNo}&reward=${item.rewardsPointsNo}`
  })
}
</script>

<style lang="scss" scoped>
.page-header {
  position: relative;
  height: 531rpx;
  .background {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 531rpx;
    z-index: -2;
    .background-image {
      height: 531rpx;
      width: 750rpx;
    }
  }
  .points-container {
    flex: 1;
    padding: 30rpx 47rpx 0;
    color: white;
    .points-rule {
      margin-bottom: 10rpx;
      .text {
        font-size: 24rpx;
        margin-right: 10rpx;
      }
    }
    .points-value {
      margin-bottom: 10rpx;
      font-size: 72rpx;
      text-shadow: 0 6rpx #d95b00;
      font-weight: bold;
    }
    .points-content {
      font-size: 24rpx;
    }
  }
  .filter-list {
    height: 110rpx;
    padding: 0 24rpx;
    .filter-item {
      margin-right: 24rpx;
      padding: 0 26rpx;
      line-height: 54rpx;
      border-radius: 26rpx;
      background-color: rgba(153, 153, 153, 0.1);
      font-size: 24rpx;
      color: #999;
      transition: background-color 0.15s, color 0.15s;
      &.active {
        background-color: #ff6a00;
        color: white;
      }
    }
  }
}
.exchange-list {
  padding: 0 24rpx;
  .exchange-item {
    background-color: white;
    border-radius: 10rpx;
    overflow: hidden;
    height: fit-content;
    &:nth-child(n + 3) {
      margin-top: 28rpx;
    }
    .item-info {
      height: 120rpx;
      padding: 0 20rpx;
      width: 337rpx;
      box-sizing: border-box;
      overflow: hidden;
      .item-price {
        color: #ff6a00;
      }
    }
  }
}
</style>
