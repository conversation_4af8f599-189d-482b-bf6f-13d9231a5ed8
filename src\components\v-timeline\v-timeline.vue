<template>
  <view class="v-timeline">
    <view
      v-for="(item, index) in timeLine"
      :key="index"
      class="v-timeline-item"
    >
      <view class="header flex-center-between">
        <view class="title text-bold">
          {{ item.title }}
        </view>
        <view class="text-light text-24">
          {{ formateUtc(item.time) }}
        </view>
      </view>
      <view class="content text-light text-24">
        {{ item.content }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { toRef } from "vue"
import type { TimeLine } from "./types"
import { formateUtc } from "@/utils/time"

const props = defineProps<{
  data: TimeLine,
}>()

const timeLine = toRef(props, "data")
</script>

<style lang="scss" scoped>
.v-timeline-item {
  position: relative;
  margin-left: 40rpx;
  padding-bottom: 40rpx;
  .header {
    line-height: 40rpx;
    .title {
      color: $text-color-light;
    }
  }
  .content {
    margin-top: 4rpx;
  }
  &::before {
    content: " ";
    position: absolute;
    height: 100%;
    width: 1px;
    background-color: #bcbcbc;
    left: -27rpx;
    transform: translate(-50%, 20rpx);
  }
  &::after {
    content: " ";
    position: absolute;
    top: 0;
    height: 10rpx;
    width: 10rpx;
    border-radius: 50%;
    background-color: #bcbcbc;
    left: -27rpx;
    transform: translate(-50%, calc(20rpx - 50%));
  }
  &:last-child {
    padding-bottom: 20rpx;
    &::before {
      display: none;
      content: "";
    }
  }
  &:last-child {
    .header {
      .title {
        color: #005bac;
      }
    }
    &::after {
      height: 14rpx;
      width: 14rpx;
      border: 2px solid #005bac;
      background-color: white;
    }
  }
}
</style>
