<template>
  <scroll-view
    v-if="pageHeight"
    scroll-y
    :style="{ height: `${pageHeight}px` }"
    enable-back-to-top
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="refresh"
    @scrolltolower="getPendingList"
  >
    <view class="page-padding-top" />
    <template v-if="!refreshing">
      <v-loading-block v-if="loading" />
      <v-empty
        v-else-if="status === 'nomore' && invoiceList.length === 0"
        src="/static/image/empty-order.png"
      >
        暂无发票单
      </v-empty>
      <template v-else>
        <view
          v-for="item in invoiceList"
          :key="item.makeInvoiceId"
          class="invoice-item"
          @click.stop="selectedInvoiceHeader(item)"
        >
          <v-swipe>
            <template #default>
              <view class="v-block flex-center-between">
                <view>
                  <view class="headline">
                    {{ item.invoiceHeader }}
                  </view>
                  <view class="content">
                    公司税号：{{ item.invoiceDutyParagraph }}
                  </view>
                </view>
                <view class="edit-btn" @click.stop="goToInvoiceFormPage(item)">
                  <view class="flex-center-center">
                    <v-icon
                      size="36rpx"
                      src="/static/icons/invoice/edit.png"
                    />编辑
                  </view>
                </view>
              </view>
            </template>
            <template #button>
              <view
                class="delete-button flex-center-center"
                @click.stop="deleteHandle(item.makeInvoiceId)"
              >
                <v-icon
                  size="36rpx"
                  src="/static/icons/components/delete.png"
                />
              </view>
            </template>
          </v-swipe>
        </view>
        <v-loadmore v-if="!refreshing" :status="status" />
      </template>
    </template>
    <view class="page-padding-bottom" />
  </scroll-view>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    pageHeight: number
  }>(),
  {
    pageHeight: undefined
  }
)
const selectMod = ref(false)
const pageHeight = toRef(props, "pageHeight")
// const orderList = ref<OrderItem[]>([])
const invoiceList = ref<InvoiceList[]>([])

const { safeNavigateBack } = usePageLayout()

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = () => {
  refreshing.value = true
  pageIndex.value = 1
  invoiceList.value = []
  status.value = "loadmore"
  getPendingList()
}
const initInvoiceList = async () => {
  loading.value = true
  pageIndex.value = 1
  invoiceList.value = []
  status.value = "loadmore"
  await getPendingList()
  loading.value = false
}
const pageStore = $store.page()
const selectedInvoiceHeader = (data: InvoiceList) => {
  if (selectMod.value) {
    pageStore.setInvoiceHeader({ ...data })
    safeNavigateBack()
  }
}
const goToInvoiceFormPage = (data: InvoiceList) => {
  pageStore.setInvoicingMsg({ ...data })
  uni.navigateTo({ url: "/package-invoice/invoice/invoicing-form" })
}
const deleteHandle = (id: string) => {
  uni.showModal({
    content: "是否删除开票信息？",
    confirmText: "确认", // 这块是确定按钮的文字
    cancelText: "再想想", // 这块是取消的文字
    success: async (res) => {
      if (res.confirm) {
        uni.showLoading({ title: "处理中", mask: true })
        const response = await $api.invoicingDelete({
          makeInvoiceId: id
        })
        uni.hideLoading()
        switch (response.__error) {
          case undefined:
            $toast.show("删除成功")
            refresh()
        }
      }
    }
  })
}
onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (query?.select === "1") {
    selectMod.value = true
  }
})
/** 分页数据 */
const pageIndex = ref(1)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")
const getPendingList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  const response = await $api.invoicingList({})
  switch (response.__error) {
    case undefined: {
      const list: InvoiceList[] =
        response.data.makeInvoiceResponseList.map((item) => ({
          invoiceDutyParagraph: item.invoiceDutyParagraph,
          makeInvoiceId: item.id,
          invoiceHeader: item.invoiceHeader,
          invoiceBank: item.invoiceBank,
          invoiceBankAccount: item.invoiceBankAccount,
          invoiceCompanyAddress: item.invoiceCompanyAddress,
          invoiceCompanyTel: item.invoiceCompanyTel,
          invoiceEmail: item.invoiceEmail,
          invoiceHeaderType: item.invoiceHeaderType
        })) ?? []
      invoiceList.value = [...invoiceList.value, ...list]
      // const totalCount = str2num(response.data.totalCount)
      // status.value = orderList.value.length < totalCount ? "loadmore" : "nomore"
      status.value = "nomore"
      break
    }
  }
  await nextTick()
  refreshing.value = false
}

defineExpose({ initInvoiceList })
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0;
  height: 149rpx;
}
.delete-button {
  background-color: #f06671;
  border-radius: 18rpx;
  height: 100%;
  width: 124rpx;
  margin-left: -20rpx;
  padding-left: 20rpx;
}
.invoice-item {
  margin: 0 24rpx 24rpx;
  position: relative;
}
.headline {
  color: #231815;
  font-size: 28rpx;
  font-weight: 800;
  margin-bottom: 19rpx;
}
.content {
  color: #666;
  font-size: 24rpx;
}
.edit-btn {
  display: var(--v-swipe-hide-display);
}
</style>
