function listChange (val, _, ownerInstance, eventInstance) {
  if (!val) return
  var instance = ownerInstance || eventInstance
  var container = instance.selectAllComponents(".v-category-item-container")[0]
  var ellipsis = instance.selectAllComponents(".v-category-ellipsis")[0]
  var itemList = instance.selectAllComponents(".v-category-item")
  
  var containerWidth = container.getBoundingClientRect().width

  var totalWidth = 0
  for (var i = 0; i < itemList.length; i += 1) {
    totalWidth += itemList[i].width
  }
  if (containerWidth >= totalWidth) {
    ellipsis.setStyle({ display: "none" })
  } else {
    var ellipsisWidth = ellipsis.getBoundingClientRect().width
    containerWidth -= ellipsisWidth
    var flag = false
    for (var i = 0; i < itemList.length; i += 1) {
      var width = itemList[i].getBoundingClientRect().width
      if (containerWidth - width < 0) {
        itemList[i].setStyle({ display: "none" })
        flag = true
      } else {
        containerWidth -= width
      }
    }
    if (flag) {
      ellipsis.setStyle({ opacity: "1" })
    } else {
      ellipsis.setStyle({ display: "none" })
    }
  }
}

module.exports = {
  listChange: listChange
}