<template>
  <view
    class="v-tag"
    :style="{
      height: height,
      lineHeight: height,
      width: width,
      padding: padding,
      margin: margin,
      backgroundColor: backgroundColor,
      color: color,
      fontSize: fontSize
    }"
  >
    <slot />
  </view>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  height?: string,
  width?: string,
  padding?: string,
  margin?: string,
  backgroundColor: string,
  color?: string,
  fontSize?: string
}>(), {
  height: "34rpx",
  width: undefined,
  padding: "0 10rpx",
  margin: "0",
  backgroundColor: undefined,
  color: "white",
  fontSize: "20rpx"
})
</script>

<style lang="scss" scoped>
.v-tag {
  border-radius: 4rpx;
  text-align: center;
}
</style>
