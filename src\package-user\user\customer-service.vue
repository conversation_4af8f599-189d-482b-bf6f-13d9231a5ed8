<template>
  <view>
    <view class="page-header">
      <v-navbar title="客户服务" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="title text-bold">
        帮助中心
      </view>
      <view class="container flex-center">
        <view class="icon flex-center-center flex-col">
          <v-icon size="52rpx" src="/static/icons/user/transportation.png" />
          <view class="text-sub text-24">
            运输类
          </view>
        </view>
        <view class="flex-1">
          <view
            class="action flex-center-between"
            @click="getSysContentManage(0)"
          >
            <view>第三方配送服务说明</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
        </view>
      </view>
      <view class="container flex-center">
        <view class="icon flex-center-center flex-col">
          <v-icon size="52rpx" src="/static/icons/user/trade.png" />
          <view class="text-sub text-24">
            交易类
          </view>
        </view>
        <view class="flex-1">
          <view
            class="action flex-center-between"
            @click="getSysContentManage(1)"
          >
            <view>价格说明</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
          <view class="divider" />
          <view
            class="action flex-center-between"
            @click="getSysContentManage(2)"
          >
            <view>订购说明</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
        </view>
      </view>
      <view class="container flex-center">
        <view class="icon flex-center-center flex-col">
          <v-icon size="52rpx" src="/static/icons/user/after-sale.png" />
          <view class="text-sub text-24">
            售后类
          </view>
        </view>
        <view class="flex-1">
          <view
            class="action flex-center-between"
            @click="getSysContentManage(3)"
          >
            <view>售后政策说明</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
          <view class="divider" />
          <view
            class="action flex-center-between"
            @click="getSysContentManage(4)"
          >
            <view>售后退款说明</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
        </view>
      </view>
      <view class="container flex-center">
        <view class="icon flex-center-center flex-col">
          <v-icon size="52rpx" src="/static/icons/user/account.png" />
          <view class="text-sub text-24">
            账号协议类
          </view>
        </view>
        <view class="flex-1">
          <view
            class="action flex-center-between"
            @click="getSysContentManage(5)"
          >
            <view>隐私协议</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
          <view class="divider" />
          <view
            class="action flex-center-between"
            @click="getSysContentManage(6)"
          >
            <view>用户服务协议</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
        </view>
      </view>
      <view class="container flex-center">
        <view class="icon flex-center-center flex-col">
          <v-icon size="52rpx" src="/static/icons/user/finance.png" />
          <view class="text-sub text-24">
            财务类
          </view>
        </view>
        <view class="flex-1">
          <view
            class="action flex-center-between"
            @click="getSysContentManage(7)"
          >
            <view>发票说明</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
        </view>
      </view>
      <view class="container flex-center">
        <view class="icon flex-center-center flex-col">
          <v-icon size="52rpx" src="/static/icons/user/points-rule.png" />
          <view class="text-sub text-24">
            活动类
          </view>
        </view>
        <view class="flex-1">
          <view
            class="action flex-center-between"
            @click="getSysContentManage(8)"
          >
            <view>积分规则</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
          <view class="divider" />
          <view
            class="action flex-center-between"
            @click="getSysContentManage(9)"
          >
            <view>活动规则</view>
            <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="page-footer">
      <view class="button-list flex-center-center">
        <!-- <view
          class="flex-col flex-center-center flex-1 button-item"
          @click="customerService"
        >
          <v-icon
            size="52rpx"
            src="/static/icons/user/online-customer-service.png"
          />
          <view class="text-sub">
            在线客服
          </view>
        </view> -->
        <button
          class="flex-col flex-center-center flex-1 button-item plain-button"
          open-type="contact"
        >
          <v-icon
            size="52rpx"
            src="/static/icons/user/online-customer-service.png"
          />
          <view class="text-sub">
            在线客服
          </view>
        </button>
        <view class="divider-vertical" style="height: 60%" />
        <view
          class="flex-col flex-center-center flex-1 button-item"
          @click="goToFeedbackPage"
        >
          <v-icon size="52rpx" src="/static/icons/user/feedback.png" />
          <view class="text-sub">
            建议反馈
          </view>
        </view>
      </view>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { checkToken } from "@/utils/auth/token"

const { pageHeight } = usePageLayout()
const navigateToSignUp = () => {
  uni.navigateTo({ url: "/pages/user/sign-up" })
}
const goToFeedbackPage = () => {
  if (!checkToken()) return navigateToSignUp()
  uni.navigateTo({ url: "/package-user/user/user-feedback" })
}
const getSysContentManage = async (type: number) => {
  uni.navigateTo({
    url: `/package-user/user/customer-service-detail?type=${type}`
  })
}

// const customerService = () => {
//   uni.openCustomerServiceChat({
//     extInfo: { url: $api.config.customerUrl },
//     corpId: $api.config.corpId,
//     showMessageCard: false,
//     fail: (err) => console.log(err)
//   })
// }
</script>

<style lang="scss" scoped>
.title {
  padding: 26rpx;
}
.container {
  background-color: white;
  margin-bottom: 20rpx;
  min-height: 110rpx;
  .icon {
    width: 200rpx;
  }
  .action {
    height: 90rpx;
    padding-right: 28rpx;
  }
}
.button-list-blank {
  height: 160rpx;
}
.page-footer {
  background-color: #fff;
}
.button-list {
  height: 170rpx;
}
</style>
