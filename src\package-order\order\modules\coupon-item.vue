<template>
  <view class="v-block coupon-item">
    <view class="coupon-content flex-center" @click="$emit('change-coupon')">
      <view class="coupon-left-part flex-center-center flex-col">
        <view class="coupon-amount text-1-row">
          {{ parseInt(coupon.couponValue) }}
        </view>
        <view class="coupon-limit text-1-row">
          满{{ parseInt(coupon.couponPriceLimit) }}可用
        </view>
      </view>
      <view class="divider-vertical" />
      <view class="coupon-right-part flex--center flex-col">
        <view class="coupon-name text-1-row">
          {{ coupon.couponName }}
        </view>
        <view class="coupon-time text-2-row">
          {{ time }}
        </view>
      </view>
      <v-checkbox :checked="checked" />
    </view>
    <view class="divider-dashed" />
    <view
      class="coupon-rule"
      :class="{ 'show-rule': isShow }"
      @click="$emit('change-rules')"
    >
      <view class="coupon-rule-header flex-center">
        <view class="coupon-rule-tag">
          使用规则
        </view>
        <view class="coupon-text text-1-row">
          {{ coupon.couponRemark }}
        </view>
        <view class="coupon-rule-arrow">
          <v-icon size="19rpx" src="/static/icons/order/show-icon.png" />
        </view>
      </view>
      <v-transition type="collapse" :show="isShow" timing="ease-out">
        <view class="coupon-rule-content">
          {{ coupon.couponRemark }}
        </view>
      </v-transition>
    </view>
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
const props = defineProps<{
  coupon: CouponItem
  isShow: boolean
  checked: boolean
}>()
interface Emits {
  (event: "change-rules"): void
  (event: "change-coupon"): void
}
defineEmits<Emits>()

const coupon = toRef(props, "coupon")

const time = computed(() => {
  const couponExp = coupon.value.couponExp
  const nowTime = new Date().getTime()
  if (coupon.value.couponExpType === 0 && couponExp.ranges) {
    // 排序时间列表
    const list = couponExp.ranges.sort((a, b) => {
      return a.startTime - b.startTime
    })
    let startTime = 0
    let endTime = 0
    for (let i = 0; i < list.length; i++) {
      if (nowTime < list[0].startTime) {
        // 当前时间还没到使用范围
        startTime = list[i].startTime
        endTime = list[i].endTime
        const startString = formateUtc(new Date(startTime), "YYYY.MM.dd hh:mm")
        return `${startString} 可用`
      } else if (nowTime <= list[i].endTime && nowTime >= list[i].startTime) {
        // 时间在可使用范围段中
        startTime = list[i].startTime
        endTime = list[i].endTime
        break
      }
      startTime = list[i].startTime
      endTime = list[i].endTime
    }
    const startString = formateUtc(new Date(startTime), "YYYY.MM.dd hh:mm")
    const endString = formateUtc(new Date(endTime), "YYYY.MM.dd hh:mm")
    return `${startString} ~ ${endString}`
  } else if (coupon.value.couponExpType === 1) {
    const date = new Date(coupon.value.overdueTime)
    const dateString = formateUtc(date, "YYYY.MM.dd hh:mm")
    return `有效期至 ${dateString}`
  }
  return ""
})
</script>
<style lang="scss" scoped>
.coupon-item {
  position: relative;
  margin: 0 32rpx 32rpx 32rpx;
  .coupon-content {
    height: 130rpx;
    .coupon-left-part {
      width: 210rpx;
      .coupon-amount {
        font-size: 56rpx;
        color: #e60012;
        font-weight: bold;
        margin-bottom: -8rpx;
      }
      .coupon-limit {
        font-size: 24rpx;
        color: #666666;
      }
    }
    .divider-vertical {
      height: 100rpx;
      margin-right: 24rpx;
    }
    .coupon-right-part {
      flex: 1;
      overflow: hidden;
      .coupon-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 6rpx;
      }
      .coupon-time {
        font-size: 22rpx;
        color: #999999;
        white-space: nowrap;
      }
    }
  }
  .divider-dashed {
    height: 1px;
    box-sizing: border-box;
    border-top: 1px dashed #e6e6e6;
    margin: 16rpx 0;
  }
  .coupon-rule {
    .coupon-rule-header {
      .coupon-rule-tag {
        width: 100rpx;
        text-align: center;
        font-size: 20rpx;
        line-height: 40rpx;
        color: #f06671;
        border-radius: 4rpx;
        border: 1px solid #f06671;
        box-sizing: border-box;
      }
      .coupon-text {
        flex: 1;
        margin: 0 16rpx;
        font-size: 24rpx;
        color: #666666;
        opacity: 1;
        transition: opacity 0.3s linear;
      }
      .coupon-rule-arrow {
        transform-origin: center;
        transform: rotate(0);
        transition: transform 0.3s ease;
      }
    }
    .coupon-rule-content {
      padding: 10rpx;
      font-size: 24rpx;
      color: #666666;
      white-space: pre-line;
    }
    &.show-rule {
      .coupon-text {
        opacity: 0;
      }
      .coupon-rule-arrow {
        transform: rotate(-180deg);
      }
    }
  }
}
</style>
