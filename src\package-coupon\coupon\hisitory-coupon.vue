<template>
  <view>
    <view class="page-header background-white">
      <v-navbar title="历史优惠券" back />
      <v-tabs
        v-model="tabIndex"
        :list="['已使用', '已失效']"
        height="86rpx"
        active-text-style="color:#005BAC;font-weight: bold"
      >
        <view class="v-tab-block" style="transform: translateX(0)" />
      </v-tabs>
    </view>
    <swiper
      :current="tabIndex"
      :style="{ height: `${pageHeight}px` }"
      @change="tabSwiperChange"
      @transition="wxs.transition"
      @animationfinish="wxs.transitionFinish"
    >
      <swiper-item>
        <coupon-list
          ref="couponList0"
          :page-height="pageHeight"
          :type="2"
          @refresh="refreshAll"
        />
      </swiper-item>
      <swiper-item>
        <coupon-list
          ref="couponList1"
          :page-height="pageHeight"
          :type="3"
          @refresh="refreshAll"
        />
      </swiper-item>
    </swiper>
  </view>
</template>

<script lang="ts">
import CouponList from "./modules/coupon-list.vue"
import wxs from "@/utils/wxs"

export default {
  components: {
    CouponList
  },
  data () {
    return {
      lockOnShow: false,
      pageHeight: 0,
      tabIndex: 0,
      initList: [false, false],
      wxs: {
        transition: wxs.transition,
        transitionFinish: wxs.transitionFinish
      }
    }
  },
  onLoad (query: Record<string, string>) {
    const index = parseInt(query.index)
    if (!isNaN(index)) this.tabIndex = index
  },
  onShow () {
    if (!this.lockOnShow) {
      this.refreshAll()
    }
  },
  onReady () {
    this.refreshPageHeight()
  },
  methods: {
    refreshAll () {
      for (let i = 0; i < this.initList.length; i += 1) {
        this.initList[i] = false
      }
      this.initCouponList(this.tabIndex)
    },
    tabSwiperChange (event: SwiperChangeEvent) {
      this.changeTabIndex(event.detail.current)
    },
    changeTabIndex (index: number) {
      this.tabIndex = index
      this.initCouponList(index)
    },
    initCouponList (index: number) {
      const listRef = this.$refs[`couponList${index}`] as InstanceType<
        typeof CouponList
      >
      if (!listRef) return
      if (this.initList[index]) return
      this.initList[index] = true
      listRef.initCouponList()
    },
    async refreshPageHeight () {
      const system = uni.getWindowInfo()
      let screenHeight
      // #ifdef MP-WEIXIN
      screenHeight = system.screenHeight
      // #endif
      // #ifdef MP-ALIPAY
      screenHeight = system.windowHeight
      // #endif
      // #ifdef H5
      screenHeight = system.screenHeight
      // #endif
      const query = uni.createSelectorQuery()
      const headerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-header")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      this.pageHeight = screenHeight - headerHeight
    }
  }
}
</script>

<script
  src="../../components/v-tabs/v-tabs.wxs"
  module="wxs"
  lang="wxs"
></script>

<style lang="scss" scoped>
.v-tab-block {
  height: 100%;
  width: 100%;
  background-color: #005bac;
}
</style>
