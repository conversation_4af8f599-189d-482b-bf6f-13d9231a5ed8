/// <reference types="./response" />

/** 首页轮播图 */
declare interface IndexSwiperItem {
  id: string
  img: string
}

/** 区 */
declare interface District {
  label: string
  value: number
}
/** 市 */
declare interface City {
  label: string
  value: number
  children?: District[]
}
/** 省 */
declare interface Province {
  label: string
  value: number
  children?: City[]
}

/** 分类 */
declare interface Category {
  /** 分类id */
  id: number
  /** 分类名称 */
  name: string
  /** 分类图片 */
  img?: string
  /** 子分类 */
  children?: Category[]
}

/** 商品格组件  所属4级分类 */
declare interface ProductBlock {
  /** 编号 */
  id: string
  /** 显示图片 */
  image: string
  /** 名称 */
  name: string
  /** 规格列表 */
  categoryList: string[]
  /** 销售单价 */
  unitPrice: number
  /** 显示单价 */
  displayPrice: number
  /** 标题 */
  title?: string
}

/** 商品项组件 所属5级分类 */
declare interface ProductItem {
  /** 编号 */
  id: string
  /** 显示图片 */
  image: string
  /** 名称 */
  name: string
  /** 销售单价 */
  unitPrice: number
  /** 销售规格 */
  unitAmount: number
  /** 显示单价 */
  displayPrice: number
  /** 显示规格 */
  displayAmount: number
  // /** 单个单位名称(个)  */
  // unitName: string
  // /** 包裹名(箱)  */
  // packageName: string
}

declare interface ProductCategoryItem extends ProductItem {
  /** 规格列表 */
  categoryList: string[]
  /** 标题 */
  title: string
}

/** 购物车商品项 */
declare interface ProductCartItem extends ProductItem {
  /** 产品规格 */
  productInfoSpec: string
  /** 个数 */
  number: number
  /** 服务器个数 */
  serverNumber: number
  /** 个数更新中标识 */
  changingNumber: boolean
  /** 是否在售 */
  isOnSale: boolean
  /** 商品上架库存 */
  shelvesStock: number
  /** 商品是否已被删除 */
  isDeleted: boolean
}

/** 收藏商品项 */
declare interface ProductCollectionItem extends ProductItem {
  /** 商品规格 */
  specification: string
  /** 是否在售 */
  isOnSale: boolean
  /** 商品上架库存 */
  shelvesStock: number
  /** 商品是否已被删除 */
  isDeleted: boolean
}

/** 订单商品项 */
declare interface ProductOrderItem extends ProductItem {
  /** 产品规格 */
  productInfoSpec: string
  /** 个数 */
  number: number
  /** 订单数量 */
  totalPackage: string
}

/** 订单商品项 */
declare interface AftersalesOrderProductItem extends ProductItem {
  /** 产品规格 */
  productInfoSpec: string
  /** 个数 */
  number: number
  /** 订单数量 */
  totalPackage: string
  /** 是否勾选 */
  selected: boolean
  /** 总装箱数量 */
  quantity: number
}

/** 售后单商品项 */
declare interface ProductAftersalesItem extends ProductItem {
  /** 产品规格 */
  productInfoSpec: string
  /** 用户购买箱数 */
  number: number
  /** 用户退回箱数 */
  returnNumber: number
}

/** 商品规格 */
declare interface ProductSpecification {
  /** 商品Id */
  id: string
  /** 商品规格标题 */
  littletitle: string
  /** 商品图片 */
  productImg: string
  /** 商品标题 */
  title: string
  /** 商品规格 */
  productSpec: string
  /** 商品规格型号 */
  productSpecModel: string
  /** 商品规格名称 */
  productSpecName: string
  /** 商品规格编号 */
  productSpecNumber: string
}

/** 选择商品详情 所属5级分类 */
declare interface ProductDetails {
  /** 商品描述 */
  description: string
  /** 商品Id */
  id: string
  /** 图片详情 */
  imageList: string[]
  /** 商品名称 */
  name: string
  /** 商品分类 */
  ncCategory: string
  /** 商品编号 */
  ncCode: string
  /** 材质 */
  ncMaterial: string
  /** nc 计量单位名称 */
  ncMeterageUnitName: string
  /** 商品一箱的件数 */
  ncPackage: string
  /** 商品毛重 */
  ncWeight: string
  /** 商品图片 */
  productImg: string
  /** 销售单价 */
  unitPrice: number
  /** 销售规格 */
  unitAmount: number
  /** 显示单价 */
  displayPrice: number
  /** 显示规格 */
  displayAmount: number
  /** 规格选择 */
  specList: ProductSpecification[]
  /** 商品标题 */
  title: string
  /** 商品规格 */
  productSpec: string
  /** 商品规格型号 */
  productSpecModel: string
  /** 商品规格名称 */
  productSpecName: string
  /** 商品规格编号 */
  productSpecNumber: string
  /** 是否在售 */
  isOnSale: boolean
  /** 商品上架库存 */
  shelvesStock: number
  /** 市场价 */
  marketPrice: number
  /** 外箱规格 */
  outerBoxSpec: string
}

/** 筛选属性 */
declare interface SearchFilter {
  categoryId: string | undefined
  categoryName: string | undefined
  isHot: true | undefined
  isNew: true | undefined
  isPromoteSales: true | undefined
  isRecommend: true | undefined
}

/** 公司 */
declare interface Corporate {
  /** 公司名 */
  corporateName: string
  /** 公司ID */
  id: string
}

/** 地址 */
declare interface Address {
  /** 收货人地址 */
  address: string
  /** 市id */
  cityCode?: number
  /** 市名称 */
  cityName: string
  /** 市id */
  districtCode?: number
  /** 区名称 */
  districtName: string
  /** 会员地址Id */
  id: string
  /** 默认地址，0-否、1-是 */
  isDefault: number
  /** 标签 */
  label: string
  /** 收货人手机 */
  mobile: string
  /** 收货人名称 */
  name: string
  /** 省id */
  provinceCode?: number
  /** 省名称 */
  provinceName: string
  /** 收货人固话 */
  tel: string
}

/** 订单确认数据 */
declare interface ConfirmOrder {
  token: string
  sumPrice: string
  productList: ProductCartItem[]
}

/** 支付权限 */
declare type PaymentProvider = "alipay" | "wxpay" | "baidu" | "appleiap"

/** 支付参数 */
/** https://uniapp.dcloud.net.cn/api/plugins/payment.html#requestpayment */
declare interface PaymentData {
  /** 支付数据 */
  orderInfo: string
  /** 支付时间戳 */
  timeStamp?: string
  /** 随机字符串 */
  nonceStr?: string
  /** 统一下单接口返回参数值 */
  package?: string
  /** 签名算法 */
  signType?: string
  /** 签名 */
  paySign?: string
}

/** 订单 */
declare interface OrderItem {
  // 售后订单状态
  afterSaleStatus: number
  // 发票编号
  invoiceNo: string
  /** 订单ID */
  orderId: string
  /** 订单编号 */
  orderNo: string
  /** 订单状态 0→待发货；1→待收货；2→已完成；3→已取消；4→已下单 */
  orderStatus: string
  /** 订单金额 */
  orderPrice: number
  /** 应付金额 */
  shouldPayPrice: number
  /** 已付金额 */
  actualPayPrice: number
  /** 下单时间 */
  orderTime: number
  /** 确认收货时间 */
  receiveTime: number
  /** 支付状态 0→待支付；1→已支付；2→退款失败；3→退款成功；4→支付失败；5→退款中 */
  payStatus: string
  /** 支付时间 */
  payTime: datetime
  /** 商品列表 */
  productList: ProductOrderItem[]
  /** 售后单id */
  orderAfterSaleId: string
  /** 配送方式:0-场内配送;1-仓库自提;2-自费物流 */
  deliveryType: number
  /** 是否能申请售后 */
  ableAfterSale?: boolean
  /** 订单支付号（只有支付完成后才有此值） */
  payTransactionId?: string
}
// 我的优惠券
declare interface CouponItem {
  // 地址限制
  couponAreaLimit: {
    /** 优惠券地区限制列表 */
    couponAreaLimitItems: {
      /** 城市id */
      cityId: long
      /** 城市名称 */
      cityName: string
      /** 省份id */
      provinceId: long
      /** 省份名称 */
      provinceName: string
    }[]
  }
  couponExp: {
    /** 有效天数 */
    expDays?: long
    /** 有效日期区间 */
    ranges?: {
      /** 有效期截止日期 */
      endTime: datetime
      /** 有效期开始日期 */
      startTime: datetime
    }[]
  }
  /** 优惠券过期类型:0->设定;1->领用; */
  couponExpType: number
  /** 优惠券名称 */
  couponName: string
  /** 优惠券编号 */
  couponNo: string
  /** 用户关联优惠券id */
  couponMemberRelationId: long
  /** 优惠券使用价格限制 */
  couponPriceLimit: double
  /** 备注 */
  couponRemark: string
  /** 优惠券类型:0->满减;1->折扣; */
  couponType: number
  /** 优惠券用户类型限制:0->全部;1->新用户;2->老用户; */
  couponUserTypeLimit: number
  /** 优惠券面值 */
  couponValue: double
  /** 是否使用:0->否;1->是 */
  isUsed: number
  /** 过期时间 */
  overdueTime: datetime
}
// 领券中心
declare interface ReceiveItem {
  /** 优惠券总数 */
  couponAmount: number
  couponAreaLimit: {
    /** 优惠券地区限制列表 */
    couponAreaLimitItems: {
      /** 城市id */
      cityId: long
      /** 城市名称 */
      cityName: string
      /** 省份id */
      provinceId: long
      /** 省份名称 */
      provinceName: string
    }[]
  }
  couponExp: {
    /** 有效天数 */
    expDays: long
    /** 有效日期区间 */
    ranges: {
      /** 有效期截止日期 */
      endTime: datetime
      /** 有效期开始日期 */
      startTime: datetime
    }[]
  }
  /** 优惠券过期类型:0->设定;1->领用; */
  couponExpType: number
  /** 优惠券名称 */
  couponName: string
  /** 优惠券编号 */
  couponNo: string
  /** 优惠券使用价格限制 */
  couponPriceLimit: double
  /** 优惠券日领取限制 */
  couponReceiveDailyLimit: number
  /** 优惠券领取限制 */
  couponReceiveLimit: number
  /** 备注 */
  couponRemark: string
  /** 优惠券类型:0->满减;1->打折; */
  couponType: number
  /** 优惠券用户类型限制:0->全部;1->新用户;2->老用户; */
  couponUserTypeLimit: number
  /** 优惠券面值 */
  couponValue: double
  /** 发布时间 */
  publishTime: datetime
  /** 领券中心Id */
  receiveCouponCenterRelationId: long
  /** 优惠券剩余数 */
  residueAmount: number
  /** 序号 */
  serialnumber: long
  /** 优惠券地区限制 */
  strCouponAreaLimit: string
  /** 优惠券有效期 */
  strCouponExp: string
  /** 用户领取数量 */
  userReceiveAmount: number
  /** 用户当日领取数量 */
  userReceiveDailyAmount: number
}
// 活动
declare interface ActivityItem {
  /** 活动入口图 */
  activityEntranceUrl: string
  /** 活动id */
  activityId: long
  /** 活动名称 */
  activityName: string
  /** 活动编号 */
  activityNo: string
  /** 活动类型: 0->活动中心;1->普通活动 */
  activityType: number
  /** 是否能分享 */
  isShare: boolean
  /** 发布时间 */
  releaseTime: datetime
}

/** 订单详情 */
declare interface OrderDetails {
  /** 优惠金额 */
  discountAmount: number
  /** 售后订单状态 */
  afterSaleStatus: number
  // 发票编号
  invoiceNo: string
  /** 订单ID */
  orderId: string
  /** 订单编号 */
  orderNo: string
  /** 订单状态 0→待发货；1→待收货；2→已完成；3→已取消；4→已下单 */
  orderStatus: string
  /** 订单金额 */
  orderPrice: number
  /** 应付金额 */
  shouldPayPrice: number
  /** 已付金额 */
  actualPayPrice: number
  /** 退款时间 */
  refundTime: number
  /** 取消订单时间 */
  cancelTime: number
  /** 下单时间 */
  orderTime: number
  /** 支付时间 */
  payTime: number
  /** 支付超时时间 */
  payDeadline: number
  /** 订单完成时间 */
  completeTime: number
  /** 支付状态 0→待支付；1→已支付；2→退款失败；3→退款成功；4→支付失败；5→退款中 */
  payStatus: string
  /** 支付类型：0→微信；1→支付宝 */
  payType: string
  /** 商品列表 */
  productList: ProductOrderItem[]
  /** 收货人地址  */
  address: string
  /** 收货人手机号码  */
  mobile: string
  /**  收货人名称  */
  name: string
  /** 物流简称  */
  deliveryCode: string
  /** 物流名称  */
  deliveryName: string
  /** 物流单号  */
  deliveryNo: string
  /** 配送方式:0-场内配送;1-仓库自提;2-自费物流 */
  deliveryType: string
  /** 备注  */
  remark: string
  /** 售后单id */
  orderAfterSaleId: string
  /** 是否能申请售后 */
  ableAfterSale?: boolean
  /** 订单支付号（只有支付完成后才有此值） */
  payTransactionId?: string
}

/** 创建售后时的订单详情 */
declare interface AftersalesOrderDetails {
  /** 订单编号 */
  orderNo: string
  /** 订单完成时间 */
  completeTime: number
  /** 商品列表 */
  productList: AftersalesOrderProductItem[]
  /** 订单实付金额 */
  actualPayCost: number
  /** 原价金额 */
  totalPrice: number
}

/** 售后单 */
declare interface AftersalesItem {
  /** 发起售后时间 */
  aftersalesTime: number
  /** 售后单id */
  aftersalesId: long
  /** 售后单编号 */
  aftersalesNo: string
  /** 售后单状态 0→售后待审核，1→售后审核通过，2→售后审核驳回，3→仓库待签收，4→仓库已验收，5→同意退款，6→退款待确认，8→用户主动取消，9→已完成 */
  aftersalesStatus: number
  /** 售后单类型 0→退货退款 */
  aftersalesType: string
  /** 退款金额 */
  refundAmount: number
  /** 物流单号 */
  // deliveryNo: string
  /** 物流名称 */
  // deliveryName: string
  /** 订单编号 */
  orderNo: string
  /** 商品列表 */
  productList: ProductAftersalesItem[]
}

/** 售后单详情 */
declare interface AftersalesDetails {
  /** 售后操作时间 */
  afterSaleOperateDate: number
  /** 售后单id */
  aftersalesId: long
  /** 售后单编号 */
  aftersalesNo: string
  /** 售后单状态 0→售后待审核，1→售后审核通过，2→售后审核驳回，3→仓库待签收，4→仓库已验收，5→同意退款，6→退款待确认，8→用户主动取消，9→已完成 */
  aftersalesStatus: number
  /** 售后单类型 0→退货退款 */
  aftersalesType: string
  /** 物流单号 */
  deliveryNo: string
  /** 物流名称 */
  deliveryName: string
  /** 退货人地址 */
  backAddress: string
  /** 退货人电话 */
  backMobile: string
  /** 退货人信息 */
  backName: string
  /** 售后单备注 */
  remark: string
  /** 发起售后时间 */
  aftersalesTime: number
  /** 发货时间 */
  deliveryTime: number
  /** 收货时间 */
  receivedTime: number
  /** 退款时间 */
  refundTime: number
  /** 退款金额 */
  refundAmount: number
  /** 客户申请退款金额 */
  applyBackPrice: number
  /** 取消时间 */
  cancelTime: number
  /** 售后原因 */
  aftersalesReason: string
  /** 售后单图片 */
  backImgs: string
  /** 订单编号 */
  orderNo: string
  /** 商品列表 */
  productList: ProductAftersalesItem[]
  /** 仓库操作时间 */
  storehouseOperateDate: number
  /** 售后退款操作时间 */
  afterSaleRefundOperateDate: number
  /** 退款操作时间-财务 */
  refundOperateDate: number
}

/** 售后物流信息 */
interface AftersalesDelivery {
  orderNo: string
  /** 订单编号 */
  aftersalesNo: string
  /** 售后单时间 */
  aftersalesTime: number
  /** 售后单ID */
  aftersalesId: string
  /** 商品列表 */
  productList: ProductAftersalesItem[]
  /** 物流名称 */
  deliveryName: string
  /** 物流编号 */
  deliveryNo: string
}

/** 发票列表项 */
declare interface InvoiceItem {
  orderNumber: number
  // 包含订单个数
  applyInvoiceDate: number
  // 申请开票日期
  dutyParagraph: string
  // 税号
  id: string
  // 发票Id
  invoiceHeader: string
  // 发票抬头
  invoiceHeaderType: number
  // 抬头类型，0->企业;1->个人
  invoiceNo: string
  // 发票编号
  invoicePrice: number
  // 发票金额
  invoiceStatus: number
  // 发票状态，0->申请中;1->已开票
  invoiceType: number
  // 发票类型，0->增值税电子普通发票;1->增值税电子普通专票
}

/** 发票列表 */
interface InvoiceList {
  invoiceHeader: string
  // 发票抬头
  makeInvoiceId: string
  // 发票Id
  invoiceDutyParagraph: string
  // 税号
  invoiceBank: string
  // 开户银行
  invoiceBankAccount: string
  // 银行账号
  invoiceCompanyAddress: string
  // 公司地址
  invoiceCompanyTel: string
  // 公司电话
  invoiceEmail: string
  // 电子邮箱
  invoiceHeaderType: number
  // 抬头类型，0-企业
}
interface RelatedGoods {
  categoryId: number
  categoryImg: string
  categoryName: string
  price: string
  productId: string
}
interface InvoiceDetail {
  applyInvoiceDate: number
  dutyParagraph: string
  id: string
  invoiceHeader: string
  invoiceHeaderType: number
  invoiceNo: string
  invoicePrice: number
  invoiceStatus: number
  invoiceType: number
  orderNumber: number
}
interface NotIssueInvoice extends OrderItem {
  /** 是否勾选 */
  selected: boolean
}

interface PointsItem {
  /** 积分 */
  points: string
  /** 积分状态，0→到账 1→消耗 */
  pointsStatus: number
  /** 积分类型，0→消费积分；1→积分过期；2→积分兑换； */
  pointsType: number
  /** 业务编号 */
  serviceNo: string
  /** 业务类型：0→订单管理；1→兑换活动 */
  serviceType: number
}

interface ExchangeItem {
  couponExp: {
    /** 有效天数 */
    expDays: string
    /** 有效日期区间 */
    ranges: {
      /** 有效期截止日期 */
      endTime: number
      /** 有效期开始日期 */
      startTime: number
    }[]
  }
  /** 优惠券有效期 */
  couponExpStr: string
  /** 优惠券id */
  couponId: string
  /** 优惠券图片 */
  couponMainIMG: string
  /** 优惠券名称 */
  couponName: string
  /** 优惠券编号 */
  couponNo: string
  /** 所需积分 */
  integrate: string
  /** 兑换方案Id */
  rewardsPointsId: string
  /** 兑换方案编号 */
  rewardsPointsNo: string
}

interface ExchangeItemDetails {
  couponExp: {
    /** 有效天数 */
    expDays: string
    /** 有效日期区间 */
    ranges: {
      /** 有效期截止日期 */
      endTime: number
      /** 有效期开始日期 */
      startTime: number
    }[]
  }
  /** 优惠券有效期 */
  couponExpStr: string
  /** 优惠券id */
  couponId: string
  /** 优惠券名称 */
  couponName: string
  /** 优惠券编号 */
  couponNo: string
  /** 使用规则 */
  couponRule: string
  /** 优惠券副片 */
  couponViceIMG: string
  /** 所需积分 */
  integrate: string
  /** 月兑次数 */
  receiveAmount: string
  /** 兑换方案Id */
  rewardsPointsId: string
  /** 兑换方案编号 */
  rewardsPointsNo: string
}

interface ExchangeRecord {
  /** 优惠券id */
  couponId: string
  /** 优惠券名称 */
  couponName: string
  /** 优惠券编号 */
  couponNo: string
  /** 优惠券副片 */
  couponViceIMG: string
  /** 消耗积分 */
  integrate: long
  /** 是否使用: 0->未使用;1->已使用 */
  isUsed: number
  /** 积分兑换时间 */
  receiveTime: datetime
  /** 兑换方案Id */
  rewardsPointsId: long
  /** 兑换方案编号 */
  rewardsPointsNo: string
  /** 兑换记录Id */
  rewardsPointsReceiveRecordId: long
  /** 兑换记录编号 */
  rewardsPointsReceiveRecordNo: string
}

interface ExchangeRecordDetails {
  /** 优惠券id */
  couponId: string
  /** 优惠券名称 */
  couponName: string
  /** 优惠券编号 */
  couponNo: string
  /** 使用规则 */
  couponRule: string
  /** 优惠券副片 */
  couponViceIMG: string
  /** 兑换积分 */
  integrate: string
  /** 是否使用: 0->未使用;1->已使用 */
  isUsed: number
  /** 积分兑换时间 */
  receiveTime: number
  /** 兑换记录Id */
  rewardsPointsReceiveRecordId: string
  /** 兑换记录编号 */
  rewardsPointsReceiveRecordNo: string
}

interface PointsRecord {
  /** 创建时间 */
  createdTime: number
  /** 删除标记 */
  delFlag: number
  /** 会员id */
  memberId: string
  /** 用户积分详细id */
  memberPointsDetailId: string
  /** 订单支付时间 */
  orderPayTime: number
  /** 积分 */
  points: string
  /** 积分状态，0->到账;1->消耗 */
  pointsStatus: number
  /** 积分类型，0->消费积分；1->积分过期；2->积分兑换； */
  pointsType: number
  /** 业务Id */
  serviceId: long
  /** 业务编号 */
  serviceNo: string
  /** 业务类型：0->订单管理；1->兑换活动 */
  serviceType: number
  /** 展开更多 */
  isShowMore?: boolean
}
