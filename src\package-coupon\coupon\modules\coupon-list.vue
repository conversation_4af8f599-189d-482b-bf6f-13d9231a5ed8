<template>
  <scroll-view
    v-if="pageHeight"
    scroll-y
    :style="{ height: `${pageHeight}px` }"
    enable-back-to-top
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="refresh"
    @scrolltolower="getCouponList"
  >
    <view class="page-padding-top" />
    <template v-if="!refreshing">
      <v-loading-block v-if="loading" />
      <v-empty
        v-else-if="status === 'nomore' && couponList.length === 0"
        src="/static/image/empty-coupon.png"
      >
        <view> {{ emptyText }} </view>
      </v-empty>
      <template v-else>
        <coupon-item
          v-for="(item, itemIndex) in couponList"
          :key="itemIndex"
          :coupon="item"
          :is-show="itemIndex === showRuleIndex"
          @change-rules="changeRules(itemIndex)"
        />
        <v-loadmore
          v-if="!refreshing"
          :status="status"
          @click="getCouponList"
        />
      </template>
    </template>
    <view class="page-padding-bottom" />
  </scroll-view>
</template>

<script setup lang="ts">
import CouponItem from "./coupon-item.vue"
import { str2num } from "@/utils/type-transfer"

const props = defineProps<{
  pageHeight: number
  type: number
}>()

interface Emits {
  (event: "refresh"): void
}
const emits = defineEmits<Emits>()

const type = toRef(props, "type")
const emptyText = computed(() => {
  switch (type.value) {
    case 0:
    case 1:
      return "暂无可用优惠券"
    case 2:
      return "暂无历史优惠券"
    case 3:
      return "暂无失效优惠券"
  }
})

const showRuleIndex = ref(-1)
const changeRules = (index: number) => {
  if (showRuleIndex.value === index) {
    showRuleIndex.value = -1
  } else {
    showRuleIndex.value = index
  }
}

const couponList = ref<CouponItem[]>([])

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  emits("refresh")
}
const initCouponList = async () => {
  loading.value = true
  pageIndex.value = 1
  couponList.value = []
  status.value = "loadmore"
  await getCouponList()
  await nextTick()
  refreshing.value = false
  loading.value = false
}
/** 分页数据 */
const pageIndex = ref(1)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")

/** 获取优惠券列表 */
const getCouponList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  switch (type.value) {
    case 0:
      getCouponListByUserId()
      break
    case 1:
      getCouponListByUserId(0)
      break
    case 2:
      getHistoryCouponListByUserId(0)
      break
    case 3:
      getHistoryCouponListByUserId(1)
      break
  }
}
const getCouponListByUserId = async (couponType?: number) => {
  const response = await $api.getCouponListByUserId({
    orderField: "overdueTime",
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    sortType: "asc",
    requestParams: { couponType }
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      couponList.value = [...couponList.value, ...response.data.result]
      const totalCount = str2num(response.data.totalCount)
      status.value =
        couponList.value.length < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
}
const getHistoryCouponListByUserId = async (historyType: number) => {
  const response = await $api.getHistoryCouponListByUserId({
    orderField: historyType === 0 ? "updatedTime" : "overdueTime",
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    sortType: "desc",
    requestParams: { historyType }
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      couponList.value = [...couponList.value, ...response.data.result]
      const totalCount = str2num(response.data.totalCount)
      status.value =
        couponList.value.length < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
}
defineExpose({ initCouponList })
</script>

<style lang="scss" scoped></style>
