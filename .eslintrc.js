// eslint-disable-next-line @typescript-eslint/no-require-imports
const autoImport = require("./.eslintrc-auto-import.json")

module.exports = {
  root: true,
  env: {
    node: true
  },
  globals: {
    uni: "readonly",
    wx: "readonly",
    getApp: "readonly",
    getCurrentPages: "readonly",
    ...autoImport.globals // 这里导入auto-import的全局量
  },
  parser: "vue-eslint-parser",
  parserOptions: {
    ecmaVersion: "latest",
    parser: {
      js: "espree",
      ts: "@typescript-eslint/parser",
      "<template>": "@typescript-eslint/parser"
    },
    sourceType: "module"
  },
  plugins: ["@typescript-eslint"],
  extends: [
    "eslint:recommended",
    "standard",
    "plugin:vue/vue3-recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  rules: {
    // 声明未使用给 @typescript-eslint/no-undef 判断
    "no-undef": "off",
    // 更倾向用 string[] 而不是 Array<string>
    "@typescript-eslint/array-type": ["warn"],
    // 这里单行最大属性改为3个
    "vue/max-attributes-per-line": ["warn", { singleline: { max: 3 } }],
    // 更倾向用 #default="scope" 而不是 v-slot="scope" 能和自定义命名的插槽统一
    "vue/v-slot-style": [
      "warn",
      {
        atComponent: "shorthand",
        default: "shorthand",
        named: "shorthand"
      }
    ],
    // 标签有属性的时候，子级更倾向于换新行，而不是嵌套
    "vue/singleline-html-element-content-newline": [
      "warn",
      {
        ignoreWhenNoAttributes: true,
        ignoreWhenEmpty: true
      }
    ],
    // 更倾向于双引号
    quotes: ["warn", "double"]
  }
}
