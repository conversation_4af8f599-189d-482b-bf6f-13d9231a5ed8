<template>
  <image
    v-if="src"
    :src="src"
    class="v-image"
    :class="{ loaded: loaded, border: border }"
    :style="{
      height: height,
      width: width,
      padding: padding,
      margin: margin,
      borderRadius: borderRadius
    }"
    :lazy-load="lazyLoad"
    :mode="mode"
    @load="loaded = true"
    @error="$emit('error')"
    @click="clickHandle"
  />
  <view
    v-else
    class="holder flex-center-center"
    :style="{
      height: height,
      width: width,
      margin: margin,
      borderRadius: borderRadius
    }"
  >
    <image
      class="v-image loaded"
      :src="logoSrc"
      mode="aspectFit"
      :style="{
        height: '100%',
        width: '100%'
      }"
    />
  </view>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  src: string
  width?: string
  height?: string
  mode?: "scaleToFill" | "aspectFit" | "aspectFill" | "widthFix" | "heightFix"
  padding?: string
  margin?: string
  lazyLoad?: boolean
  borderRadius?: string
  border?: boolean
  preview?: boolean
}>(), {
  src: undefined,
  width: "100%",
  height: undefined,
  mode: "widthFix",
  padding: "",
  margin: "",
  lazyLoad: false,
  borderRadius: "14rpx",
  border: false,
  preview: false
})

interface Emits {
  (event: "click"): void
  (event: "error"): void
}
const emits = defineEmits<Emits>()

const loaded = ref(false)

const src = toRef(props, "src")
const preview = toRef(props, "preview")

const clickHandle = () => {
  if (preview.value && src.value) {
    uni.previewImage({
      urls: [src.value]
    })
  } else {
    emits("click")
  }
}

import { webName } from '@/apis/config' 

const logoSrc = computed(() => {
  if (webName === 'cheerray') {
    return "/static/image/image-holder-cheerray.png"
  } else {
    return "/static/image/image-holder.png"
  }
})

</script>

<style lang="scss" scoped>
.v-image {
  display: block;
  box-sizing: border-box;
  opacity: 0;
  transition: opacity 150ms linear;
  &.loaded {
    opacity: 1;
  }
  &.border {
    border: 1px solid #eee;
  }
}
.holder {
  box-sizing: border-box;
  padding: 10rpx;
  background-color: #fafafa;
  border-radius: 8rpx;
}
</style>
