<template>
  <view>
    <view class="page-header">
      <v-navbar title="账号与设置" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <view class="v-block plain">
        <view class="action flex-center-between" @click="navigateToUserInfo">
          <view> 更新头像/昵称 </view>
          <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
        </view>
        <!-- <view class="divider" />
        <view class="action flex-center-between">
          <view> 用户服务协议 </view>
          <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
        </view>
        <view class="divider" />
        <view class="action flex-center-between">
          <view> 隐私权政策 </view>
          <v-icon size="22rpx" src="/static/icons/right-arrow.png" />
        </view> -->
      </view>
      <view class="logout-button" @click="logout">
        退出登录
      </view>
      <template v-if="isTest">
        <view style="padding: 30rpx">
          <v-button type="primary" @click="test1">
            获取登录/支付 code
          </v-button>
        </view>
        <view style="padding: 0 30rpx 30rpx">
          <v-button
            type="primary"
            open-type="getPhoneNumber"
            @getphonenumber="test2"
          >
            获取手机号 code
          </v-button>
        </view>
        <view style="padding: 0 30rpx 30rpx">
          <v-button type="primary" @click="test3">
            获取头像昵称
          </v-button>
        </view>
      </template>
    </scroll-view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { clearToken } from "@/utils/auth/token"

const { pageHeight } = usePageLayout()

const navigateToUserInfo = () => {
  uni.navigateTo({ url: "/pages/user/user-info" })
}

const logout = () => {
  clearToken()
  uni.switchTab({ url: "/pages/index/index-page" })
}

// 测试相关
const isTest = ref(false)
onLoad(() => {
  isTest.value = uni.getStorageSync("test_mode")
})
const test1 = () => {
  uni.login({
    success: (res) => {
      console.log("获取成功", res)
      console.log("code: ", res.code)
    },
    fail: (err) => {
      console.log("获取失败", err)
    }
  })
}
const test2 = (event: GetPhoneNumberEvent) => {
  console.log("获取手机code", event)
  console.log("code: ", event?.detail?.code)
}
const test3 = () => {
  uni.getUserProfile({
    desc: "用于用户页展示微信头像、昵称",
    success: (res) => {
      console.log("获取成功", res)
      console.log("昵称: ", res.userInfo.nickName)
      console.log("头像: ", res.userInfo.avatarUrl)
    },
    fail: (err) => {
      console.log("获取失败", err)
    }
  })
}
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0 0 24rpx;
  padding: 0 24rpx;
}
.action {
  padding: 24rpx 0;
}
.logout-button {
  background-color: white;
  text-align: center;
  line-height: 106rpx;
  font-weight: bold;
  color: #f06671;
}
</style>
