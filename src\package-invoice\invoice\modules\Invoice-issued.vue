<template>
  <view class="v-block" @click="goToInvoiceDetail">
    <view class="invoice-headline flex-center-between">
      <view class="text-light text-24 flex-center">
        {{ formateUtc(propsData.applyInvoiceDate) }}
      </view>
      <view class="text-light text-24 flex-center">
        <view class="invoice-head-lable">
          开票金额：
        </view>
        <v-price boold size="28rpx" :price="propsData.invoicePrice" />
      </view>
    </view>
    <view class="divider order-info-margin-adjust" />
    <view class="info flex-center-between">
      <view class="invoice-info">
        <view class="info-row flex-center-between">
          <view class="flex">
            <view class="label">
              发票申请编号:
            </view>
            <view class="text-1-row">
              {{ propsData.invoiceNo }}
            </view>
          </view>
          <view class="flex">
            <view class="label">
              抬头类型:
            </view>
            <view class="text-1-row">
              {{ propsData.invoiceHeaderType === 0 ? "企业" : "个人" }}
            </view>
          </view>
        </view>
        <view class="info-row flex">
          <view class="label">
            发票类型:
          </view>
          <view class="text-1-row">
            {{
              propsData.invoiceType === 0
                ? "增值税电子普通发票"
                : "增值税电子普通专票"
            }}
          </view>
        </view>
        <view class="info-row flex">
          <view class="label">
            发票抬头:
          </view>
          <view class="flex-1">
            {{ propsData.invoiceHeader }}
          </view>
        </view>
        <view v-if="propsData.dutyParagraph" class="info-row flex">
          <view class="label">
            税号:
          </view>
          <view class="text-1-row">
            {{ propsData.dutyParagraph }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
const props = defineProps<{
  data: InvoiceItem
}>()
const propsData = toRef(props, "data")
const goToInvoiceDetail = () => {
  uni.navigateTo({
    url: `/package-invoice/invoice/invoice-detail?invoiceno=${propsData.value.invoiceNo}`
  })
}
</script>

<style lang="scss" scoped>
.v-block {
  padding: 4rpx 24rpx;
}
.price {
  font-weight: 800;
}
.invoice-headline {
  height: 73rpx;
}
.invoice-head-lable {
  color: #666666;
  font-size: 20rpx;
}
.info {
  .invoice-info {
    width: 100%;
  }
  .invoice-info {
    .info-row {
      margin-bottom: 20rpx;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  margin: 21rpx 0;
  align-items: flex-start;
}
.label {
  width: fit-content;
  margin-right: 10rpx;
}
</style>
