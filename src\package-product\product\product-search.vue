<template>
  <view>
    <view class="page-header background-white">
      <v-navbar :title="title" back />
      <view class="search-input-container">
        <v-search-input
          v-model="searchInputValue"
          :focus="isShowHistory"
          @confirm="search"
          @focus="showHistory"
        />
      </view>
      <view class="tabs flex">
        <view
          class="tabs-item flex-center-center"
          :class="{ active: orderField === undefined }"
          @click="clickGeneralTab"
        >
          <view class="text">
            综合
          </view>
        </view>
        <view
          class="tabs-item flex-center-center"
          :class="{ active: orderField === 'productPrice' }"
          @click="clickPriceTab"
        >
          <view class="text">
            价格
          </view>
          <view class="icon flex-col flex-center">
            <view class="asc">
              <v-icon
                size="16rpx"
                :active="sortType === 'asc'"
                src="/static/icons/up-arrow.png"
                active-src="/static/icons/up-arrow-active.png"
              />
            </view>
            <view class="desc">
              <v-icon
                size="16rpx"
                :active="sortType === 'desc'"
                src="/static/icons/up-arrow.png"
                active-src="/static/icons/up-arrow-active.png"
              />
            </view>
          </view>
        </view>
        <!-- <view
          class="tabs-item flex-center-center"
          :class="{ active: isShowFilter }"
          @click="showFilter"
        >
          <view class="text">
            筛选
          </view>
          <view class="icon flex-col flex-center">
            <v-icon
              size="29rpx"
              margin="4rpx 8rpx 0"
              :active="isShowFilter"
              src="/static/icons/product/filter.png"
              active-src="/static/icons/product/filter-active.png"
            />
          </view>
        </view> -->
      </view>
    </view>
    <scroll-view
      v-if="pageHeight"
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
      @scroll="scrollHandle"
    >
      <view class="page-padding-top" />
      <template v-if="!refreshing">
        <v-loading-block v-if="loading" />
        <v-empty
          v-else-if="productList.length === 0"
          src="/static/image/empty-search.png"
        >
          暂无商品
        </v-empty>
        <template v-else>
          <view class="product-list flex--between flex-warp">
            <v-product-block
              v-for="(product, index) in productList"
              :key="product.id"
              :show="index > displayMinIndex && index < displayMaxIndex"
              :product="product"
              @select="() => showSelect(product)"
            />
          </view>
        </template>
      </template>
      <view class="page-padding-bottom" />
    </scroll-view>
    <v-product-select ref="selectRef" />
    <v-popover v-model="isShowHistory" mode="top" :top="historyTop">
      <v-search-input-history ref="historyRef" @click="clickSearchHistory" />
    </v-popover>
    <v-popover
      :model-value="isShowFilter"
      mode="top"
      :top="filterTop"
      @close="isShowFilter = false"
    >
      <view class="filter-pad">
        <view class="filter-title text-bold">
          商品标签
        </view>
        <view class="filter-list flex flex-warp">
          <view
            class="filter-item text-1-row"
            :class="{ active: filter.isNew }"
            @click="toggleFilter('isNew')"
          >
            新品
          </view>
          <view
            class="filter-item text-1-row"
            :class="{ active: filter.isHot }"
            @click="toggleFilter('isHot')"
          >
            热销
          </view>
          <view
            class="filter-item text-1-row"
            :class="{ active: filter.isRecommend }"
            @click="toggleFilter('isRecommend')"
          >
            推荐
          </view>
          <view
            class="filter-item text-1-row"
            :class="{ active: filter.isPromoteSales }"
            @click="toggleFilter('isPromoteSales')"
          >
            促销
          </view>
        </view>
        <view class="button-list flex-center-between">
          <v-button
            width="300rpx"
            height="86rpx"
            font-size="28rpx"
            @click="filterReset"
          >
            重置
          </v-button>
          <v-button
            type="primary"
            width="300rpx"
            height="86rpx"
            font-size="28rpx"
            @click="filterCommit"
          >
            确定
          </v-button>
        </view>
      </view>
    </v-popover>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { categoryNameToList } from "@/utils/data-transfer"
import { num2str, str2num } from "@/utils/type-transfer"

const { pageHeight, rpxRatio } = usePageLayout()

const title = ref("商品列表")

const { navBarHeight, statusBarHeight } = useNavbarLayout()
const historyTop = computed(() => {
  const navHeight = navBarHeight.value + statusBarHeight.value
  return `calc(${navHeight}px + 70rpx)`
})
const filterTop = computed(() => {
  const navHeight = navBarHeight.value + statusBarHeight.value
  return `${navHeight}px`
})

/** 搜索内容 */
const searchInputValue = ref("")
let keyword = ""
/** 搜搜历史 */
const historyRef = ref()
const isShowHistory = ref(false)
const showHistory = async () => {
  if (isShowFilter.value) return
  isShowHistory.value = true
}
const setSearchHistory = (searchHistory: string) => {
  historyRef.value?.setSearchHistory(searchHistory)
}
const clickSearchHistory = (searchHistory: string) => {
  searchInputValue.value = searchHistory
  search()
}

/** 筛选 */
const isShowFilter = ref(false)
const searchFilter = ref<SearchFilter>({
  categoryId: undefined,
  categoryName: undefined,
  isHot: undefined,
  isNew: undefined,
  isPromoteSales: undefined,
  isRecommend: undefined
})
const filter = ref<SearchFilter>({
  categoryId: undefined,
  categoryName: undefined,
  isHot: undefined,
  isNew: undefined,
  isPromoteSales: undefined,
  isRecommend: undefined
})
const toggleFilter = (
  key: "isHot" | "isNew" | "isPromoteSales" | "isRecommend"
) => {
  const current = filter.value[key]
  filter.value.isHot = undefined
  filter.value.isNew = undefined
  filter.value.isPromoteSales = undefined
  filter.value.isRecommend = undefined
  if (!current) filter.value[key] = true
}
// const showFilter = async () => {
//   if (isShowHistory.value) return
//   filter.value.isHot = searchFilter.value.isHot
//   filter.value.isNew = searchFilter.value.isNew
//   filter.value.isPromoteSales = searchFilter.value.isPromoteSales
//   filter.value.isRecommend = searchFilter.value.isRecommend
//   isShowFilter.value = true
// }
const filterReset = () => {
  filter.value = {
    categoryId: undefined,
    categoryName: undefined,
    isHot: undefined,
    isNew: undefined,
    isPromoteSales: undefined,
    isRecommend: undefined
  }
}
const filterCommit = () => {
  searchFilter.value.isHot = filter.value.isHot
  searchFilter.value.isNew = filter.value.isNew
  searchFilter.value.isPromoteSales = filter.value.isPromoteSales
  searchFilter.value.isRecommend = filter.value.isRecommend
  search()
}

/** 排序 */
const orderField = ref<string | undefined>()
const sortType = ref<"asc" | "desc" | undefined>()
const clickGeneralTab = () => {
  if (orderField.value === undefined) return
  orderField.value = undefined
  sortType.value = undefined
  getProductList()
}
const clickPriceTab = () => {
  orderField.value = "productPrice"
  sortType.value = sortType.value !== "asc" ? "asc" : "desc"
  getProductList()
}

/** 虚拟列表逻辑 */
const scrollTop = ref(0)
const scrollHandle = (event: ScrollEvent) => {
  scrollTop.value = event.detail.scrollTop
}
/** 上部留空大小 */
const pagePaddingTop = computed(() =>
  rpxRatio.value ? 24 / rpxRatio.value : 0
)
/** 每行商品高度 */
const productBlockHeight = computed(() =>
  rpxRatio.value ? 564 / rpxRatio.value : 0
)
/** 起始序号 */
const displayMinIndex = computed(() => {
  if (!pagePaddingTop.value || !productBlockHeight.value) return 0
  const current = scrollTop.value - pagePaddingTop.value
  const lineIndex = Math.floor(current / productBlockHeight.value) - 2
  return lineIndex * 2
})
/** 终止序号 */
const displayMaxIndex = computed(() => {
  if (!pagePaddingTop.value || !productBlockHeight.value) return 0
  const current = scrollTop.value - pagePaddingTop.value + pageHeight.value
  const lineIndex = Math.floor(current / productBlockHeight.value) + 3
  return lineIndex * 2
})

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = () => {
  refreshing.value = true
  search()
}

/** 商品列表 */
const productList = ref<ProductBlock[]>([])
/** 搜索 */
const search = async () => {
  isShowHistory.value = false
  isShowFilter.value = false
  searchInputValue.value = searchInputValue.value.trim()
  keyword = searchInputValue.value
  setSearchHistory(keyword)
  productList.value = []
  getProductList()
}
onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  searchInputValue.value = query?.keyword ?? ""
  searchFilter.value.categoryId = query?.categoryid
  title.value = query?.categoryname ? query.categoryname : "商品列表"
})
onReady(search)

/** 获取商品 */
const getProductList = async () => {
  if (loading.value) return
  loading.value = true
  const id = searchFilter.value.categoryId
    ? str2num(searchFilter.value.categoryId)
    : undefined
  const response = await $api.searchProduct({
    keyword,
    categoryIdThree: id,
    isHot: searchFilter.value.isHot,
    isNew: searchFilter.value.isNew,
    isPromoteSales: searchFilter.value.isPromoteSales,
    isRecommend: searchFilter.value.isRecommend,
    orderField: orderField.value,
    sortType: sortType.value
  })
  switch (response.__error) {
    case undefined: {
      productList.value =
        response.data?.map?.((item) => ({
          id: num2str(item.productInfoId),
          image: item.categoryImg,
          name: item.categoryName,
          categoryList: item.productSpecModelList?.length
            ? item.productSpecModelList
            : categoryNameToList(item.categoryName),
          unitPrice: str2num(item.productPrice),
          displayPrice: str2num(item.displayUnitPrice),
          title: item.productTitle
        })) ?? []
      break
    }
  }
  loading.value = false
  await nextTick()
  refreshing.value = false
}

/** 商品选择 */
const selectRef = ref()
const showSelect = (product: ProductBlock) => {
  selectRef.value.showSelect({ id: product.id })
}
</script>

<style lang="scss" scoped>
.search-input-container {
  margin: 0 24rpx;
}
.tabs {
  height: 86rpx;
  .tabs-item {
    position: relative;
    flex: 1;
    .text {
      color: #999999;
      transition: color 0.1s ease-out;
    }
    .icon {
      margin-left: 8rpx;
      .desc {
        transform-origin: 50% 50%;
        transform: rotate(180deg);
      }
    }
    &::after {
      content: " ";
      position: absolute;
      left: 50%;
      bottom: 0;
      height: 4rpx;
      width: 0;
      background-color: #005bac;
      transform: translateX(-50%);
      transition: width 0.1s ease-out;
    }
    &.active {
      .text {
        color: #231815;
        font-weight: bold;
      }
      &::after {
        width: 110rpx;
      }
    }
  }
}
.filter-pad {
  padding: 24rpx;
  background-color: white;
  border-radius: 0 0 16rpx 16rpx;
  .filter-title {
    margin-bottom: 30rpx;
  }
  .filter-list {
    margin-bottom: 24rpx;
  }
  .filter-item {
    margin-right: 24rpx;
    margin-bottom: 20rpx;
    padding: 0 28rpx;
    color: #231815;
    font-size: 24rpx;
    line-height: 50rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    transition: color 0.2s linear, background-color 0.2s linear;
    &.active {
      background-color: #005bac;
      color: white;
    }
  }
  .out-of-stock {
    margin-bottom: 30rpx;
  }
  .button-list {
    padding: 0 20rpx;
  }
}
.page-padding-top {
  height: 24rpx;
}
.product-list {
  padding: 0 24rpx;
  .product-block-container {
    height: fit-content;
    width: fit-content;
  }
}
</style>
