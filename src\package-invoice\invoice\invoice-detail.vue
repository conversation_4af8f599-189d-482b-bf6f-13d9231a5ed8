<template>
  <view>
    <view class="page-header">
      <v-navbar title="发票详情" back />
    </view>
    <v-loading-block v-if="!invoiceDetail" />
    <template v-else>
      <view class="v-block">
        <view class="headline flex-center-center">
          {{ invoiceDetail.invoiceStatus === 0 ? "申请中" : "已开票" }}
        </view>
        <view class="flex-center-center adjust-margin">
          <view class="price-notice">
            开票金额 &nbsp;&nbsp;
          </view>
          <v-price boold size="28rpx" :price="invoiceDetail.invoicePrice" />
        </view>
        <view class="v-form-item flex-center">
          <view class="label invoice-label">
            发票申请编号:
          </view>
          <view class="flex-center flex-1">
            {{ invoiceDetail.invoiceNo }}
          </view>
        </view>
        <view class="v-form-item flex-center">
          <view class="label invoice-label">
            发票类型:
          </view>
          <view class="flex-center flex-1">
            {{
              invoiceDetail.invoiceType === 0
                ? "电子普通发票"
                : "增值税电子专用发票"
            }}
          </view>
        </view>
        <view class="v-form-item flex-center">
          <view class="label invoice-label">
            发票抬头:
          </view>
          <view class="flex-center flex-1">
            {{ invoiceDetail.invoiceHeader }}
          </view>
        </view>
        <view
          v-if="invoiceDetail.invoiceHeaderType === 0"
          class="v-form-item flex-center"
        >
          <view class="label invoice-label">
            税&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号:
          </view>
          <view class="flex-center flex-1">
            {{ invoiceDetail.dutyParagraph }}
          </view>
        </view>
        <view class="v-form-item flex-center">
          <view class="label invoice-label">
            开票金额:
          </view>
          <view class="flex-center flex-1">
            ￥
            {{ invoiceDetail.invoicePrice.toFixed(2) }}
          </view>
        </view>
        <view class="v-form-item flex-center">
          <view class="label invoice-label">
            申请开票日期:
          </view>
          <view class="flex-center flex-1">
            {{ formateUtc(invoiceDetail.applyInvoiceDate) }}
          </view>
        </view>
      </view>
      <view class="notice-headline">
        <view class="notice">
          温馨提示：
        </view>
        <view class="text">
          发票开取成功后，我们将发送至您的邮箱，请查收邮件，有问题可联系在线客服。
        </view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
import { str2num } from "@/utils/type-transfer"
import { checkToken } from "@/utils/auth/token"

const invoiceDetail = ref<InvoiceDetail>()
const invoiceNo = ref("")

const { safeNavigateBack } = usePageLayout()

const getInvoiceDetail = async () => {
  const response = await $api.findOrderInvoiceDetail({
    invoiceNo: invoiceNo.value
  })
  switch (response.__error) {
    case undefined: {
      invoiceDetail.value = {
        ...response.data,
        invoicePrice: str2num(response.data.invoicePrice)
      }
    }
  }
}

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.invoiceno) {
    safeNavigateBack()
  } else if (query?.customer && !checkToken()) {
    uni.redirectTo({
      url: `/pages/customer/customer-service?source=${query.customer}&number=${query.invoiceno}`
    })
  } else {
    invoiceNo.value = query.invoiceno
    getInvoiceDetail()
  }
})
</script>

<style lang="scss" scoped>
.v-block {
  margin: 24rpx;
  padding-left: 48rpx;
  padding-bottom: 80rpx;
}
.invoice-label {
  min-width: 180rpx;
  color: #999999;
}
.headline {
  color: #231815;
  font-size: 40rpx;
  font-weight: 800;
  margin: 100rpx 0 25rpx 0;
}
.price-color {
  font-size: 28rpx;
  font-weight: 800;
  color: #e60012;
}
.price-notice {
  font-size: 28rpx;
  color: #999999;
}
.adjust-margin {
  margin-bottom: 63rpx;
}
.notice-headline {
  margin: 24rpx;
}
.notice {
  font-size: 28rpx;
  color: #e60012;
  margin-bottom: 20rpx;
}
.text {
  color: #666666;
  line-height: 40rpx;
  font-size: 22rpx;
}
</style>
