<template>
  <view>
    <view class="page-background-white" />
    <view class="page-header" :class="{ 'hide-shadow': !headerShadow }">
      <v-navbar>
        <view class="logo">
          <v-image
            :height="navBarHeight + 'px'"
            mode="heightFix"
            :src="logoSrc"
          />
        </view>
      </v-navbar>
      <view class="search-input-container">
        <view
          class="search-input flex-center-center"
          @click="navigateToSearchInput"
        >
          <v-icon
            size="32rpx"
            margin-right="8rpx"
            src="/static/icons/search.png"
          />
          <view class="placeholder">
            搜索
          </view>
        </view>
      </view>
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      @scroll="scrollHandle"
    >
      <view class="page-padding-top" />
      <!-- 广告 -->
      <view class="swiper-container">
        <v-advert-swiper :space="0" height="320rpx" width="702rpx" />
      </view>
      <!-- 菜单列表 -->
      <view class="menu-list-container">
        <view class="menu-list flex flex-warp">
          <view
            v-for="category in categoryList"
            :key="category.name"
            class="menu-item flex-col flex-center-center"
            @click="() => clickCategory(category)"
          >
            <v-image height="120rpx" width="120rpx" :src="category.img" />
            <view class="text-24 menu-label text-1-row">
              {{ category.name }}
            </view>
          </view>
        </view>
      </view>
      <!-- 广告 -->
      <view class="banner-container">
        <v-advert-swiper :space="1" height="230rpx" width="702rpx" />
      </view>
      <!-- 展示商品 -->
      <view class="product-list">
        <v-loading-block v-if="loading" />
        <v-empty
          v-else-if="productList.length === 0"
          src="/static/image/empty-search.png"
        >
          暂无商品
        </v-empty>
        <template v-else>
          <view class="flex--between flex-warp">
            <v-product-block
              v-for="product in productList"
              :key="product.id"
              class="product-item"
              :product="product"
              :category="false"
              @select="() => showSelect(product)"
            />
          </view>
        </template>
      </view>
      <view class="page-padding-bottom background-gray" />
    </scroll-view>
    <v-product-select ref="selectRef" switch-tab />
    <view class="page-footer">
      <v-tabbar :index="0" />
      <view
        v-if="isRenderPopper"
        class="popper-mask"
        :style="{ display: isShowPopper ? 'flex' : 'none' }"
      >
        <view class="popper">
          <!-- <view class="image" /> -->
          <image
            class="image"
            mode="widthFix"
            :src="popperImg"
            @load="loadHandle"
            @click="navigate({ url: popperUrl })"
          />
        </view>
        <view class="close-button" @click="closePopper">
          <image
            src="/static/icons/components/close-icon.png"
            class="close-icon"
            mode="widthFix"
          />
        </view>
      </view>
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { checkToken } from "@/utils/auth/token"
import { str2num } from "@/utils/type-transfer"

const { pageHeight } = usePageLayout()
const { navBarHeight } = useNavbarLayout()

const navigateToSearchInput = () => {
  uni.navigateTo({ url: "/pages/product/product-search-input-page" })
}

/** 菜单目录 */
const pageStore = $store.page()
const categoryList = computed(() => pageStore.categoryList?.slice(0, 8) ?? [])
const clickCategory = (category: Category) => {
  pageStore.changeFirstCategory(category.id)
  uni.switchTab({ url: "/pages/product/product-page" })
}
onLoad(() => pageStore.getCategoryList())

/** 加载 */
const loading = ref(false)
/** 推荐商品 */
const productList = ref<ProductBlock[]>([])
const getProductList = async () => {
  loading.value = true
  const response = await $api.getRecommendProductList({})
  switch (response.__error) {
    case undefined: {
      productList.value =
        response.data?.map?.((item) => ({
          id: item.productId,
          image: item.recommendImg || item.productImg,
          name: item.productSpecName,
          categoryList: [],
          unitPrice: str2num(item.ncRetailPrice),
          displayPrice: str2num(item.displayUnitPrice),
          title: item.productSpecName
        })) ?? []
    }
  }
  loading.value = false
}

onLoad(getProductList)
/** 选择商品 */
const selectRef = ref()
const showSelect = (product: ProductBlock) => {
  selectRef.value.showSelect({ id: product.id })
}

const headerShadow = ref(false)
const scrollHandle = (event: ScrollEvent) => {
  if (event.detail.scrollTop > 50) {
    headerShadow.value = true
  } else {
    headerShadow.value = false
  }
}

const isRenderPopper = ref(false)
const isShowPopper = ref(false)
const popperImg = ref("")
const popperUrl = ref("")
const loadHandle = () => {
  isShowPopper.value = true
}
const closePopper = () => {
  isRenderPopper.value = false
  isShowPopper.value = false
}

onShow(() => {
  getAdvertising()
})
const imageList = ref<
{
  img: string
  url: string
}[]
>([])
const getAdvertising = async () => {
  if (!checkToken()) return
  const img = uni.getStorageSync("popperImg")
  const url = uni.getStorageSync("popperUrl")
  const response = await $api.getAdvertising({ advertisingSpace: 2 })
  switch (response.__error) {
    case undefined: {
      imageList.value = response.data.advertisingDetailList
        ?.filter((item) => item.isEnable === 0)
        .map((item) => ({
          img: item.imgUrl,
          url: item.url
        })) ?? []
      if (imageList.value.length) {
        popperImg.value = imageList.value[0].img
        popperUrl.value = imageList.value[0].url
        if (img === popperImg.value && url === popperUrl.value) return
        uni.setStorageSync("popperImg", popperImg.value)
        uni.setStorageSync("popperUrl", popperUrl.value)
        isRenderPopper.value = true
      }
    }
  }
}
const navigate = (item: Record<string, unknown>) => {
  if (!item.url) return
  isRenderPopper.value = false
  isShowPopper.value = false
  switch (true) {
    case /^\/pages\/index\/index-page/.test(item.url as string):
    case /^\/pages\/product\/product-page/.test(item.url as string):
    case /^\/pages\/cart\/cart-page/.test(item.url as string):
    case /^\/pages\/user\/user-page/.test(item.url as string):
      uni.switchTab({
        url: item.url as string,
        fail: () => {
          console.warn(item.url, "不是一个合法的页面路径")
        }
      })
      break
    default:
      uni.navigateTo({
        url: item.url as string,
        fail: () => {
          console.warn(item.url, "不是一个合法的页面路径")
        }
      })
  }
}

import { webName } from '@/apis/config' 

const logoSrc = computed(() => {
  if (webName === 'cheerray') {
    return "/static/image/logo-cheerray.png"
  } else {
    return "/static/image/logo.png"
  }
})

</script>

<style lang="scss" scoped>
.search-input-container {
  padding: 10rpx 24rpx;
  .search-input {
    background-color: #f5f5f5;
    border-radius: 100vh;
    padding: 0 8rpx;
    height: 70rpx;
    box-sizing: border-box;
  }
}
.menu-list-container {
  margin: 0 10rpx 10rpx;
}
.swiper-container {
  margin: 0 24rpx 24rpx;
}
.banner-container {
  margin: 0 24rpx 24rpx;
}
.logo {
  position: absolute;
  left: 24rpx;
  bottom: 0;
}
.menu-list {
  .menu-item {
    width: 25%;
    margin-bottom: 20rpx;
  }
}
.product-list {
  background-color: #f5f5f5;
  padding: 37rpx 24rpx 0;
}

.popper-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.popper {
  width: fit-content;
}
.image {
  width: 90vw;
}
.close-button {
  margin-top: 60rpx;
  width: fit-content;
  padding: 10rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.25);
}
.close-icon {
  width: 50rpx;
  display: block;
}
</style>
