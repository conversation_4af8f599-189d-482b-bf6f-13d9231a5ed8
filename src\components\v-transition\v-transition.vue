<template>
  <view
    class="v-transition"
    :class="customClass"
    :data="transitionData"
    :change:data="wxs.changeData"
  >
    <view class="v-transition-content" style="display: none">
      <slot />
    </view>
  </view>
</template>

<script lang="ts">
import wxs from "@/utils/wxs"

export default {
  props: {
    customClass: {
      type: String,
      default: ""
    },
    show: {
      type: Boolean,
      default: false
    },
    duration: {
      type: Number,
      default: 300
    },
    timing: {
      type: String,
      default: "linear"
    },
    type: {
      type: String,
      default: "fade"
    }
  },
  data () {
    return {
      wxs: {
        changeData: wxs.changeData
      }
    }
  },
  computed: {
    transitionData () {
      return {
        duration: this.duration,
        type: this.type,
        show: this.show,
        timing: this.timing
      }
    }
  }
}
</script>

<script src="./v-transition.wxs" module="wxs" lang="wxs"></script>

<style lang="scss" scoped>
.v-transition {
  overflow: hidden;
}
</style>
