function bundle() {
  DEV_BASEURL="https://welshinepayment.dev.dthy.cn/applet"
  DEV_APPID="wx4788798388c0e0ab"
  DEV_KFID="kfc24d79d61f7da4981"
  DEV_CORPID="wwb612f4203c7ac868"

  TEST_BASEURL="https://welshinepayment.test.dthy.cn/applet"
  TEST_APPID="wx88d6be8927b26f36"
  # TEST_KFID="kfc24d79d61f7da4981"
  # TEST_CORPID="wwb612f4203c7ac868"

  PRO_BASEURL="https://www.welshine.shop/applet"
  PRO_APPID="wx898834a20ceeb879"
  PRO_KFID="kfcd271a202428d5edf"
  PRO_CORPID="ww0bcc1f28fd587bdd"

  # nvm use 16.16.0
  sed -i -r "s|const baseUrl = \"($DEV_BASEURL\|$TEST_BASEURL\|$PRO_BASEURL)\"|const baseUrl = \"$DEV_BASEURL\"|" ./src/apis/config.ts
  sed -i -r "s|const kfid = \"($DEV_KFID\|$PRO_KFID)\"|const kfid = \"$DEV_KFID\"|" ./src/apis/config.ts
  sed -i -r "s|const corpId = \"($DEV_CORPID\|$PRO_CORPID)\"|const corpId = \"$DEV_CORPID\"|" ./src/apis/config.ts
  sed -i -r "s|\"appid\": \"($DEV_APPID\|$TEST_APPID\|$PRO_APPID)\"|\"appid\": \"$DEV_APPID\"|" ./src/manifest.json
  npm run build:mp-weixin
  cd ./dist/build
  rm -rf *.tar
  local name="电商小程序 开发 "`date '+%m.%d %H.%M'`".tar"
  tar -cf "$name" mp-weixin
  sed -i -r  "s|$DEV_BASEURL|$TEST_BASEURL|"  ./mp-weixin/apis/config.js
  sed -i -r  "s|$DEV_APPID|$TEST_APPID|"  ./mp-weixin/project.config.json
  name="电商小程序 测试 "`date '+%m.%d %H.%M'`".tar"
  tar -cf "$name" mp-weixin
  sed -i -r  "s|$TEST_BASEURL|$PRO_BASEURL|"  ./mp-weixin/apis/config.js
  sed -i -r  "s|$DEV_KFID|$PRO_KFID|"  ./mp-weixin/apis/config.js
  sed -i -r  "s|$DEV_CORPID|$PRO_CORPID|"  ./mp-weixin/apis/config.js
  sed -i -r  "s|$TEST_APPID|$PRO_APPID|"  ./mp-weixin/project.config.json
  name="电商小程序 正式 "`date '+%m.%d %H.%M'`".tar"
  tar -cf "$name" mp-weixin
}

bundle
