<template>
  <scroll-view
    v-if="pageHeight"
    scroll-y
    :style="{
      height: `${
        propsStatus === '可开票订单' ? pageHeight - 100 : pageHeight
      }px`
    }"
    enable-back-to-top
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="refresh"
    @scrolltolower="getPendingList"
  >
    <view class="page-padding-top" />
    <template v-if="!refreshing">
      <v-loading-block v-if="loading" />
      <v-empty
        v-else-if="
          status === 'nomore' &&
            propsStatus === '可开票订单' &&
            pageStore.notIssueInvoiceList.length === 0
        "
        src="/static/image/empty-order.png"
      >
        暂无发票单
      </v-empty>
      <v-empty
        v-else-if="
          status === 'nomore' &&
            propsStatus !== '可开票订单' &&
            invoiceList.length === 0
        "
        src="/static/image/empty-order.png"
      >
        暂无发票单
      </v-empty>
      <template v-else-if="propsStatus === '可开票订单'">
        <notIssueInvoice
          v-for="item in pageStore.notIssueInvoiceList"
          :key="item.orderNo"
          :data="item"
          @refresh="initInvoiceList"
        />
        <v-loadmore v-if="!refreshing" :status="status" />
      </template>
      <template v-else-if="propsStatus === '申请中'">
        <awitInvoice
          v-for="item in invoiceList"
          :key="item.id"
          :data="item"
          @refresh="initInvoiceList"
        />
        <v-loadmore v-if="!refreshing" :status="status" />
      </template>
      <template v-else-if="propsStatus === '已开票'">
        <InvoiceIssued
          v-for="item in invoiceList"
          :key="item.id"
          :data="item"
          @refresh="initInvoiceList"
        />
        <v-loadmore v-if="!refreshing" :status="status" />
      </template>
    </template>
    <view class="page-padding-bottom" />
  </scroll-view>
</template>

<script setup lang="ts">
import notIssueInvoice from "./not-issue-invoice.vue"
import InvoiceIssued from "./Invoice-issued.vue"
import awitInvoice from "./awit-invoice.vue"
import { num2str, str2num } from "@/utils/type-transfer"

const props = withDefaults(
  defineProps<{
    pageHeight: number
    status: string
  }>(),
  {
    status: undefined,
    pageHeight: undefined
  }
)

// const notIssueInvoiceList = ref<notIssueInvoice[]>([])
const invoiceList = ref<InvoiceItem[]>([])
const pageStore = $store.page()
/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = () => {
  refreshing.value = true
  pageIndex.value = 1
  // notIssueInvoiceList.value = []
  pageStore.setNotIssueInvoiceList([])
  invoiceList.value = []
  status.value = "loadmore"
  getPendingList()
}
const initInvoiceList = async () => {
  loading.value = true
  pageIndex.value = 1
  // notIssueInvoiceList.value = []
  pageStore.setNotIssueInvoiceList([])
  invoiceList.value = []
  status.value = "loadmore"
  await getPendingList()
  loading.value = false
}

/** 分页数据 */
const pageIndex = ref(1)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")
const propsStatus = toRef(props, "status")
const getPendingList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  if (propsStatus.value === "可开票订单") {
    const response = await $api.orderListByInvoice({
      orderField: "orderId",
      requestParams: {
        handleStatusRequests: [
          {
            handleStatus: 2
          }
        ],
        payStatus: 1
      },
      pageSize: "5",
      pageIndex: num2str(pageIndex.value),
      sortType: "desc"
    })

    switch (response.__error) {
      case undefined: {
        pageIndex.value += 1
        const list: NotIssueInvoice[] =
          response.data.result?.map?.((order) => ({
            orderId: order.id,
            orderNo: order.orderNo,
            invoiceNo: order.invoiceNo,
            orderStatus: order.handleStatus,
            orderPrice: str2num(order.sumPrice),
            afterSaleStatus: order.afterSaleStatus,
            shouldPayPrice: str2num(order.shouldPayCost),
            actualPayPrice: str2num(order.actualPayCost),
            orderTime: order.postDate,
            payStatus: order.payStatus,
            orderAfterSaleId: order.orderAfterSaleId,
            payTime: order.paymentTime,
            selected: false,
            receiveTime: order.receiveTime,
            deliveryType: order.deliveryType,
            productList: order.orderProductRelationVOList.map((product) => {
              const item: ProductOrderItem = {
                id: product.productInfoId,
                image: product.productInfoProductImg,
                name: product.productInfoName,
                unitPrice: str2num(product.productInfoPrice),
                unitAmount: str2num(product.productInfoPackage),
                displayPrice: str2num(product.displayUnitPrice),
                displayAmount: product.displayPackage,
                number: str2num(product.quantity),
                productInfoSpec: product.productInfoSpec,
                totalPackage: product.totalPackage
              }
              return item
            })
          })) ?? []
        pageStore.setNotIssueInvoiceList([
          ...pageStore.notIssueInvoiceList,
          ...list
        ])
        const totalCount = str2num(response.data.totalCount)
        status.value =
          pageStore.notIssueInvoiceList.length < totalCount
            ? "loadmore"
            : "nomore"
        break
      }
      default:
        status.value = "loadmore"
    }
  } else {
    const response = await $api.findOrderInvoiceList({
      orderField: "updateTime",
      pageSize: "5",
      pageIndex: num2str(pageIndex.value),
      requestParams: {
        invoiceStatus: propsStatus.value === "申请中" ? 0 : 1
      },
      sortType: "desc"
    })
    switch (response.__error) {
      case undefined: {
        const list: InvoiceItem[] =
          response.data.result.map((item) => ({
            applyInvoiceDate: item.applyInvoiceDate,
            dutyParagraph: item.dutyParagraph,
            id: item.id,
            invoiceHeader: item.invoiceHeader,
            invoiceHeaderType: item.invoiceHeaderType,
            invoiceNo: item.invoiceNo,
            invoicePrice: str2num(item.invoicePrice),
            invoiceStatus: item.invoiceStatus,
            invoiceType: item.invoiceType,
            orderNumber: item.orderNumber
          })) ?? []
        invoiceList.value = [...invoiceList.value, ...list]
        const totalCount = str2num(response.data.totalCount)
        status.value =
          invoiceList.value.length < totalCount ? "loadmore" : "nomore"
        break
      }
    }
  }
  await nextTick()
  refreshing.value = false
}

defineExpose({ initInvoiceList })
</script>
