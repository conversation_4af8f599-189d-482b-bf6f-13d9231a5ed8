<template>
  <view>
    <view class="page-header">
      <v-navbar title="退款查询" back />
    </view>
    <view class="v-block">
      <view class="step-box">
        <view class="step-item">
          <v-icon
            size="32rpx"
            src="/static/icons/components/checkbox-checked.png"
          />
          <view class="step-info-msg">
            已提交
          </view>
        </view>
        <view class="step-line" />
        <view class="step-item">
          <v-icon
            size="32rpx"
            src="/static/icons/components/checkbox-checked.png"
          />
          <view class="step-info-msg">
            处理中
          </view>
        </view>
        <view v-if="order?.payStatus === '3'" class="step-line" />
        <view v-if="order?.payStatus === '3'" class="step-item">
          <v-icon
            size="32rpx"
            src="/static/icons/components/checkbox-checked.png"
          />
          <view class="step-info-msg">
            退款成功
          </view>
        </view>
      </view>
      <view class="divider time-line-margin-adjust" />
      <v-timeline :data="timeLine" />
    </view>

    <view class="v-block">
      <view class="info-item text-bold">
        基本信息
      </view>
      <view class="v-form-item flex-center-between">
        <view class="text-bold label gray">
          联系方式
        </view>
        <view class="text-32 red">
          {{ encryptedPhone }}
        </view>
      </view>
      <view class="divider" />
      <view class="v-form-item flex-center-between">
        <view class="text-bold label gray">
          订单编号
        </view>
        <view class="text-32">
          {{ orderNo }}
        </view>
      </view>
    </view>

    <view class="flex-1 contact">
      <!-- <v-button
        height="94rpx"
        background-color="#005BAC"
        @click="customerService"
      >
        <v-icon size="44rpx" src="/static/icons/order/customer-service.png" />
        &nbsp;
        <view class="contact-msg">
          在线客服
        </view>
      </v-button> -->
      <v-button
        height="94rpx"
        background-color="#005BAC"
        open-type="contact"
        send-message-title="订单详情"
        :send-message-path="`/package-order/order/order-details.html?orderno=${orderNo}&customer=3`"
        :send-message-img="logoSrc"
        show-message-card
      >
        <v-icon size="44rpx" src="/static/icons/order/customer-service.png" />
        &nbsp;
        <view class="contact-msg">
          在线客服
        </view>
      </v-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { str2num } from "@/utils/type-transfer"
import { formateUtc } from "@/utils/time"
import type { TimeLine } from "@/components/v-timeline/types"
const orderNo = ref("")
const order = ref<OrderDetails>()
const userPhone = ref<string | undefined>()

const { safeNavigateBack } = usePageLayout()

const getOrderDetails = async () => {
  const response = await $api.orderDetail({ orderNo: orderNo.value })
  switch (response.__error) {
    case undefined: {
      const data = response.data
      order.value = {
        orderId: data.id,
        orderNo: data.orderNo,
        invoiceNo: data.invoiceNo,
        orderStatus: data.handleStatus,
        orderPrice: str2num(data.sumPrice),
        shouldPayPrice: str2num(data.shouldPayCost),
        actualPayPrice: str2num(data.actualPayCost),
        discountAmount: str2num(data.discountAmount),
        orderTime: data.postDate,
        payTime: data.paymentTime,
        payDeadline: data.postDateEnd,
        afterSaleStatus: data.afterSaleStatus,
        completeTime: data.receiveTime,
        orderAfterSaleId: data.orderAfterSaleId,
        payStatus: data.payStatus,
        payType: data.payType,
        cancelTime: data.cancelTime,
        refundTime: data.refundTime,
        productList: data.orderProductRelationVOList.map((product) => {
          const item: ProductOrderItem = {
            id: product.productInfoId,
            image: product.productInfoProductImg,
            name: product.productInfoName,
            unitPrice: str2num(product.productInfoPrice),
            unitAmount: str2num(product.productInfoPackage),
            displayPrice: str2num(product.displayUnitPrice),
            displayAmount: product.displayPackage,
            number: str2num(product.quantity),
            productInfoSpec: product.productInfoSpec,
            totalPackage: product.totalPackage
          }
          return item
        }),
        address: data.consigneeAddress,
        mobile: data.consigneeMobile,
        name: data.consigneeName,
        deliveryCode: data.deliveryCode,
        deliveryName: data.deliveryName,
        deliveryNo: data.deliveryNo,
        deliveryType: data.deliveryType,
        remark: data.remark
      }
      userPhone.value = order.value.mobile
      break
    }
    default:
      safeNavigateBack()
  }
}
const encryptedPhone = computed(() => {
  if (!userPhone.value) return ""
  return userPhone.value.slice(0, 3) + "****" + userPhone.value.slice(-4)
})

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (!query?.orderno) {
    safeNavigateBack()
  } else {
    orderNo.value = query.orderno
    getOrderDetails()
  }
})
const timeLine = computed<TimeLine>(() => {
  const list = [] as TimeLine
  if (!order.value) return list
  const data = order.value
  const status = data.payStatus

  list.push({
    title: "您的取消订单申请已提交。",
    content: formateUtc(order.value.cancelTime) as string,
    time: undefined
  })
  list.push({
    title: "系统处理中，请客耐心等待。",
    content: formateUtc(order.value.cancelTime) as string,
    time: undefined
  })
  if (status === "3") {
    list.push({
      title: "退款已完成，款项已原路返回您的支付账户。",
      content: formateUtc(order.value.refundTime) as string,
      time: undefined
    })
  }
  return list
})

// const customerService = () => {
//   if (!orderNo.value) return
//   uni.openCustomerServiceChat({
//     extInfo: { url: $api.config.customerUrl },
//     corpId: $api.config.corpId,
//     showMessageCard: true,
//     sendMessageTitle: "订单详情",
//     sendMessagePath: `/package-order/order/order-details.html?orderno=${orderNo.value}&customer=3`,
//     sendMessageImg: "/static/image/service-logo.png",
//     fail: (err) => console.log(err)
//   })
// }

import { webName } from '@/apis/config' 

const logoSrc = computed(() => {
  if (webName === 'cheerray') {
    return "/static/image/service-logo-cheerray.png"
  } else {
    return "/static/image/service-logo.png"
  }
})
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 32rpx;
}
.gray {
  color: #999999;
}
.step-box {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 32rpx 0;
}
.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.step-line {
  width: 120rpx;
  height: 2rpx;
  background: #005bac;
  margin: 0 30rpx;
}
.step-info-msg {
  font-size: 22rpx;
  color: #005bac;
  font-weight: bold;
}
.time-line-margin-adjust {
  margin-bottom: 40rpx;
}
.contact {
  margin: 0 100rpx;
  .contact-msg {
    color: white;
  }
}
</style>
