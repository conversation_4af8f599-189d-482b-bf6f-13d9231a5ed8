<template>
  <view>
    <view class="page-header hide-shadow flex--center flex-col">
      <view class="background">
        <image class="background-image" src="@/package-points/static/image/points-center.png" />
      </view>
      <v-navbar
        type="white"
        title="我的积分"
        back
        background-color="transparent"
      />
      <view class="points-container">
        <view class="not-received">
          {{
            `未到账积分:${
              outstandingPoints === undefined ? "-" : outstandingPoints
            }`
          }}
        </view>
        <view class="points-value">
          {{ availablePoints === undefined ? "-" : availablePoints }}
        </view>
        <view class="points-rule flex-center" @click="navigateToService">
          <view class="text">
            积分规则
          </view>
          <v-icon size="17rpx" src="/static/icons/points/right-arrow.png" />
        </view>
      </view>
      <view class="tab-container">
        <view class="tab-container-backgound" />
        <v-tabs
          v-model="tabIndex"
          :list="['积分收支明细', '积分兑换记录']"
          height="80rpx"
          text-style="color: #999"
          active-text-style="color:#333;font-weight: bold"
        >
          <view class="v-tab-block" style="transform: translateX(0)" />
        </v-tabs>
      </view>
    </view>
    <swiper
      :current="tabIndex"
      :style="{ height: `${pageHeight}px` }"
      @change="tabSwiperChange"
      @transition="wxs.transition"
      @animationfinish="wxs.transitionFinish"
    >
      <swiper-item>
        <points-record-list
          ref="pointsRecordListRef"
          :page-height="pageHeight"
        />
      </swiper-item>
      <swiper-item>
        <exchange-record-list
          ref="exchangeRecordListRef"
          :page-height="pageHeight"
        />
      </swiper-item>
    </swiper>
  </view>
</template>

<script lang="ts">
import pointsRecordList from "./modules/points-record-list.vue"
import exchangeRecordList from "./modules/exchange-record-list.vue"
import wxs from "@/utils/wxs"

export default {
  components: {
    pointsRecordList,
    exchangeRecordList
  },
  data () {
    return {
      pageHeight: 0,
      availablePoints: undefined as number | undefined,
      outstandingPoints: undefined as number | undefined,
      tabIndex: 0,
      initList: [false, false],
      wxs: {
        transition: wxs.transition,
        transitionFinish: wxs.transitionFinish
      }
    }
  },
  onLoad (query: Record<string, string>) {
    const index = parseInt(query.index)
    if (!isNaN(index)) this.tabIndex = index
  },
  onShow () {
    this.getUserInfo()
    this.refreshAll()
  },
  onReady () {
    this.refreshPageHeight()
  },
  methods: {
    async getUserInfo () {
      const response = await $api.findMemberInfoWeChat({})
      switch (response.__error) {
        case undefined:
          this.availablePoints = parseInt(response.data.availablePoints)
          this.outstandingPoints = parseInt(response.data.outstandingPoints)
      }
    },
    refreshAll () {
      for (let i = 0; i < this.initList.length; i += 1) {
        this.initList[i] = false
      }
      this.initPointsList(this.tabIndex)
    },
    tabSwiperChange (event: SwiperChangeEvent) {
      this.changeTabIndex(event.detail.current)
    },
    changeTabIndex (index: number) {
      this.tabIndex = index
      this.initPointsList(index)
    },
    initPointsList (index: number) {
      switch (index) {
        case 0: {
          if (!this.$refs.pointsRecordListRef) return
          if (this.initList[0]) return
          const ref = this.$refs.pointsRecordListRef as InstanceType<
            typeof pointsRecordList
          >
          if (!ref) return
          this.initList[0] = true
          ref.initPointsRecordList()
          break
        }
        case 1: {
          if (!this.$refs.exchangeRecordListRef) return
          if (this.initList[1]) return
          const ref = this.$refs.exchangeRecordListRef as InstanceType<
            typeof exchangeRecordList
          >
          if (!ref) return
          this.initList[1] = true
          ref.initExchangeRecordList()
          break
        }
      }
    },
    async refreshPageHeight () {
      const system = uni.getWindowInfo()
      let screenHeight
      // #ifdef MP-WEIXIN
      screenHeight = system.screenHeight
      // #endif
      // #ifdef MP-ALIPAY
      screenHeight = system.windowHeight
      // #endif
      // #ifdef H5
      screenHeight = system.screenHeight
      // #endif
      const query = uni.createSelectorQuery()
      const headerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-header")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      const footerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-footer")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      this.pageHeight = screenHeight - headerHeight - footerHeight
    },
    navigateToService () {
      uni.navigateTo({
        url: "/package-user/user/customer-service-detail?type=8"
      })
    }
  }
}
</script>

<script
  src="../../components/v-tabs/v-tabs.wxs"
  module="wxs"
  lang="wxs"
></script>

<style lang="scss" scoped>
.page-header {
  position: relative;
  height: 490rpx;
  padding-bottom: 15rpx;
  .background {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 470rpx;
    z-index: -2;
    .background-image {
      height: 470rpx;
      width: 750rpx;
    }
  }
  .points-container {
    flex: 1;
    padding: 30rpx 47rpx 0;
    color: white;
    .not-received {
      margin-bottom: 10rpx;
    }
    .points-value {
      margin-bottom: 10rpx;
      font-size: 72rpx;
      text-shadow: 0 6rpx #d95b00;
      font-weight: bold;
    }
    .points-rule {
      .text {
        font-size: 24rpx;
        margin-right: 10rpx;
      }
    }
  }
  .tab-container {
    position: relative;
    height: 80rpx;
    .tab-container-backgound {
      position: absolute;
      top: 0;
      left: 24rpx;
      right: 24rpx;
      height: 100%;
      border-radius: 20rpx;
      background-color: white;
      z-index: -1;
    }
  }
}
.v-tab-block {
  height: 6rpx;
  width: 40rpx;
  margin: 0 auto;
  background-color: #ff6300;
}
.page-footer-content {
  height: 80rpx;
  padding-top: 20rpx;
  .divider-vertical {
    height: 40rpx;
  }
}
</style>
