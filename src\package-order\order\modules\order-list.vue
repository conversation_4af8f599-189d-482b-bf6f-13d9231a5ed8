<template>
  <scroll-view
    v-if="pageHeight"
    scroll-y
    :style="{ height: `${pageHeight}px` }"
    enable-back-to-top
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="refresh"
    @scrolltolower="getOrderList"
  >
    <view class="page-padding-top" />
    <template v-if="!refreshing">
      <v-loading-block v-if="loading" />
      <v-empty
        v-else-if="status === 'nomore' && orderList.length === 0"
        src="/static/image/empty-order.png"
      >
        暂无订单
      </v-empty>
      <template v-else>
        <order-item
          v-for="order in orderList"
          :key="order.orderId"
          :order="order"
          @refresh="$emit('refresh')"
          @pay="$emit('pay')"
          @paid="$emit('paid')"
        />
        <v-loadmore v-if="!refreshing" :status="status" />
      </template>
    </template>
    <view class="page-padding-bottom" />
  </scroll-view>
</template>

<script setup lang="ts">
import OrderItem from "./order-item.vue"
import { str2num } from "@/utils/type-transfer"

const props = defineProps<{
  index: number
  pageHeight: number
}>()

interface Emits {
  (event: "refresh"): void
  (event: "pay"): void
  (event: "paid"): void
}
defineEmits<Emits>()

const orderList = ref<OrderItem[]>([])

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = () => {
  refreshing.value = true
  pageIndex.value = 1
  orderList.value = []
  status.value = "loadmore"
  getOrderList()
}
const initOrderList = async () => {
  loading.value = true
  pageIndex.value = 1
  orderList.value = []
  status.value = "loadmore"
  await getOrderList()
  loading.value = false
}

/** 订单状态 */
const index = toRef(props, "index")
const orderStatus = computed(() => {
  switch (index.value) {
    case 0:
      return undefined
    case 1:
      return [{ handleStatus: 4 }]
    case 2:
      return [{ handleStatus: 0 }, { handleStatus: 4 }]
    case 3:
      return [{ handleStatus: 1 }]
    case 4:
      return [{ handleStatus: 2 }]
    case 5:
      return [{ handleStatus: 3 }, { handleStatus: 5 }]
    default:
      return undefined
  }
})
/** 支付状态 */
const payStatus = computed(() => {
  switch (index.value) {
    case 0:
      return undefined
    case 1:
      return 0
    case 2:
      return 1
    case 3:
      return 1
    case 4:
      return 1
    case 5:
      return undefined
    default:
      return undefined
  }
})
/** 分页数据 */
const pageIndex = ref(1)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")

/** 获取订单列表 */
const getOrderList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  const response = await $api.orderListByWeChat({
    orderField: "createdTime",
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    sortType: "desc",
    requestParams: {
      handleStatusRequests: orderStatus.value,
      payStatus: payStatus.value
    }
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      const list: OrderItem[] =
        response.data.result?.map?.((order) => ({
          orderId: order.id,
          orderNo: order.orderNo,
          invoiceNo: order.invoiceNo,
          orderStatus: order.handleStatus,
          orderPrice: str2num(order.sumPrice),
          shouldPayPrice: str2num(order.shouldPayCost),
          actualPayPrice: str2num(order.actualPayCost),
          orderTime: order.postDate,
          payStatus: order.payStatus,
          orderAfterSaleId: order.orderAfterSaleId,
          afterSaleStatus: order.afterSaleStatus,
          payTime: order.paymentTime,
          receiveTime: order.receiveTime,
          ableAfterSale: order.isLaunchAfterSale === 1,
          payTransactionId: order.payTransactionId,
          deliveryType: order.deliveryType,
          productList: order.orderProductRelationVOList.map((product) => {
            const item: ProductOrderItem = {
              id: product.productInfoId,
              image: product.productInfoProductImg,
              name: product.productInfoName,
              unitPrice: str2num(product.productInfoPrice),
              unitAmount: str2num(product.productInfoPackage),
              displayPrice: str2num(product.displayUnitPrice),
              displayAmount: product.displayPackage,
              number: str2num(product.quantity),
              productInfoSpec: product.productInfoSpec,
              totalPackage: product.totalPackage
            }
            return item
          })
        })) ?? []
      orderList.value = [...orderList.value, ...list]
      const totalCount = str2num(response.data.totalCount)
      status.value = orderList.value.length < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
}

defineExpose({ initOrderList })
</script>

<style lang="scss" scoped></style>
