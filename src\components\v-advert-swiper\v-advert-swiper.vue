<template>
  <view class="v-advert-container">
    <view v-if="loading" class="placeholder" />
    <template v-else-if="imageList.length > 1">
      <v-swiper
        :list="imageList"
        src-key="img"
        type="dot"
        :height="height"
        :width="width"
        autoplay
        circular
        @click="navigate"
      />
    </template>
    <template v-else-if="imageList.length === 1">
      <v-image
        :src="imageList[0].img"
        :height="height"
        :width="width"
        mode="aspectFit"
        @click="navigate(imageList[0])"
      />
    </template>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{ space: number; height: string; width: string }>()
const space = toRef(props, "space")

const loading = ref(true)
const duration = ref(3)
const imageList = ref<
{
  img: string
  url: string
}[]
>([])

const getAdvertising = async () => {
  if (!loading.value) return
  const response = await $api.getAdvertising({ advertisingSpace: space.value })
  switch (response.__error) {
    case undefined:
      imageList.value = response.data.advertisingDetailList
        ?.filter((item) => item.isEnable === 0)
        .map((item) => ({
          img: item.imgUrl,
          url: item.url
        })) ?? []
      duration.value = response.data.duration ?? 5
      loading.value = false
  }
}

const navigate = (item: Record<string, unknown>) => {
  if (!item.url) return
  switch (true) {
    case /^\/pages\/index\/index-page/.test(item.url as string):
    case /^\/pages\/product\/product-page/.test(item.url as string):
    case /^\/pages\/cart\/cart-page/.test(item.url as string):
    case /^\/pages\/user\/user-page/.test(item.url as string):
      uni.switchTab({
        url: item.url as string,
        fail: () => {
          console.warn(item.url, "不是一个合法的页面路径")
        }
      })
      break
    default:
      uni.navigateTo({
        url: item.url as string,
        fail: () => {
          console.warn(item.url, "不是一个合法的页面路径")
        }
      })
  }
}

onShow(() => getAdvertising())
</script>

<style lang="scss" scoped></style>
