<template>
  <view>
    <view class="page-header">
      <v-navbar title="建议反馈" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="block">
        <view class="title flex-center text-bold">
          反馈类型
        </view>
        <view class="divider" />
        <view class="flex-center flex-1 content">
          <view class="flex-center" @click="feedbackForm.feedbackType = 0">
            <v-checkbox :checked="feedbackForm.feedbackType === 0" />
            <view style="margin-left: 10rpx">
              功能异常
            </view>
          </view>
          <view
            class="flex-center"
            style="margin-left: 40rpx"
            @click="feedbackForm.feedbackType = 1"
          >
            <v-checkbox :checked="feedbackForm.feedbackType === 1" />
            <view style="margin-left: 10rpx">
              优化建议
            </view>
          </view>
        </view>
      </view>
      <view class="block">
        <view class="title flex-center text-bold">
          问题或建议
        </view>
        <view class="divider" />
        <view class="content">
          <v-textarea
            v-model="feedbackForm.remark"
            maxlength="100"
            custom-style="height: 100rpx; width: auto"
            :cursor-spacing="75"
            placeholder="任何功能的使用问题和体验建议，都期望得到您的反馈。我们会尽快优化提升。"
          />
        </view>
      </view>
      <view class="block">
        <view class="title flex-center">
          <text class="text-bold">
            添加图片
          </text>
          <text style="margin-left: 10rpx">
            (非必填)
          </text>
        </view>
        <view class="divider" />
        <view class="content">
          <v-uploader @change="changeImageList" />
        </view>
      </view>
      <view class="block">
        <view class="title flex-center">
          <text class="text-bold">
            联系方式
          </text>
          <text style="margin-left: 10rpx">
            (非必填)
          </text>
        </view>
        <view class="divider" />
        <view class="content">
          <input
            v-model="feedbackForm.tel"
            class="input"
            placeholder-class="placeholder"
            maxlength="13"
            custom-style="height: 100rpx; width: auto"
            placeholder="留下您的手机号，方便联系沟通。"
          >
        </view>
      </view>
      <view class="button-list-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        :disabled="feedbackForm.remark === ''"
        height="94rpx"
        font-size="32rpx"
        type="primary"
        @click="submit"
      >
        提交
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import type { UploaderItem } from "@/components/v-uploader/modules/types"
import { str2num } from "@/utils/type-transfer"
const { safeNavigateBack, pageHeight } = usePageLayout()
const feedbackForm = ref<{
  feedbackType: number
  imgIds: number[]
  remark: string
  tel: string
}>({
  feedbackType: 0,
  imgIds: [],
  remark: "",
  tel: ""
})
const validate = () => {
  switch (true) {
    case feedbackForm.value.tel.length > 13:
      uni.showToast({
        title: "电话号码最大长度为13位",
        icon: "none"
      })
      return false
    default:
      return true
  }
}

const changeImageList = (list: UploaderItem[]) => {
  feedbackForm.value.imgIds = list
    .filter((item) => item.status === "success")
    .map((item) => str2num(item.id))
}
const submit = async () => {
  if (!validate()) return
  const response = await $api.addSysOpinionFeedback(feedbackForm.value)
  switch (response.__error) {
    case undefined: {
      await uni.showModal({ title: "提交成功", showCancel: false })
      safeNavigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.block {
  margin-bottom: 20rpx;
  padding: 0 24rpx;
  background-color: white;
}
.submit-button {
  position: fixed;
  padding: 20rpx 24rpx 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 100;
}
.button-list-blank {
  height: 114rpx;
}
.title {
  min-height: 90rpx;
}
.content {
  min-height: 110rpx;
  padding: 20rpx;
  box-sizing: border-box;
}
.under-divider-box-style {
  margin-top: 32rpx;
  margin-top: 32rpx;
}
.input {
  background-color: #F5F5F5;
  padding: 12rpx;
  border-radius: 16rpx;
}
</style>
