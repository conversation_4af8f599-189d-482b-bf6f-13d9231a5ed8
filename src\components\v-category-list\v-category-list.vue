<template>
  <view class="v-category-list" :list="list" :change:list="wxs.listChange">
    <view class="v-category-item-container flex flex-warp">
      <view
        v-for="(category, index) in list"
        :key="index"
        class="v-category-item"
      >
        <v-tag color="#005bac" background-color="#e5eef7">
          {{ category }}
        </v-tag>
      </view>
      <view class="v-category-ellipsis" style="opacity: 0">
        <v-tag color="#005bac" background-color="#e5eef7">
          ...
        </v-tag>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import wxs from "@/utils/wxs"
import type { PropType } from "vue"

export default {
  props: {
    list: {
      type: Array as PropType<string[]>,
      default: () => [] as string[]
    }
  },
  data () {
    return {
      wxs: {
        listChange: wxs.listChange
      }
    }
  }
}
</script>

<script src="./v-category-list.wxs" module="wxs" lang="wxs"></script>

<style lang="scss" scoped>
.v-category-item {
  padding-right: 8rpx;
}
</style>
