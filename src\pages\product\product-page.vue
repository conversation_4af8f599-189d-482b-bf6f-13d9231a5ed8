<template>
  <view>
    <view class="page-header">
      <v-navbar>
        <view class="search-input-container flex-1">
          <view
            class="search-input flex-center-center"
            :style="'height: ' + menuButtonBounding.height + 'px'"
            @click="navigateToSearchInput"
          >
            <v-icon
              size="32rpx"
              margin-right="8rpx"
              src="/static/icons/search.png"
            />
            <view class="placeholder">
              请输入商品名称
            </view>
          </view>
        </view>
      </v-navbar>
      <view class="category-filter">
        <scroll-view
          scroll-x
          :scroll-into-view="scrollId"
          scroll-with-animation
          class="hide-scroll-bar"
        >
          <view class="filter-list flex-center">
            <view
              v-for="item in firstCategoryList"
              :id="`filter-item-${item.id}`"
              :key="item.id"
              class="filter-item flex-col flex-center-evenly"
              :class="{ active: firstCategory && item.id === firstCategory.id }"
              @click="() => changeFirstCategory(item.id)"
            >
              <v-image
                height="106rpx"
                width="106rpx"
                :src="item.img"
                mode="aspectFill"
              />
              <view class="filter-label text-24 text-bold text-1-row">
                {{ item.name }}
              </view>
            </view>
          </view>
        </scroll-view>
        <view
          class="expand-button flex-col flex-center-center"
          @click="expandHandle"
        >
          <view class="text">
            全部
          </view>
          <v-icon size="26rpx" src="/static/icons/product/menu.png" />
        </view>
      </view>
      <view class="gap" />
    </view>
    <view class="flex">
      <scroll-view
        scroll-y
        class="filter-scroller hide-scroll-bar"
        :style="{ height: `${pageHeight}px` }"
      >
        <view class="filter-list">
          <view
            v-for="item in secondCategoryList"
            :key="item.id"
            class="filter-item flex-center"
            :class="{ active: secondCategory && secondCategory.id === item.id }"
            @click="() => changeSecondCategory(item.id)"
          >
            <view class="text-1-row">
              {{ item.name }}
            </view>
          </view>
        </view>
      </scroll-view>
      <scroll-view
        v-if="pageHeight"
        scroll-y
        :scroll-top="scrollTop"
        class="product-scroller background-white"
        :style="{ height: `${pageHeight}px` }"
        hide-scroll-bar
        enable-back-to-top
        :refresher-enabled="secondCategory !== undefined"
        :refresher-triggered="status === 'refreshing'"
        @refresherrefresh="refresh"
        @scroll="scrollHandle"
      >
        <view v-for="item in thirdCategoryList" :key="item.categoryId">
          <product-group
            :category-id="item.categoryId"
            :category-name="item.categoryName"
            :product-list="item.children"
            @select="showSelect"
          />
        </view>
      </scroll-view>
    </view>
    <view class="page-footer">
      <v-tabbar :index="1" />
    </view>
    <v-product-select ref="selectRef" />
    <v-popover v-model="isExpand" mode="top" :top="expandContainerTop">
      <view>
        <view class="filter-expand-list flex flex-warp">
          <view
            v-for="item in firstCategoryList"
            :key="item.id"
            class="filter-item flex-col flex-center-evenly"
            :class="{ active: firstCategory && item.id === firstCategory.id }"
            @click="() => changeFirstCategory(item.id, true)"
          >
            <v-image
              height="106rpx"
              width="106rpx"
              :src="item.img"
              mode="aspectFill"
            />
            <view class="filter-label text-24 text-bold text-1-row">
              {{ item.name }}
            </view>
          </view>
        </view>
        <view
          class="collapse-button flex-center-center"
          @click="collapseHandle"
        >
          <v-icon
            size="28rpx"
            src="/static/icons/product/collapse-button-icon.png"
          />
          <view class="text text-24 text-sub">
            点击收起
          </view>
        </view>
      </view>
    </v-popover>
    <v-toast />
  </view>
</template>

<script lang="ts" setup>
import ProductGroup from "./modules/product-group.vue"

const { pageHeight } = usePageLayout()
const { navBarHeight, statusBarHeight, menuButtonBounding } = useNavbarLayout()

const navigateToSearchInput = () => {
  uni.navigateTo({ url: "/pages/product/product-search-input-page" })
}

/** 一级分类展开 */
const isExpand = ref(false)
const expandContainerTop = computed(() => {
  const navHeight = navBarHeight.value + statusBarHeight.value
  return `${navHeight}px`
})
const expandHandle = () => {
  isExpand.value = true
}
const collapseHandle = () => {
  isExpand.value = false
}

const pageStore = $store.page()
/** 一级分类 */
const firstCategory = computed(() => pageStore.firstCategory)
const firstCategoryList = computed(() => pageStore.firstCategoryList)
const changeFirstCategory = (id: number, scroll?: boolean) => {
  pageStore.changeFirstCategory(id)
  isExpand.value = false
  scrollToTop()
  if (scroll) scrollToFilterItem(id)
}

/** 二级分类 */
const secondCategory = computed(() => pageStore.secondCategory)
const secondCategoryList = computed(() => pageStore.secondCategoryList)
const changeSecondCategory = (id: number) => {
  pageStore.changeSecondCategory(id)
  scrollToTop()
}

/** 三级分类 */
const thirdCategoryList = computed(() => pageStore.thirdCategoryList)
onShow(() => pageStore.getCategoryList())

/** 选择商品 */
const selectRef = ref()
const showSelect = (product: ProductBlock) => {
  selectRef.value.showSelect({ id: product.id })
}

/** 刷新状态 */
const status = computed(() => {
  return pageStore.thirdCategoryStatus
})
const refresh = async () => {
  pageStore.refreshThirdCategoryList()
}

/** 控制一级滚动位置 */
const scrollId = ref("")
const scrollToFilterItem = async (id?: number) => {
  scrollId.value = ""
  await new Promise((resolve) => nextTick(() => resolve(true)))
  scrollId.value = `filter-item-${id}`
}
onShow(() => {
  scrollToFilterItem(firstCategory.value?.id)
})

/** 控制右边滚动位置 */
let currentOffset = 0
const scrollTop = ref(0)
const scrollToTop = async () => {
  scrollTop.value = currentOffset
  await new Promise((resolve) => nextTick(() => resolve(true)))
  scrollTop.value = 0
}
const scrollHandle = (event: ScrollEvent) => {
  currentOffset = event.detail.scrollTop
}
</script>

<style lang="scss" scoped>
.search-input-container {
  padding: 0 24rpx;
  .search-input {
    background-color: #f5f5f5;
    border-radius: 100vh;
    padding: 0 8rpx;
    height: 70rpx;
    box-sizing: border-box;
  }
}
.category-filter {
  position: relative;
  padding: 24rpx 0 4rpx;
  background-color: white;
  .filter-list {
    min-height: 180rpx;
    padding-left: 14rpx;
    padding-right: 80rpx;
    width: fit-content;
    .filter-item {
      padding: 0 10rpx 10rpx;
      height: 150rpx;
      width: 106rpx;
      opacity: 0.5;
      transition: opacity 0.2s linear, color 0.2s linear;
      &.active {
        opacity: 1;
        color: #005bac;
      }
    }
  }
  .expand-button {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 68rpx;
    box-shadow: -8px 0 6px -6px rgba(0, 0, 0, 0.15);
    background-color: white;
    .text {
      white-space: nowrap;
      writing-mode: vertical-lr;
      margin-bottom: 6rpx;
    }
  }
}
.gap {
  height: 8rpx;
}
.filter-expand-list {
  background-color: white;
  padding: 24rpx 0 14rpx;
  .filter-item {
    margin: 0 0 20rpx;
    width: 20vw;
    height: 150rpx;
    box-sizing: border-box;
    padding: 0 10rpx;
  }
}
.collapse-button {
  height: 70rpx;
  background-color: #e6e6e6;
  border-radius: 0 0 16rpx 16rpx;
  .text {
    margin-left: 12rpx;
  }
}
.filter-scroller {
  background-color: #f5f5f5;
  width: 180rpx;
  .filter-list {
    width: 180rpx;
    .filter-item {
      position: relative;
      height: 106rpx;
      padding: 0 24rpx;
      color: $text-color-light;
      font-size: 26rpx;
      &.active {
        background: white;
        color: $text-color-main;
        &::before {
          content: " ";
          background-color: #005bac;
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 6rpx;
        }
      }
    }
  }
}
.product-scroller {
  width: calc(100vw - 180rpx);
}
</style>
