<template>
  <view class="v-uploader-item" @click="() => changeImage?.(imageIndex)">
    <view
      class="image-container"
      :style="{ opacity: ['loading', 'error'].includes(status) ? 0.5 : 1 }"
    >
      <v-image
        mode="aspectFill"
        height="100%"
        width="100%"
        :src="src"
        border-radius="12rpx"
      />
    </view>
    <view v-if="status === 'loading'" class="status">
      上传中
    </view>
    <view v-else-if="status === 'error'" class="status">
      上传失败
    </view>
    <view class="delete-button" @click.stop="() => deleteImage?.(imageIndex)">
      <v-icon size="36rpx" src="/static/icons/components/delete-button.png" />
    </view>
  </view>
</template>

<script setup lang="ts">
import type { UploaderStatus } from "./types"

defineProps<{
  status: UploaderStatus
  src: string
  url: string
  imageIndex: number
}>()
const changeImage = inject<(index: number) => void>("changeImage")
const deleteImage = inject<(index: number) => void>("deleteImage")
</script>

<style lang="scss" scoped>
.v-uploader-item {
  position: relative;
  height: var(--v-uploader-size);
  width: var(--v-uploader-size);
  border-radius: 12rpx;
  margin-right: 20rpx;
  border: 1px solid rgba(0, 91, 172, 0.5);
  .image-container {
    height: var(--v-uploader-size);
    width: var(--v-uploader-size);
    padding: 2rpx;
    border-radius: 12rpx;
    box-sizing: border-box;
  }
  .status {
    position: absolute;
    bottom: 2rpx;
    left: 2rpx;
    right: 2rpx;
    height: 32rpx;
    line-height: 32rpx;
    font-size: 20rpx;
    color: white;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 0 0 12rpx 12rpx;
  }
  .delete-button {
    position: absolute;
    right: 0;
    top: 0;
    padding: 24rpx;
    transform: translate(50%, -50%);
  }
}
</style>
