export default (): {
  payOrder: (orderNo: string) => Promise<boolean>
} => {
  const payOrder = async (orderNo: string) => {
    const data = await chatPay(orderNo)
    if (!data) return false
    const payresult = await requirePayment({ ...data, orderInfo: "" })
    if (!payresult) return false
    checkPayStatus(orderNo)
    return true
  }

  /** 创建订单支付 */
  const chatPay = async (orderNo: string) => {
    const wxCode = await new Promise<string>((resolve) => {
      uni.login({
        success: (res) => resolve(res.code),
        fail: () => resolve("")
      })
    })
    if (!wxCode) return uni.hideLoading()
    const response = await $api.weChatPay({
      orderNo,
      wxCode
    })
    switch (response.__error) {
      case undefined:
        return response.data
    }
  }

  const requirePayment = async (data: PaymentData) => {
    const result = await payment({
      orderInfo: "",
      nonceStr: data.nonceStr,
      package: data.package,
      paySign: data.paySign,
      signType: data.signType,
      timeStamp: data.timeStamp
    })
    uni.hideLoading()
    if (result) {
      // 支付成功后后台去主动查询支付状态
      return true
    }
    console.log("支付取消")
    return false
  }
  /** 支付 */
  const payment = async (data: PaymentData) => {
    const provider = await getProvider()
    if (!provider) return
    const paymentResult = await requestPayment(provider, data)
    return paymentResult
  }

  /** 获取支付权限 */
  const getProvider = async () => {
    const provider = await new Promise<PaymentProvider | undefined>(
      (resolve) => {
        uni.getProvider({
          service: "payment",
          success: (result) => {
            resolve(result.provider?.[0] as PaymentProvider)
          },
          fail: () => resolve(undefined)
        })
      }
    )
    if (!provider) {
      await uni.showModal({ title: "支付授权失败", showCancel: false })
    } else {
      return provider
    }
  }

  /** 拉起支付 */
  const requestPayment = async (
    provider: PaymentProvider,
    data: PaymentData
  ) => {
    const paymentResult = await new Promise<boolean>((resolve) => {
      uni.requestPayment({
        provider,
        ...data,
        success: () => resolve(true),
        fail: () => resolve(false)
      })
    })
    return paymentResult
  }

  /** 查询支付状态 */
  const checkPayStatus = async (orderNo: string) => {
    // 这里延迟500毫秒查询
    await new Promise((resolve) => setTimeout(resolve, 500))
    const response = await $api.checkWeChatPayStatus({ orderNo })
    switch (response.__error) {
      case undefined:
    }
  }

  return { payOrder }
}
