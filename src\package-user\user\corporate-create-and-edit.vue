<template>
  <view>
    <view class="page-header">
      <v-navbar :title="title" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <view class="v-block plain">
        <view class="v-form-item flex-center">
          <view class="text-bold label required">
            公司名
          </view>
          <view class="flex-1">
            <v-input
              v-model="corporate.corporateName"
              maxlength="40"
            />
          </view>
        </view>
      </view>
      <view class="submit-button-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        type="primary"
        height="94rpx"
        font-size="32rpx"
        @click="submit"
      >
        保存
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { safeNavigateBack, pageHeight } = usePageLayout()

const pageStore = $store.page()

const title = ref("编辑公司")

const corporate = ref<Corporate>({ id: "", corporateName: "" })

const validate = () => {
  const reg = /[^\u4E00-\u9FA5a-zA-Z]/
  switch (true) {
    case !corporate.value.corporateName:
      uni.showToast({ title: "请填写公司名", icon: "none" })
      return false
    case reg.test(corporate.value.corporateName):
      uni.showToast({ title: "公司名只能填写中英文", icon: "none" })
      return false
    default:
      return true
  }
}
const submit = async () => {
  if (!validate()) return
  uni.showLoading({ title: "提交中", mask: true })
  const response = await $api.corporateSaveAndUpdate({
    id: corporate.value.id || undefined,
    corporateName: corporate.value.corporateName
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      // uni.$emit("select-corporate", response.data.id)
      await uni.showModal({ title: "保存成功", showCancel: false })
      safeNavigateBack()
  }
}

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  if (query?.id) {
    title.value = "编辑公司"
    const editCorporate = pageStore.corporateList.find(item => item.id === query.id)
    if (!editCorporate) {
      safeNavigateBack()
      return
    }
    corporate.value = { ...editCorporate }
  } else {
    title.value = "新增公司"
  }
})
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0 0 24rpx;
}
.submit-button-blank {
  height: 94rpx;
}
.submit-button {
  position: fixed;
  padding: 0 24rpx;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
