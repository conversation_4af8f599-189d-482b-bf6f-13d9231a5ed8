<template>
  <view class="v-tabbar">
    <view v-if="isShowTabbar" class="tabbar">
      <view v-if="isShowLogin" class="login-bar flex-center-between">
        <view class="text-white text-26">
          登录后可查看更多功能
        </view>
        <v-button
          type="primary"
          height="56rpx"
          width="184rpx"
          font-size="26rpx"
          @click="navigateToSignUp"
        >
          立即登录
        </v-button>
      </view>
      <view class="flex">
        <view class="flex-1">
          <v-tabbar-item
            :is-active="index === 0"
            url="/pages/index/index-page"
            icon="/static/icons/tabbar/home.png"
            active-icon="/static/icons/tabbar/home-active.png"
            label="首页"
          />
        </view>
        <view class="flex-1">
          <v-tabbar-item
            :is-active="index === 1"
            url="/pages/product/product-page"
            icon="/static/icons/tabbar/product.png"
            active-icon="/static/icons/tabbar/product-active.png"
            label="分类"
          />
        </view>
        <view class="flex-1">
          <v-tabbar-item
            :is-active="index === 2"
            url="/pages/cart/cart-page"
            icon="/static/icons/tabbar/cart.png"
            active-icon="/static/icons/tabbar/cart-active.png"
            label="购物车"
          />
        </view>
        <view class="flex-1">
          <v-tabbar-item
            :is-active="index === 3"
            url="/pages/user/user-page"
            icon="/static/icons/tabbar/user.png"
            active-icon="/static/icons/tabbar/user-active.png"
            label="我的"
          />
        </view>
      </view>
      <view class="tabbar-padding-bottom" />
    </view>
    <view class="tabbar-blank" />
  </view>
</template>

<script lang="ts" setup>
import { checkToken } from "@/utils/auth/token"
import vTabbarItem from "./v-tabbar-item.vue"

defineProps<{
  // 当前页序号
  index: number
}>()

const navigateToSignUp = () => {
  uni.navigateTo({ url: "/pages/user/sign-up" })
}

const pageStore = $store.page()
const isShowTabbar = computed(() => pageStore.showCustomTabbar)
const isShowLogin = ref(false)

const switchTab = (url: string) => {
  if (!checkToken() && url === "/pages/cart/cart-page") {
    return navigateToSignUp()
  }
  uni.switchTab({ url })
}
provide("switchTab", switchTab)

onShow(() => {
  isShowLogin.value = !checkToken()
})
</script>

<style lang="scss" scoped>
.v-tabbar {
  position: relative;
  .login-bar {
    position: absolute;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 0 24rpx;
    height: 80rpx;
    transform: translateY(-80rpx);
  }
  .tabbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    z-index: 300;
  }
  .tabbar-blank {
    height: calc(110rpx + constant(safe-area-inset-bottom));
    height: calc(110rpx + env(safe-area-inset-bottom));
  }
  .tabbar-padding-bottom {
    height: constant(safe-area-inset-bottom);
    height: env(safe-area-inset-bottom);
  }
}
</style>
