var threshold = 20
var duration = 300
var timer
var hideDisplay = "unset"

// 开始触摸
function touchstart (event, ins) {
  var instance = ins || event.instance
  var state = instance.getState()
  if (state.disabled) return
  var touches = event.touches
  // 避免多指触控
  if (touches && touches.length > 1) return
  state.moving = true
  state.startX = touches[0].pageX
  state.startY = touches[0].pageY

  if (timer !== undefined) {
    instance.clearTimeout(timer)
    timer = undefined
  }
}

// 触摸滑动
function touchmove (event, ins) {
  var instance = ins || event.instance
  var state = instance.getState()
  if (!state.startX) return
  var touches = event.touches
  var currentX = touches[0].pageX
  var currentY = touches[0].pageY
  var offsetX = currentX - state.startX
  var offsetY = currentY - state.startY

  // 未开始时，y轴移动比x轴移动多，不判断
  if (!state.started && Math.abs(offsetX) < Math.abs(offsetY)) return

  var content = instance.selectComponent(".v-swipe__content")
  var button = instance.selectComponent(".v-swipe__button")
  if (!content || !button) return
  // 更新按钮宽度
  var bounding = button.getBoundingClientRect()
  state.buttonsWidth = bounding.width
  if (state.status === "open") {
    // 开启状态不能往左滑动，也不能超过按钮长度
    if (offsetX < 0) offsetX = 0
    if (offsetX > state.buttonsWidth) offsetX = state.buttonsWidth
    // 更新offset
    state.offsetX = -state.buttonsWidth + offsetX
  } else {
    // 关闭状态不能往右滑动，也不能超过按钮长度
    if (offsetX > 0) offsetX = 0
    if (Math.abs(offsetX) > state.buttonsWidth) offsetX = -state.buttonsWidth
    // 更新offset
    state.offsetX = offsetX
  }
  if (Math.abs(state.offsetX) > threshold) state.started = true
  moveSwipeAction(state.offsetX, content, button, instance)
}

// 触摸结束
function touchend (event, ins) {
  var instance = ins || event.instance
  var state = instance.getState()
  if (!state.startX) return
  var offsetX = state.offsetX

  var content = instance.selectComponent(".v-swipe__content")
  var button = instance.selectComponent(".v-swipe__button")
  if (!content || !button) return

  if (Math.abs(state.offsetX) > state.buttonsWidth / 2) {
    openSwipeAction(content, button, instance)
  } else {
    closeSwipeAction(content, button, instance)
  }
  state.started = false
  state.offsetX = undefined
}

// 刷新位移
function moveSwipeAction (offsetX, content, button, instance) {
  instance.requestAnimationFrame(function () {
    // 这里检测它是否算是打开
    if (-offsetX > threshold) {
      hideDisplay = "none"
    } else {
      hideDisplay = "unset"
    }
    content.setStyle({
      transition: "none",
      width: "calc(100% + " + offsetX + "px)",
      "--v-swipe-hide-display": hideDisplay
    })
    button.setStyle({ display: "unset" })
  })
}

// 一次性展开滑动菜单
function openSwipeAction (content, button, instance) {
  var state = instance.getState()
  // 展开过程中，是向左移动，所以X的偏移应该为负值
  instance.requestAnimationFrame(function () {
    // 设置菜单主体内容
    hideDisplay = "none"
    content.setStyle({
      transition: "width " + duration + "ms",
      width: "calc(100% - " + state.buttonsWidth + "px)",
      "--v-swipe-hide-display": hideDisplay
    })
    button.setStyle({ display: "unset" })
  })
  state.status = "open"
}

// 一次性收起滑动菜单
function closeSwipeAction (content, button, instance) {
  var state = instance.getState()
  instance.requestAnimationFrame(function () {
    // 设置菜单主体内容
    content.setStyle({
      transition: "width " + duration + "ms",
      width: "100%",
      "--v-swipe-hide-display": hideDisplay
    })
    // 这里加个锁，完全结束动画时判断，判断完了再隐藏按钮
    timer = instance.setTimeout(function () {
      var state = instance.getState()
      if (state.status !== "close") return
      hideDisplay = "unset"
      content.setStyle({
        transition: "width " + duration + "ms",
        width: "100%",
        "--v-swipe-hide-display": hideDisplay
      })
      button.setStyle({ display: "none" })
    }, duration)
  })
  state.status = "close"
}

function close (event, ins) {
  var instance = ins || event.instance
  var state = instance.getState()

  var content = instance.selectComponent(".v-swipe__content")
  var button = instance.selectComponent(".v-swipe__button")
  if (!content || !button) return
  closeSwipeAction(content, button, instance)
}

module.exports = {
  touchstart: touchstart,
  touchmove: touchmove,
  touchend: touchend,
  close: close
}
