<template>
  <view class="v-block">
    <view class="order-id flex-center-between">
      <view class="text-light text-24 flex-center">
        <view class="text-light text-24 flex-center" @click="selectSingleItem">
          <v-checkbox :checked="order.selected" />&nbsp;
          {{ `订单编号:${order.orderNo}` }}
        </view>
        <view
          class="copy-style text-20"
          style="color: #005bac"
          @click="copyOrderNo"
        >
          复制
        </view>
      </view>
      <view class="date text-light text-24" @click="selectSingleItem">
        {{ formateUtc(order.orderTime, "YYYY-MM-dd") }}
      </view>
    </view>
    <view class="divider order-info-margin-adjust" @click="selectSingleItem" />
    <view class="order-info flex-center-between" @click="selectSingleItem">
      <view class="order-headline">
        {{ order.productList[0].name }}
      </view>
      <view class="right-box-style">
        <view class="price">
          ￥{{ order.actualPayPrice }}
        </view>
        <view class="product-sum">
          共{{ countProductNum(order.productList) }}件
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
const props = defineProps<{
  data: NotIssueInvoice
}>()

const order = toRef(props, "data")
const countProductNum = (product: ProductOrderItem[]) => {
  let count = 0
  product.forEach((item) => {
    count += item.number
  })
  return count
}
const selectSingleItem = () => {
  order.value.selected = !order.value.selected
}
const copyOrderNo = () => {
  if (!order.value) return
  uni.setClipboardData({
    data: order.value.orderNo,
    success: () => {
      uni.showToast({ title: "复制成功", icon: "none" })
    },
    fail: (err) => {
      console.log({ err })
      uni.showToast({ title: "复制失败", icon: "none" })
    }
  })
}
</script>

<style lang="scss" scoped>
.v-block {
  padding: 4rpx 24rpx;
}
.copy-style {
  border-radius: 10rpx;
  border: 1rpx solid #005bac;
  margin-left: 10rpx;
  padding: 0 6rpx;
}
.order-id {
  height: 86rpx;
}
.right-box-style {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}
.order-headline {
  font-weight: 800;
  color: #231815;
  font-size: 28rpx;
}
.order-info {
  margin-bottom: 31rpx;
}
.order-info-margin-adjust {
  margin-bottom: 31rpx;
}
.price {
  color: #231815;
  font-weight: 800;
}
.product-sum {
  color: #999999;
}
.date {
  color: #999;
}
</style>
