declare interface SwiperChangeEvent {
  detail: {
    current: number
  }
}
declare interface PluginInstance {
  openWaybillTracking(option: { waybillToken: string }): void
}
declare function requirePlugin(name: string): PluginInstance
declare interface SwiperTransitionEvent {
  detail: {
    dx: number
    dy: number
  }
}
declare interface PickerViewChangeEvent {
  detail: {
    value: number[]
  }
}
declare interface GetPhoneNumberEvent {
  detail: {
    code: string
  }
}
declare interface ScrollEvent {
  detail: {
    scrollTop: number
    scrollLeft: number
  }
}
declare interface InputEvent extends Event {
  detail: {
    value: string
  }
}
declare interface ChooseAvatar {
  detail: {
    avatarUrl: string
  }
}

/** 按钮事件 */
declare namespace ButtonEvent {
  /** 获取用户手机号 */
  export interface GetPhoneNumber {
    detail: {
      code: string
    }
  }
}
