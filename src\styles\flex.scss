@each $justify in -start, -center, -end, -around, -between, -evenly {
  .flex-#{$justify} {
    display: flex;

    @if $justify == -start {
      justify-content: flex-start;
    } @else if $justify == -center {
      justify-content: center;
    } @else if $justify == -end {
      justify-content: flex-end;
    } @else if $justify == -around {
      justify-content: space-around;
    } @else if $justify == -between {
      justify-content: space-between;
    } @else if $justify == -evenly {
      justify-content: space-evenly;
    }
  }
}
@each $align in -start, -center, -end, -baseline {
  @each $justify in -start, -center, -end, -around, -between, -evenly, null {
    .flex#{$align}#{$justify} {
      display: flex;

      @if $align == -start {
        align-items: flex-start;
      } @else if $align == -center {
        align-items: center;
      } @else if $align == -end {
        align-items: flex-end;
      } @else if $align == -baseline {
        align-items: baseline;
      }

      @if $justify == -start {
        justify-content: flex-start;
      } @else if $justify == -center {
        justify-content: center;
      } @else if $justify == -end {
        justify-content: flex-end;
      } @else if $justify == -around {
        justify-content: space-around;
      } @else if $justify == -between {
        justify-content: space-between;
      } @else if $justify == -evenly {
        justify-content: space-evenly;
      }
    }
  }
}

.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}

.flex-self-start {
  align-self: flex-start;
}

.flex-self-center {
  align-self: center;
}

.flex-self-end {
  align-self: flex-end;
}

.flex-self-stretch {
  align-self: stretch;
}

.flex-warp {
  flex-wrap: wrap;
}

@for $num from 1 to 5 {
  .flex-#{$num} {
    flex: $num;
  }
}
