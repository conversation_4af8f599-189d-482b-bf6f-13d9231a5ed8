export default () => {
  const orderStatus = (order: OrderItem | OrderDetails | undefined) => {
    if (!order) return ""
    // 用户手动取消
    if (order.orderStatus === "3") return "已取消"
    // 后台强制关闭订单视为取消
    if (order.orderStatus === "5") return "已取消"

    if (order.payStatus === "1") {
      // 已支付
      switch (order.orderStatus) {
        case "4": // 已下单
          return "已支付"
        case "0": // 待发货
          return "待发货"
        case "1": // 待收货
          return "待收货"
        case "2": // 已完成
          return "已完成"
      }
    } else {
      // 未支付
      switch (order.payStatus) {
        case "0": // 待支付
          return "待支付"
        case "2": // 退款失败
          return "退款失败"
        case "3": // 退款成功
          return "退款成功"
        case "4": // 支付失败
          return "已取消"
        case "5": // 退款中
          return "退款中"
      }
    }
  }

  const orderStatusInfo = (order: OrderItem | OrderDetails | undefined) => {
    if (!order) return ""
    // 用户手动取消
    if (order.orderStatus === "3") return "用户手动取消订单。"
    // 后台强制关闭订单视为取消
    if (order.orderStatus === "5") return "手动取消。"

    if (order.payStatus === "1") {
      // 已支付
      switch (order.orderStatus) {
        case "4": // 已下单
          return "订单已支付成功，货物正在打包中，请耐心等待。"
        case "0": // 待发货
          return "货物正在打包中，请耐心等待。"
        case "1": // 待收货
          return "物流运输中，请耐心等待。"
        case "2": // 已完成
          return "订单已完成，期待下次购买。"
      }
    } else {
      // 未支付
      switch (order.payStatus) {
        case "0": // 未支付
          return "请尽快支付，超时系统将自动取消您的订单。"
        case "2": // 退款失败
          return "点击退款查询，查看退款详情。"
        case "3": // 退款成功
          return "点击退款查询，查看退款详情。"
        case "4": // 支付失败
          return "支付超时，系统自动取消订单。"
        case "5": // 退款中
          return "点击退款查询，查看退款详情。"
      }
    }
  }

  const deleteOrder = async (orderNo: string) => {
    const result = await new Promise<boolean>((resolve) =>
      uni.showModal({
        title: "提示",
        content: "是否确认删除该订单?",
        success: (res) => resolve(res.confirm)
      })
    )
    if (!result) return false
    uni.showLoading({ title: "删除订单中", mask: true })
    const response = await $api.deleteOrder({ orderNo })
    uni.hideLoading()
    switch (response.__error) {
      case undefined:
        return true
      default:
        return false
    }
  }

  const cancelOrder = async (orderNo: string) => {
    const result = await new Promise<boolean>((resolve) =>
      uni.showModal({
        title: "提示",
        content: "是否确认取消该订单?",
        success: (res) => resolve(res.confirm)
      })
    )
    if (!result) return false
    uni.showLoading({ title: "取消订单中", mask: true })
    const response = await $api.orderCancelAndRefund({ orderNo })
    uni.hideLoading()
    switch (response.__error) {
      case undefined:
        return true
      default:
        return false
    }
  }

  const pageStore = $store.page()
  const buyAgain = async (
    shoppingCartList: {
      productInfoId: string
      quantity: string
      quantityRetail: string
    }[]
  ) => {
    uni.showLoading({ title: "加载购物车中", mask: true })
    const response = await $api.comeAgainCart({ shoppingCartList })
    uni.hideLoading()
    switch (response.__error) {
      case undefined: {
        const idList =
          shoppingCartList?.map?.((item) => item.productInfoId) ?? []
        pageStore.selectProductList(idList)
        uni.navigateTo({ url: "/pages/cart/cart-list" })
      }
    }
  }

  const receive = async (orderNo: string, transactionId?: string) => {
    if (!transactionId) {
      uni.showToast({ title: "交易单号为空", icon: "none" })
      return
    }
    uni.showLoading({ title: "加载中", mask: true })
    const result = await new Promise<boolean>((resolve) => {
      // eslint-disable-next-line
      //@ts-ignore
      wx.openBusinessView({
        businessType: "weappOrderConfirm",
        extraData: {
          transaction_id: transactionId
        },
        success: (data: { extraData: { status: string } }) => {
          console.log("success", data)
          if (data?.extraData?.status === "success") {
            resolve(true)
          } else {
            resolve(false)
          }
        },
        fail: (err: unknown) => {
          uni.showToast({ title: "确认收货失败", icon: "none" })
          console.log("fail", err)
          resolve(false)
        }
      })
    })
    // const result = await new Promise<boolean>((resolve) =>
    //   uni.showModal({
    //     title: "是否确认收货",
    //     content: "请确认收到货后才点击收货以避免损失",
    //     success: (res) => resolve(res.confirm)
    //   })
    // )
    if (!result) return false
    uni.showLoading({ title: "加载中", mask: true })
    const response = await $api.confirmReceipt({ orderNo })
    uni.hideLoading()
    switch (response.__error) {
      case undefined:
        return true
      default:
        return false
    }
  }

  return {
    orderStatus,
    orderStatusInfo,
    deleteOrder,
    cancelOrder,
    buyAgain,
    receive
  }
}
