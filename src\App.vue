<template>
  <view />
</template>
<script setup lang="ts">
const updateManager = uni.getUpdateManager()
updateManager.onUpdateReady(() => {
  uni.showModal({
    title: "更新提示",
    content: "新版本已经准备好，是否重启应用？",
    success (res) {
      if (res.confirm) {
        updateManager.applyUpdate()
      }
    }
  })
})
</script>
<style lang="scss">
@import "./styles/global.scss";
@import "./styles/flex.scss";
@import "./styles/text.scss";
@import "./styles/custom.scss";
</style>
