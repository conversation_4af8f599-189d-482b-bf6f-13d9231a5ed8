<template>
  <scroll-view
    v-if="pageHeight"
    scroll-y
    :style="{ height: `${pageHeight}px` }"
    enable-back-to-top
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="refresh"
    @scrolltolower="getPendingList"
  >
    <view class="page-padding-top" />
    <template v-if="!refreshing">
      <v-loading-block v-if="loading" />
      <v-empty
        v-else-if="status === 'nomore' && aftersalesList.length === 0"
        src="/static/image/empty-order.png"
      >
        暂无售后单
      </v-empty>
      <template v-else>
        <AftersalesItem
          v-for="aftersales in aftersalesList"
          :key="aftersales.aftersalesId"
          :aftersales="aftersales"
          @refresh="initAftersalesList"
        />
        <v-loadmore v-if="!refreshing" :status="status" @click="getPendingList" />
      </template>
    </template>
    <view class="page-padding-bottom" />
  </scroll-view>
</template>

<script setup lang="ts">
import AftersalesItem from "./aftersales-item.vue"
import { num2str, str2num } from "@/utils/type-transfer"

const props = withDefaults(
  defineProps<{
    orderStatus: number[]
    pageHeight: number
  }>(),
  {
    orderStatus: undefined,
    pageHeight: undefined
  }
)

const aftersalesList = ref<AftersalesItem[]>([])

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = () => {
  refreshing.value = true
  pageIndex.value = 1
  aftersalesList.value = []
  status.value = "loadmore"
  getPendingList()
}
const initAftersalesList = async () => {
  loading.value = true
  pageIndex.value = 1
  aftersalesList.value = []
  status.value = "loadmore"
  await getPendingList()
  loading.value = false
}

/** 分页数据 */
const pageIndex = ref(1)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")

const orderStatus = toRef(props, "orderStatus")
const getPendingList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  const response = await $api.orderRefundList({
    orderField: "createdTime",
    pageIndex: num2str(pageIndex.value),
    pageSize: "10",
    requestParams: {
      /** 售后状态：:0->售后待审核，1->售后审核通过，2->售后审核驳回，3->仓库待签收，4->仓库已验收，5->退款通过，6->退款待审核，8->用户主动取消，9->已完成 */
      backStatuses: orderStatus.value.map((item) => {
        return {
          afterSaleStatus: item
        }
      })
    },
    /** 排序方式 desc or asc */
    sortType: "desc"
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      const list: AftersalesItem[] =
        response.data.result?.map?.((aftersales) => ({
          aftersalesId: aftersales.orderBackId,
          aftersalesNo: aftersales.orderAfterSaleNo,
          aftersalesTime: aftersales.createdTime,
          orderNo: aftersales.orderNo,
          aftersalesStatus: aftersales.afterSaleStatus,
          aftersalesType: aftersales.afterSaleType,
          refundAmount: str2num(aftersales.afterSaleApplyPrice),
          productList: aftersales.orderProductRelationVOList.map((product) => {
            const item: ProductAftersalesItem = {
              id: product.productInfoId,
              image: product.productInfoProductImg,
              name: product.productInfoName,
              unitPrice: str2num(product.productInfoPrice),
              unitAmount: str2num(product.productInfoPackage),
              displayPrice: str2num(product.displayUnitPrice),
              displayAmount: product.displayPackage,
              productInfoSpec: product.productInfoSpec,
              number: str2num(product.quantity),
              returnNumber: product.memberReturnBoxNumber
            }
            return item
          })
        })) ?? []
      aftersalesList.value = [...aftersalesList.value, ...list]
      const totalCount = str2num(response.data.totalCount)
      status.value =
        aftersalesList.value.length < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
}

defineExpose({ initAftersalesList })
</script>
