<template>
  <view class="page-header background-white">
    <v-navbar title="领券中心" back />
  </view>
  <v-popover :model-value="isShow" @close="close">
    <view class="rule">
      <view
        class="title flex-center-center"
        style="font-size: 32rpx; color: #333333; font-weight: bold"
      >
        使用规则
        <view class="close" @click="hidePopover">
          <v-icon size="24rpx" src="/static/icons/cart/close-icon.png" />
        </view>
      </view>
      <scroll-view scroll-y style="height: 50vh">
        <view class="text">
          <view>
            {{ rule }}
          </view>
          <view class="popper-padding-bottom background-white" />
        </view>
      </scroll-view>
    </view>
  </v-popover>
  <scroll-view
    v-if="pageHeight"
    scroll-y
    :style="{ height: `${pageHeight}px` }"
    enable-back-to-top
    refresher-enabled
    :refresher-triggered="refreshing"
    @refresherrefresh="refresh"
    @scrolltolower="getReceiveCouponCenterList"
  >
    <view class="page-padding-top" />
    <template v-if="!refreshing">
      <v-loading-block v-if="loading && !refreshing" />
      <v-empty
        v-else-if="status === 'nomore' && receiveList.length === 0"
        src="/static/image/empty-order.png"
      >
        暂无可领取券
      </v-empty>
      <template v-else>
        <receive-item
          v-for="(item, itemIndex) in receiveList"
          :key="itemIndex"
          :coupon="item"
          @refresh-coupon="refresh"
          @show-popover="showPopover"
        />
        <v-loadmore
          v-if="!refreshing"
          :status="status"
          @click="getReceiveCouponCenterList"
        />
      </template>
    </template>
    <view class="popper-padding-bottom" />
  </scroll-view>
</template>

<script setup lang="ts">
import ReceiveItem from "./modules/receive-item.vue"
import { str2num } from "@/utils/type-transfer"

const { pageHeight } = usePageLayout()

const receiveList = ref<ReceiveItem[]>([])
const isShow = ref(false)
const rule = ref("")
const close = () => {
  isShow.value = false
}
const hidePopover = () => {
  isShow.value = false
}
const showPopover = (event: string) => {
  rule.value = event
  isShow.value = true
}
/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  await initCouponList()
}
const initCouponList = async () => {
  loading.value = true
  pageIndex.value = 1
  receiveList.value = []
  status.value = "loadmore"
  await getReceiveCouponCenterList()
  await nextTick()
  refreshing.value = false
  loading.value = false
}
onLoad(() => {
  initCouponList()
})
/** 分页数据 */
const pageIndex = ref(1)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")
/** 获取列表 */
const getReceiveCouponCenterList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  const response = await $api.getReceiveCouponCenterList({
    orderField: "overdueTime",
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    sortType: "desc"
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      const list: ReceiveItem[] =
        response.data.result?.map?.((item) => ({
          /** 优惠券总数 */
          couponAmount: str2num(item.couponAmount),
          couponAreaLimit: item.couponAreaLimit,
          couponExp: item.couponExp,
          /** 优惠券过期类型:0->设定;1->领用; */
          couponExpType: item.couponExpType,
          /** 优惠券名称 */
          couponName: item.couponName,
          /** 优惠券编号 */
          couponNo: item.couponNo,
          /** 优惠券使用价格限制 */
          couponPriceLimit: item.couponPriceLimit,
          /** 优惠券日领取限制 */
          couponReceiveDailyLimit: str2num(item.couponReceiveDailyLimit),
          /** 优惠券领取限制 */
          couponReceiveLimit: str2num(item.couponReceiveLimit),
          /** 备注 */
          couponRemark: item.couponRemark,
          /** 优惠券类型:0->满减;1->打折; */
          couponType: item.couponType,
          /** 优惠券用户类型限制:0->全部;1->新用户;2->老用户; */
          couponUserTypeLimit: item.couponUserTypeLimit,
          /** 优惠券面值 */
          couponValue: item.couponValue,
          /** 发布时间 */
          publishTime: item.publishTime,
          /** 领券中心Id */
          receiveCouponCenterRelationId: item.receiveCouponCenterRelationId,
          /** 优惠券剩余数 */
          residueAmount: str2num(item.residueAmount),
          /** 序号 */
          serialnumber: item.serialnumber,
          /** 优惠券地区限制 */
          strCouponAreaLimit: item.strCouponAreaLimit,
          /** 优惠券有效期 */
          strCouponExp: item.strCouponExp,
          /** 用户领取数量 */
          userReceiveAmount: item.userReceiveAmount,
          /** 用户当日领取数量 */
          userReceiveDailyAmount: item.userReceiveDailyAmount
        })) ?? []
      receiveList.value = [...receiveList.value, ...list]
      const totalCount = str2num(response.data.totalCount)
      status.value =
        receiveList.value.length < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
}
</script>

<style lang="scss" scoped>
.page-footer-content {
  height: 80rpx;
  padding-top: 20rpx;
}
.rule {
  width: 100%;
  background-color: white;

  .title {
    height: 80rpx;
    border-bottom: 1px solid #e6e6e6;
    box-sizing: border-box;
    position: relative;
    .close {
      position: absolute;
      top: 50%;
      right: 24rpx;
      transform: translateY(-50%);
    }
  }

  .text {
    padding: 32rpx;
    box-sizing: border-box;
    white-space: pre-line;
  }
}
</style>
