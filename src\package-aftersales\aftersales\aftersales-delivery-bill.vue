<template>
  <view>
    <view class="page-header">
      <v-navbar title="上传发货单" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <view class="v-block plain">
        <view class="v-form-item">
          <view class="label text-bold">
            寄回包裹信息
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label required">
            快递公司
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="delivery.deliveryName"
              class="flex-1"
              placeholder="请输入"
              placeholder-class="placeholder"
            >
            <!-- <v-icon size="22rpx" src="/static/icons/right-arrow.png" /> -->
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label required">
            快递单号
          </view>
          <view class="flex-center flex-1">
            <input
              v-model="delivery.deliveryNo"
              class="flex-1"
              placeholder="请输入快递单号"
              placeholder-class="placeholder"
            >
            <view v-if="showTool" class="tooltip-container" @click="test">
              <view v-if="noticeFlag" class="tooltip">
                <v-icon
                  height="64rpx"
                  width="178rpx"
                  src="/static/icons/aftersales/scan-tooltip.png"
                />
                <view class="text flex-center-center">
                  扫码更便捷
                </view>
              </view>
              <v-icon size="34rpx" src="/static/icons/aftersales/scan.png" />
            </view>
          </view>
        </view>
      </view>
      <view class="v-block plain">
        <view class="v-form-item">
          <view class="label text-bold">
            包裹内商品信息
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            售后单号
          </view>
          <view class="flex-center flex-1 text-light">
            {{ delivery.aftersalesNo }}
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label">
            申请时间
          </view>
          <view class="flex-center flex-1 text-light">
            {{ formateUtc(delivery.aftersalesTime) }}
          </view>
        </view>
      </view>
      <view class="text-bold" style="padding: 0 20rpx 20rpx 20rpx">
        商品信息
      </view>
      <view
        v-for="product in delivery.productList"
        :key="product.id"
        class="v-block v-block-product"
        :product="product"
        size="small"
      >
        <view class="order-no">
          订单编号：{{ delivery.orderNo }}
        </view>
        <view class="divider order-headline-margin-adjust" />
        <view class="product-item">
          <v-product-item :product="product" size="small">
            <template #name>
              {{ product.name }}
            </template>
            <template #spec>
              {{ product.productInfoSpec }}
            </template>
            <template #default>
              <view class="text-light text-22">
                {{ `装箱规格: ${product.displayAmount}pcs/箱` }}
              </view>
              <view class="text-sub text-22">
                {{ `订购数量: ${product.number}箱` }}
              </view>
            </template>
          </v-product-item>
          <view class="divider handle-count" />
          <view class="v-form-item flex-center-between">
            <view class="text-bold label">
              申请数量(箱)
            </view>
            <view>{{ product.returnNumber }}箱</view>
          </view>
        </view>
      </view>
      <view class="submit-button-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        type="primary"
        height="94rpx"
        font-size="32rpx"
        @click="submit"
      >
        提交
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
const { safeNavigateBack, pageHeight } = usePageLayout()

const pageStore = $store.page()

const delivery = ref<AftersalesDelivery>({
  orderNo: "",
  aftersalesNo: "",
  aftersalesId: "",
  aftersalesTime: NaN,
  deliveryName: "",
  deliveryNo: "",
  productList: []
})
const noticeFlag = ref(true)
const showTool = ref(true)
const test = async () => {
  uni.scanCode({
    scanType: ["barCode"],
    success: (res) => {
      delivery.value.deliveryNo = res.result
    }
  })
}
const validate = () => {
  switch (true) {
    case !delivery.value.deliveryName:
      uni.showToast({ title: "请填写快递公司", icon: "none" })
      return false
    case !delivery.value.deliveryNo:
      uni.showToast({ title: "请输入快递单号", icon: "none" })
      return false
    case delivery.value.deliveryName.length > 10:
      uni.showToast({
        title: "请输入正确的物流公司名称",
        icon: "none"
      })
      return false
    case !/^[0-9da-zA-z]{1,30}$/.test(delivery.value.deliveryNo):
      uni.showToast({
        title: "请输入正确的快递单号",
        icon: "none"
      })
      return false
    default:
      return true
  }
}

const submit = async () => {
  if (!validate()) return
  uni.showLoading({ title: "提交中", mask: true })
  const response = await $api.updateOrderBackDelivery({
    deliveryName: delivery.value.deliveryName,
    deliveryNo: delivery.value.deliveryNo,
    orderBackId: delivery.value.aftersalesId
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      await uni.showModal({ title: "提交成功", showCancel: false })
      safeNavigateBack()
  }
}
onMounted(() => {
  setTimeout(() => {
    noticeFlag.value = false
  }, 2000)
})
onLoad(() => {
  if (!pageStore.aftersalesDelivery) {
    safeNavigateBack()
  } else {
    delivery.value = { ...pageStore.aftersalesDelivery }
  }
})
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0 0 24rpx;
}
.v-block-product {
  margin: 0 24rpx 24rpx;
}
.v-block.plain {
  padding: 8rpx 24rpx;
}
.package-title {
  padding-top: 20rpx;
}
.package-info-title {
  padding: 20rpx 0;
}
.product-number {
  padding: 20rpx 0;
}
.submit-button-blank {
  height: 94rpx;
}
.submit-button {
  position: fixed;
  padding: 0 24rpx;
  left: 0;
  right: 0;
  bottom: 0;
}
.tooltip-container {
  position: relative;
}
.tooltip {
  position: absolute;
  top: 0;
  right: 50%;
  height: fit-content;
  width: fit-content;
  transform: translateY(-100%);
  .text {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 20%;
    left: 0;
    color: white;
    font-size: 24rpx;
  }
}

.red {
  color: #e60012;
}
.aftersales-select-handle {
  position: fixed;
  left: 24rpx;
  right: 24rpx;
  bottom: 0;
  z-index: 10;
  .next-btn {
    display: flex;
    margin-top: 32rpx;
    justify-content: center;
    align-items: center;
    height: 94rpx;
    font-size: 32rpx;
    border-radius: 47rpx 47rpx;
    background-color: #005bac;
    color: #fff;
  }
}
.spec-style {
  color: #999999;
  font-size: 24rpx;
}
.count-bottom-style {
  color: #666666;
  font-size: 24rpx;
}
.handle-select {
  display: flex;
  justify-content: space-between;
  .selected-product-notice {
    font-size: 24rpx;
    color: #666;
  }
  .selected-all-style {
    font-size: 24rpx;
    color: #666;
  }
}
.handle-count {
  margin-top: 14rpx;
}
.price-notice {
  color: #999999;
  font-size: 22rpx;
}
.product-info {
  margin: 0 24rpx 24rpx;
}
.back-info-style {
  margin: 7rpx 0;
}
.delivery-hendle-margin {
  margin-bottom: 20rpx;
}
.order-no {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}
.order-headline-margin-adjust {
  margin-bottom: 12rpx;
}
</style>
