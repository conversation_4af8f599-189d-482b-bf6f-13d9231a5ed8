<template>
  <view class="coupon-item" :class="{ disabled: status !== '领取' }">
    <view class="top flex">
      <view class="left flex-col flex-center-center">
        <view class="price flex-baseline">
          <view class="price-icon">
            ￥
          </view>
          <view class="price-num">
            {{ parseInt(coupon.couponValue) }}
          </view>
        </view>
        <view class="text">
          {{ `满 ${parseInt(coupon.couponPriceLimit)} 可用` }}
        </view>
      </view>
      <view class="right flex-1 flex-col flex--center">
        <view class="name text-1-row">
          {{ coupon.couponName }}
        </view>
        <view
          class="rule flex-center"
          @click="showPopover(coupon.couponRemark)"
        >
          <view>使用规则</view>
          <view>
            <v-icon
              size="17rpx"
              margin-left="11rpx"
              src="/static/icons/coupon/rule-icon.png"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="bottom flex-center-between">
      <view class="time">
        {{ time }}
      </view>
      <view class="btn flex-center-center" @click="couponCenterReceiveCoupon">
        {{ status }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { formateUtc } from "@/utils/time"
const props = defineProps<{
  coupon: ReceiveItem
}>()
const coupon = toRef(props, "coupon")

interface Emits {
  (event: "refresh-coupon"): void
  (event: "show-popover", value: string): void
}
const emits = defineEmits<Emits>()

const showPopover = (value: string) => {
  emits("show-popover", value)
}
const time = computed(() => {
  if (coupon.value.couponExp.expDays) {
    const dayTime = coupon.value.couponExp.expDays
    return `领取后${dayTime}天内有效`
  } else {
    const couponExp = coupon.value.couponExp
    const nowTime = new Date().getTime()
    if (coupon.value.couponExpType === 0 && couponExp.ranges) {
      // 排序时间列表
      const list = couponExp.ranges.sort((a, b) => {
        return a.startTime - b.startTime
      })
      let startTime = 0
      let endTime = 0
      for (let i = 0; i < list.length; i++) {
        if (nowTime < list[0].startTime) {
          // 当前时间还没到使用范围
          startTime = list[i].startTime
          endTime = list[i].endTime
          const startString = formateUtc(
            new Date(startTime),
            "YYYY.MM.dd hh:mm"
          )
          const endString = formateUtc(new Date(endTime), "YYYY.MM.dd hh:mm")
          return `${startString} ~ ${endString}`
        } else if (nowTime <= list[i].endTime && nowTime >= list[i].startTime) {
          // 时间在可使用范围段中
          startTime = list[i].startTime
          endTime = list[i].endTime
          break
        }
        startTime = list[i].startTime
        endTime = list[i].endTime
      }
      const startString = formateUtc(new Date(startTime), "YYYY.MM.dd hh:mm")
      const endString = formateUtc(new Date(endTime), "YYYY.MM.dd hh:mm")
      return `有效期:${startString} ~ ${endString}`
    }
  }

  return ""
})
const status = computed(() => {
  const residueAmount = coupon.value.residueAmount
  const userReceiveAmount = coupon.value.userReceiveAmount
  const userReceiveDailyAmount = coupon.value.userReceiveDailyAmount
  const couponReceiveLimit = coupon.value.couponReceiveLimit
  const couponReceiveDailyLimit = coupon.value.couponReceiveDailyLimit
  // 每日领取达到优惠券美日领取上线或总领取数达到优惠券领取限制时为已领取
  if (
    userReceiveDailyAmount === couponReceiveDailyLimit ||
    userReceiveAmount === couponReceiveLimit
  ) {
    return "已领取"
  }
  // 优惠券剩余数为0时已抢光
  if (residueAmount === 0) return "已抢光"
  // 优惠券剩余数大于0时可领取
  if (residueAmount > 0) return "领取"
})
const couponCenterReceiveCoupon = async () => {
  if (status.value !== "领取") return
  const response = await $api.couponCenterReceiveCoupon({
    /** 优惠券编号 */
    couponNo: coupon.value.couponNo,
    /** 领券中心id */
    receiveCouponCenterRelationId: coupon.value.receiveCouponCenterRelationId
  })
  switch (response.__error) {
    case undefined: {
      await uni.showModal({ title: "领取成功", showCancel: false })
    }
  }
  emits("refresh-coupon")
}
</script>
<style lang="scss" scoped>
.coupon-item {
  position: relative;
  width: 702rpx;
  margin: 0 auto 28rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: white;
  .top {
    height: 148rpx;
    border-bottom: 1px dashed #e6e6e6;
    .left {
      width: 204rpx;
      height: 100%;
      position: relative;
      .price {
        color: #ff5255;
        .price-icon {
          font-size: 36rpx;
        }
        .price-num {
          font-weight: bold;
          font-size: 52rpx;
        }
      }
      .text {
        color: #666666;
        font-size: 24rpx;
      }
      &::after {
        content: "";
        width: 1rpx;
        height: 100rpx;
        background-color: #e6e6e6;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 0;
      }
    }
    .right {
      width: calc(100% - 204rpx);
      padding: 15rpx 24rpx;
      overflow: hidden;
      .name {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
      }

      .rule {
        font-size: 20rpx;
        color: #999999;
        margin-top: 23rpx;
      }
    }
  }
  .bottom {
    padding: 0 28rpx;
    height: 90rpx;
    box-sizing: border-box;
    .time {
      font-size: 22rpx;
      color: #999999;
      white-space: nowrap;
    }
    .btn {
      width: 90rpx;
      height: 43rpx;
      font-size: 22rpx;
      color: #ffffff;
      background-color: #ff5255;
      border-radius: 6rpx;
    }
  }
  &::before,
  &::after {
    content: "";
    position: absolute;
    top: calc(148rpx - 14rpx);
    width: 28rpx;
    height: 28rpx;
    background-color: #f5f5f5;
    border-radius: 100%;
    z-index: 99;
  }
  &::before {
    left: 0;
    transform: translatex(-50%);
  }
  &::after {
    right: 0;
    transform: translatex(50%);
  }
}
.disabled {
  .bottom {
    .btn {
      background-color: #cccccc;
    }
  }
}
</style>
