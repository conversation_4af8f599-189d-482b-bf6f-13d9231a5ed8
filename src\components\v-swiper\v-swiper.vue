<template>
  <view class="v-swiper">
    <swiper
      :style="{ height: height || '320rpx', width: width || '700rpx' }"
      :circular="circular !== false"
      :autoplay="autoplay"
      @change="change"
    >
      <swiper-item v-for="(item, index) in list" :key="index">
        <v-image
          height="100%"
          width="100%"
          mode="aspectFit"
          border-radius="none"
          :src="
            typeof item === 'string' || srcKey === undefined
              ? item
              : item[srcKey]
          "
          :preview="preview"
          @click="$emit('click', item)"
        />
      </swiper-item>
    </swiper>
    <view v-if="type === 'dot'" class="dot-list flex-center-center">
      <view
        v-for="(image, index) in list"
        :key="index"
        class="dot-item"
        :class="{ active: index === currentIndex - 1 }"
      />
    </view>
    <view v-else-if="type === 'count'" class="index-pad">
      {{ `${currentIndex}/${total}` }}
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  list?: string[] | Record<string, string>[]
  srcKey?: string
  autoplay?: boolean
  circular?: boolean
  type?: string
  height?: string
  width?: string
  preview?: boolean
}>()

interface Emits {
  (event: "click", val: string | Record<string, string>): void
}
defineEmits<Emits>()

const list = toRef(props, "list")

const currentIndex = ref<number>(0)
const total = ref<number>(0)

const change = (event: SwiperChangeEvent) => {
  currentIndex.value = event.detail.current + 1
}

onMounted(() => {
  watch(
    list,
    (val) => {
      if (!val) return
      currentIndex.value = 1
      total.value = val.length
    },
    { immediate: true }
  )
})
</script>

<style lang="scss" scoped>
.v-swiper {
  position: relative;
  height: fit-content;
  width: fit-content;
  .dot-list {
    position: absolute;
    bottom: 24rpx;
    left: 0;
    right: 0;
    pointer-events: none;
    .dot-item {
      height: 14rpx;
      width: 14rpx;
      margin: 0 10rpx;
      border-radius: 50%;
      background-color: white;
      transition: background-color 0.2s linear;
      &.active {
        background-color: #005bac;
      }
    }
  }
  .index-pad {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    height: 38rpx;
    line-height: 38rpx;
    width: 80rpx;
    border-radius: 100vw;
    color: white;
    font-size: 24rpx;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.4);
    pointer-events: none;
  }
}
</style>
