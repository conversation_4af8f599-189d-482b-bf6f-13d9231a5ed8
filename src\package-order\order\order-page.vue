<template>
  <view>
    <view class="page-header background-white">
      <v-navbar title="订单中心" back />
      <v-tabs
        v-model="tabIndex"
        :list="['全部', '待支付', '待发货', '待收货', '已完成', '已取消']"
        height="86rpx"
      >
        <view class="v-tab-block" style="transform: translateX(0)" />
      </v-tabs>
    </view>
    <swiper
      :current="tabIndex"
      :style="{ height: `${pageHeight}px` }"
      @change="tabSwiperChange"
      @transition="wxs.transition"
      @animationfinish="wxs.transitionFinish"
    >
      <swiper-item>
        <order-list
          ref="orderList0"
          :index="0"
          :page-height="pageHeight"
          @refresh="refreshAll"
          @pay="pay"
          @paid="paid"
        />
      </swiper-item>
      <swiper-item>
        <order-list
          ref="orderList1"
          :index="1"
          :page-height="pageHeight"
          @refresh="refreshAll"
          @pay="pay"
          @paid="paid"
        />
      </swiper-item>
      <swiper-item>
        <order-list
          ref="orderList2"
          :index="2"
          :page-height="pageHeight"
          @refresh="refreshAll"
          @pay="pay"
          @paid="paid"
        />
      </swiper-item>
      <swiper-item>
        <order-list
          ref="orderList3"
          :index="3"
          :page-height="pageHeight"
          @refresh="refreshAll"
          @pay="pay"
          @paid="paid"
        />
      </swiper-item>
      <swiper-item>
        <order-list
          ref="orderList4"
          :index="4"
          :page-height="pageHeight"
          @refresh="refreshAll"
          @pay="pay"
          @paid="paid"
        />
      </swiper-item>
      <swiper-item>
        <order-list
          ref="orderList5"
          :index="5"
          :page-height="pageHeight"
          @refresh="refreshAll"
          @pay="pay"
          @paid="paid"
        />
      </swiper-item>
    </swiper>
    <v-toast />
  </view>
</template>

<script lang="ts">
import OrderList from "./modules/order-list.vue"
import wxs from "@/utils/wxs"

export default {
  components: {
    OrderList
  },
  setup () {
    const pageStore = $store.page()
    onLoad(() => pageStore.getWXTemplateId())
  },
  data () {
    return {
      lockOnShow: false,
      pageHeight: 0,
      tabIndex: 0,
      initList: [false, false, false, false, false, false],
      wxs: {
        transition: wxs.transition,
        transitionFinish: wxs.transitionFinish
      }
    }
  },
  onLoad (query: Record<string, string>) {
    const index = parseInt(query.index)
    if (!isNaN(index)) this.tabIndex = index
  },
  onShow () {
    if (!this.lockOnShow) {
      this.refreshAll()
    }
  },
  onReady () {
    this.refreshPageHeight()
  },
  methods: {
    refreshAll () {
      for (let i = 0; i < this.initList.length; i += 1) {
        this.initList[i] = false
      }
      this.initOrderList(this.tabIndex)
    },
    pay () {
      this.lockOnShow = true
    },
    paid () {
      setTimeout(() => {
        this.lockOnShow = false
      }, 500)
    },
    tabSwiperChange (event: SwiperChangeEvent) {
      this.changeTabIndex(event.detail.current)
    },
    changeTabIndex (index: number) {
      this.tabIndex = index
      this.initOrderList(index)
    },
    initOrderList (index: number) {
      const listRef = this.$refs[`orderList${index}`] as InstanceType<
        typeof OrderList
      >
      if (!listRef) return
      if (this.initList[index]) return
      this.initList[index] = true
      listRef.initOrderList()
    },
    async refreshPageHeight () {
      const system = uni.getWindowInfo()
      let screenHeight
      // #ifdef MP-WEIXIN
      screenHeight = system.screenHeight
      // #endif
      // #ifdef MP-ALIPAY
      screenHeight = system.windowHeight
      // #endif
      // #ifdef H5
      screenHeight = system.screenHeight
      // #endif
      const query = uni.createSelectorQuery()
      const headerHeight = await new Promise<number>((resolve) => {
        query
          .select(".page-header")
          .boundingClientRect((res) => {
            const result = res as UniApp.NodeInfo
            resolve(result?.height ?? 0)
          })
          .exec()
      })
      this.pageHeight = screenHeight - headerHeight
    }
  }
}
</script>

<script
  src="../../components/v-tabs/v-tabs.wxs"
  module="wxs"
  lang="wxs"
></script>

<style lang="scss" scoped>
.v-tab-block {
  height: 100%;
  width: 100%;
  background-color: #005bac;
}
</style>
