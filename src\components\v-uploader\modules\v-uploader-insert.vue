<template>
  <view class="v-uploader-insert" @click="insertImage">
    <view class="content flex-center-center">
      <v-icon size="50rpx" src="/static/icons/components/uploader-icon.png" />
    </view>
  </view>
</template>

<script setup lang="ts">
const insertImage = inject<() => void>("insertImage")
</script>

<style lang="scss" scoped>
.v-uploader-insert {
  height: var(--v-uploader-size);
  width: var(--v-uploader-size);
  padding: 2rpx;
  border-radius: 12rpx;
  border: 1px solid #F5F5F5;
  .content {
    height: 100%;
    width: 100%;
    border-radius: 12rpx;
    background-color: #F5F5F5;
  }
}
</style>
