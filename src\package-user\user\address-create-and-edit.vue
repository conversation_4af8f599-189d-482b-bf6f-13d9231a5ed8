<template>
  <view>
    <view class="page-hader">
      <v-navbar :title="title" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <view class="v-block plan">
        <view class="v-form-item flex-center">
          <view class="text-bold label required">
            收货人
          </view>
          <view class="flex-1">
            <v-input
              v-model="address.name"
              maxlength="20"
              placeholder="请输入收件人姓名"
            />
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label required">
            手机号码
          </view>
          <view class="flex-1">
            <v-input
              v-model="address.mobile"
              :replace="/[^\d]/g"
              type="number"
              maxlength="11"
              placeholder="请输入收件人手机号码"
            />
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex-center">
          <view class="text-bold label required">
            所在地区
          </view>
          <view class="flex-1">
            <v-select
              v-model="isShowRegionPicker"
              :value="regionName"
              placeholder="省/市/区"
            />
          </view>
        </view>
        <view class="divider" />
        <view class="v-form-item flex">
          <view class="text-bold label required">
            详细地址
          </view>
          <view class="textarea flex-1">
            <v-textarea
              v-model="address.address"
              maxlength="35"
              custom-style="height: 100rpx; width: auto"
              :cursor-spacing="75"
              placeholder="请输入详细地址"
            />
          </view>
          <!-- <view class="position-button flex" @click="chooseLocation">
            <v-icon
              size="32rpx"
              margin-right="4rpx"
              src="/static/icons/address/location.png"
            />
            <view class="text-24 text-bold">
              定位
            </view>
          </view> -->
        </view>
      </view>
      <view class="v-block plan">
        <view class="v-form-item flex-center-between">
          <view class="text-bold">
            设为默认地址
          </view>
          <v-switch
            :model-value="address.isDefault === 1"
            :disabled="!defaultEditAble"
            @active="address.isDefault = 1"
            @deactive="address.isDefault = 0"
          />
        </view>
      </view>
      <view class="submit-button-blank" />
      <view class="page-padding-bottom" />
    </scroll-view>
    <view class="submit-button">
      <v-button
        type="primary"
        height="94rpx"
        font-size="32rpx"
        @click="submit"
      >
        保存
      </v-button>
      <view class="page-padding-bottom" />
    </view>
    <v-picker
      ref="pickerRef"
      v-model="isShowRegionPicker"
      :options="regionList"
      @confirm="changeHandle"
    />
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { safeNavigateBack, pageHeight } = usePageLayout()

const title = ref("新增收货地址")

const pageStore = $store.page()

const address = ref<Address>({
  id: "",
  name: "",
  mobile: "",
  address: "",
  provinceCode: undefined,
  provinceName: "",
  cityCode: undefined,
  cityName: "",
  districtCode: undefined,
  districtName: "",
  isDefault: 0,
  label: "",
  tel: ""
})

const defaultEditAble = ref(true)
const pickerRef = ref()

const getRegionData = () => {
  const provinceCode = address.value.provinceCode
  const cityCode = address.value.cityCode
  const districtCode = address.value.districtCode
  if (!provinceCode || !cityCode || !districtCode) {
    address.value.provinceCode = undefined
    address.value.provinceName = ""
    address.value.cityCode = undefined
    address.value.cityName = ""
    address.value.districtCode = undefined
    address.value.districtName = ""
    return
  }

  const provinceIndex =
    regionList.value?.findIndex((item) => item.value === provinceCode) ?? -1
  const province = regionList.value?.[provinceIndex]
  if (!province) {
    address.value.provinceCode = undefined
    address.value.provinceName = ""
    address.value.cityCode = undefined
    address.value.cityName = ""
    address.value.districtCode = undefined
    address.value.districtName = ""
    return
  }

  const cityIndex =
    province.children?.findIndex((item) => item.value === cityCode) ?? -1
  const city = province.children?.[cityIndex]
  if (!city) {
    regionName.value = province.label
    pickerRef.value.setValue([provinceIndex])
    address.value.cityCode = undefined
    address.value.cityName = ""
    address.value.districtCode = undefined
    address.value.districtName = ""
    return
  }

  const districtIndex =
    city.children?.findIndex((item) => item.value === districtCode) ?? -1
  const district = city?.children?.[districtIndex]
  if (!district) {
    pickerRef.value.setValue([provinceIndex, cityIndex])
    regionName.value = [province, city].map((item) => item.label).join("/")
    address.value.districtCode = undefined
    address.value.districtName = ""
  } else {
    pickerRef.value.setValue([provinceIndex, cityIndex, districtIndex])
    regionName.value = [province, city, district]
      .map((item) => item.label)
      .join("/")
  }
}

const isShowRegionPicker = ref(false)
const regionName = ref("")
const changeHandle = (val: (Province | City | District)[]) => {
  regionName.value = val.map((item) => item.label).join("/")
  address.value.provinceCode = val[0].value
  address.value.provinceName = val[0].label
  address.value.cityCode = val[1].value
  address.value.cityName = val[1].label
  address.value.districtCode = val[2].value
  address.value.districtName = val[2].label
}
const regionList = computed(() => pageStore.regionList)
onLoad(async () => {
  await pageStore.getRegionList()
  getRegionData()
})

// const chooseLocation = () => {
//   uni.chooseLocation({
//     success: (res) => {
//       if (!res.address || !res.name) return
//       console.log(res)
//       address.value.address = `${res.address} ${res.name}`.slice()
//     }
//   })
// }

const validateName = () => {
  switch (true) {
    case !address.value.name:
      uni.showToast({ title: "请输入收件人姓名", icon: "none" })
      return false
    default:
      return true
  }
}
const validateMobile = () => {
  switch (true) {
    case !address.value.mobile:
      uni.showToast({ title: "请输入收件人手机号码", icon: "none" })
      return false
    case !/^\d{11}$/.test(address.value.mobile):
      uni.showToast({ title: "收件人手机号码请填写11位数字格式", icon: "none" })
      return false
    default:
      return true
  }
}
const validateRegion = () => {
  switch (true) {
    case !address.value.provinceCode:
      uni.showToast({ title: "请选择所在地区", icon: "none" })
      return false
    default:
      return true
  }
}
const validateAddress = () => {
  switch (true) {
    case !address.value.address:
      uni.showToast({ title: "请输入详细地址", icon: "none" })
      return false
    case address.value.address.length > 35:
      uni.showToast({ title: "详细地址最多35个字符", icon: "none" })
      return false
    default:
      return true
  }
}
const validate = () => {
  switch (true) {
    case !validateName():
    case !validateMobile():
    case !validateRegion():
    case !validateAddress():
      return false
    default:
      return true
  }
}

const submit = async () => {
  const valid = validate()
  if (!valid) return
  uni.showLoading({ title: "提交中", mask: true })
  const response = await $api.addressSaveAndUpdate({
    id: address.value.id || "0",
    name: address.value.name,
    mobile: address.value.mobile,
    address: address.value.address,
    provinceCode: address.value.provinceCode as number,
    provinceName: address.value.provinceName,
    cityCode: address.value.cityCode as number,
    cityName: address.value.cityName,
    districtCode: address.value.districtCode as number,
    districtName: address.value.districtName,
    isDefault: address.value.isDefault
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      uni.$emit("select-address", response.data.id)
      await uni.showModal({ title: "保存成功", showCancel: false })
      safeNavigateBack()
  }
}

onLoad(async (query) => {
  if (pageStore.addressList.length === 0) {
    address.value.isDefault = 1
    defaultEditAble.value = false
  }

  if (query?.id) {
    title.value = "编辑收货地址"
    const editAddress = pageStore.addressList.find(
      (item) => item.id === query.id
    )
    if (!editAddress) {
      await uni.showModal({ title: "数据错误", showCancel: false })
      return safeNavigateBack()
    }
    // 查询找到要修改的地址
    address.value = { ...editAddress }

    // 由于更新默认地址后不刷新页面，所以默认地址可能并不是本身的那个
    // 查看一下页面默认的地址是哪个，然后判断default
    address.value.isDefault =
      pageStore.defaultAddressId === address.value.id ? 1 : 0
  } else {
    title.value = "新增收货地址"
    // 新增地址把公司名清除
    pageStore.setCorporate("")
  }
})
onReady(getRegionData)
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0 0 24rpx;
}
.position-button {
  margin: 4rpx 0 4rpx 10rpx;
}
.submit-button-blank {
  height: 94rpx;
}
.submit-button {
  position: fixed;
  padding: 0 24rpx;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
