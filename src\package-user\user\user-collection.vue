<template>
  <view>
    <view class="page-header">
      <v-navbar title="我的收藏" back />
    </view>
    <scroll-view
      v-if="pageHeight"
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
      @scrolltolower="getCollectionList"
    >
      <view class="page-padding-top" />
      <template v-if="!refreshing">
        <v-loading-block v-if="loading" />
        <v-empty
          v-else-if="status === 'nomore' && collectionList.length === 0"
          src="/static/image/empty-collection.png"
        >
          暂无收藏
        </v-empty>
        <template v-else>
          <v-swipe
            v-for="collection in collectionList"
            :key="collection.id"
            margin="0 24rpx 24rpx"
          >
            <template #default>
              <view class="v-block">
                <v-product-item
                  :product="collection"
                  :is-on-sale="collection.isOnSale"
                  :is-in-stock="!!collection?.shelvesStock"
                  :is-deleted="collection.isDeleted"
                  @click="() => navigateToDetails(collection)"
                >
                  <template #name>
                    {{ collection.name }}
                  </template>
                  <template #spec>
                    {{ collection.specification }}
                  </template>
                  <template #default>
                    <view class="flex-center-between">
                      <v-price :price="collection.displayPrice">
                        /pcs
                      </v-price>
                      <v-icon
                        size="48rpx"
                        src="/static/icons/product/cart-button.png"
                        @click.stop="() => showSelect(collection)"
                      />
                    </view>
                  </template>
                </v-product-item>
              </view>
            </template>
            <template #button>
              <view
                class="delete-button flex-center-center"
                @click="() => delCollection(collection)"
              >
                <v-icon
                  size="36rpx"
                  src="/static/icons/components/delete.png"
                />
              </view>
            </template>
          </v-swipe>
          <v-loadmore v-if="!refreshing" :status="status" />
        </template>
      </template>
      <view class="page-padding-bottom" />
    </scroll-view>
    <v-product-select ref="selectRef" />
    <v-toast />
  </view>
</template>

<script setup lang="ts">
import { str2num } from "@/utils/type-transfer"

const { pageHeight } = usePageLayout()

const collectionList = ref<ProductCollectionItem[]>([])

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = () => {
  refreshing.value = true
  pageIndex.value = 1
  collectionList.value = []
  status.value = "loadmore"
  getCollectionList()
}
/** 初始化加载 */
const initCollectionList = async () => {
  if (loading.value) return
  loading.value = true
  await getCollectionList()
  loading.value = false
}

/** 跳转详情页 */
const navigateToDetails = async (product: ProductCollectionItem) => {
  if (product.isDeleted) return $toast.show("该商品已下架")
  if (!product.isOnSale) return $toast.show("该商品已下架")
  uni.navigateTo({
    url: `/package-product/product/product-details?productid=${product.id}`
  })
}

/** 分页数据 */
const pageIndex = ref(1)
const status = ref<"loadmore" | "loading" | "nomore">("loadmore")

/** 获取收藏列表 */
const getCollectionList = async () => {
  if (status.value !== "loadmore") return
  status.value = "loading"
  const response = await $api.collectList({
    orderField: "createdTime",
    pageIndex: pageIndex.value.toString(),
    pageSize: "10",
    sortType: "desc",
    requestParams: {}
  })
  switch (response.__error) {
    case undefined: {
      pageIndex.value += 1
      collectionList.value = [
        ...collectionList.value,
        ...(response.data.result?.map?.((collection) => {
          const unitAmount = collection.ncPackage
          const stockNum = str2num(collection.putawayStock)
          const stockCount = isNaN(stockNum) ? 0 : stockNum
          const item: ProductCollectionItem = {
            id: collection.productInfoId,
            image: collection.productInfoImg,
            name: collection.productSpecName || collection.productInfoName,
            specification:
              collection.productSpecModel || collection.productInfoSpec,
            unitPrice: str2num(collection.ncRetailPrice),
            unitAmount: 1,
            displayPrice: str2num(collection.displayUnitPrice),
            displayAmount: collection.displayPackage,
            isOnSale: collection.isOnSale === "1",
            isDeleted: collection.delFlag === "1",
            shelvesStock: Math.floor(Math.max(stockCount, 0) / unitAmount)
          }
          return item
        }) ?? [])
      ]
      const totalCount = str2num(response.data.totalCount)
      status.value =
        collectionList.value.length < totalCount ? "loadmore" : "nomore"
      break
    }
    default:
      status.value = "loadmore"
  }
  await nextTick()
  refreshing.value = false
}
/** 删除收藏 */
const delCollection = async (collection: ProductCollectionItem) => {
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "提示",
      content: "是否确认删除该收藏商品?",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return
  uni.showLoading({ title: "删除收藏商品中", mask: true })
  const response = await $api.deleteMemberCollect({
    productInfoId: collection.id
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined:
      uni.showToast({ title: "删除成功", icon: "none" })
      refresh()
  }
}

/** 选择商品 */
const selectRef = ref()
const showSelect = (product: ProductCollectionItem) => {
  if (product.isDeleted) return $toast.show("该商品已下架")
  if (!product.isOnSale) return $toast.show("该商品已下架")
  selectRef.value.showSelect({ id: product.id })
}

onLoad(() => initCollectionList())
</script>

<style lang="scss" scoped>
.v-block {
  margin: 0;
}
.collection-container {
  margin: 0 24rpx 24rpx;
}
.delete-button {
  background-color: #f06671;
  border-radius: 0 18rpx 18rpx 0;
  height: 100%;
  width: 124rpx;
  margin-left: -20rpx;
  padding-left: 20rpx;
}
</style>
