<template>
  <view class="v-navbar" :style="navbarStyle">
    <!-- 空白占位用 -->
    <view :style="{ backgroundColor: backgroundColor }">
      <view v-if="blank" class="status-bar-blank" />
      <view v-if="blank" class="nav-bar-blank" />
    </view>
    <view class="v-navbar-content">
      <!-- 状态栏 -->
      <view class="status-bar" />
      <!-- 导航栏 -->
      <view class="nav-bar flex-center-between">
        <template v-if="$slots.default">
          <!-- 这里给个安全区域范围 -->
          <view
            class="save-area flex-center"
            :style="{ width: menuButtonBounding.left + 'px' }"
          >
            <slot />
          </view>
        </template>
        <template v-else>
          <!-- 左边部分 -->
          <view class="left-part flex-center">
            <!-- 返回按钮 -->
            <v-icon
              v-if="back !== false"
              padding="12rpx"
              size="30rpx"
              :src="backIcon"
              @click="navigateBack"
            />
            <!-- 首页按钮 -->
            <v-icon
              v-if="home !== false"
              padding="12rpx"
              size="30rpx"
              src="/static/icons/navbar/home.png"
              @click="navigateHome"
            />
          </view>
          <!-- 标题 -->
          <view class="center-part text-1-row" :style="titleStyle">
            {{ title }}
          </view>
        </template>
        <!-- 右边部分，其实被胶囊按钮挡住了 -->
        <view
          class="right-part"
          :style="{
            width: menuButtonBounding.width + menuButtonBounding.right + 'px'
          }"
        />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { checkToken } from "@/utils/auth/token"
import request from "@/utils/http"
import { isFunction } from "lodash-es"

const props = withDefaults(
  defineProps<{
    // 标题
    title?: string
    // 是否显示返回按钮，可以接受一个函数通过页面自定内容
    back?: boolean |(() => void)
    // 是否显示首页按钮，可以接受一个函数通过页面自定内容
    home?: boolean |(() => void)
    // 是否存在高度占位
    blank?: boolean
    // 类型 default 或者 white
    type?: string
    backgroundColor?: string
  }>(),
  {
    title: undefined,
    back: false,
    home: false,
    blank: true,
    type: "default",
    backgroundColor: "white"
  }
)

const { navBarHeight, statusBarHeight, menuButtonBounding } = useNavbarLayout()

const navbarStyle = computed(() => {
  const navbar = `--nav-bar-height:${navBarHeight.value}px`
  const statusbar = `--nav-bar-status-bar-height:${statusBarHeight.value}px`
  return `${navbar}; ${statusbar}`
})

const type = toRef(props, "type")
const backIcon = computed(() => {
  switch (type.value) {
    case "white":
      return "/static/icons/navbar/back-white.png"
    default:
      return "/static/icons/navbar/back.png"
  }
})

const titleStyle = computed(() => {
  switch (type.value) {
    case "white":
      return "color: #FFF;"
    default:
      return "color: #231815;"
  }
})

const refreshUserData = async () => {
  if (!checkToken()) return uni.removeStorageSync("last_login")
  const lastLogin = uni.getStorageSync("last_login")
  const now = new Date().toDateString()
  if (lastLogin === now) return
  const response = await request.post<$res["findMemberInfoWeChat"]>({
    apiInfo: "获取用户信息",
    url: "/wxapp/member/findMemberInfoWeChat",
    data: {},
    notifyApiError: false,
    notifyRequestError: false,
    notifyResponseError: false
  })
  if (!response?.__error) {
    uni.setStorageSync("last_login", now)
  }
}
const pages = getCurrentPages()
onShow(() => {
  const lastPage = pages[pages.length - 1]
  switch (lastPage.route) {
    case "pages/user/user-page":
    case "package-points/points/points-center":
    case "package-points/points/points-page":
    case "package-points/points/points-record-list":
      // 这些页面会刷新用户信息，避免重复刷新
      break
    default:
      refreshUserData()
  }
})

/** 返回前一页 */
const back = toRef(props, "back")
const navigateBack = () => {
  if (isFunction(back.value)) return back.value()
  const pageLength = getCurrentPages().length
  if (pageLength <= 1) {
    navigateHome()
  } else {
    uni.navigateBack()
  }
}
/** 返回首页 */
const home = toRef(props, "home")
const navigateHome = () => {
  if (isFunction(home.value)) return home.value()
  uni.switchTab({ url: "/pages/index/index-page" })
}
</script>

<style lang="scss" scoped>
.v-navbar {
  .status-bar-blank {
    height: var(--nav-bar-status-bar-height);
  }
  .nav-bar-blank {
    height: var(--nav-bar-height);
  }
  .v-navbar-content {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 300;
    .status-bar {
      height: var(--nav-bar-status-bar-height);
    }
    .nav-bar {
      position: relative;
      height: var(--nav-bar-height);
      .save-area {
        position: relative;
        height: 100%;
      }
      .left-part {
        position: absolute;
        left: 24rpx;
        height: fit-content;
        width: fit-content;
      }
      .center-part {
        position: absolute;
        left: 50%;
        font-size: 32rpx;
        font-weight: bold;
        transform: translateX(-50%);
      }
      .right-part {
        position: absolute;
        right: 0;
        height: 100%;
      }
    }
  }
}
</style>
