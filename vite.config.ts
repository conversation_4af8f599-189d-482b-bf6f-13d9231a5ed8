import { defineConfig } from "vite"
import path from "path"
import uni from "@dcloudio/vite-plugin-uni"
import AutoImport from "unplugin-auto-import/vite"

const pathSrc = path.resolve(__dirname, "src")
const pathTypes = path.resolve(pathSrc, "types")

export default defineConfig({
  resolve: {
    alias: {
      "@": pathSrc
    },
    extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json"]
  },
  plugins: [
    uni(),
    AutoImport({
      imports: [
        "vue",
        {
          "@/composables/use-page-layout": [["default", "usePageLayout"]],
          "@/composables/use-navbar-layout": [["default", "useNavbarLayout"]],
          "@dcloudio/uni-app": ["onLoad", "onShow", "onHide", "onUnload", "onReady", "onLaunch"],
          "@/apis": [["default", "$api"]],
          "@/stores": [["default", "$store"]],
          "@/hooks/toast": [["default", "$toast"]]
        }
      ],
      dts: path.resolve(pathTypes, "auto-imports.d.ts"),
      eslintrc: {
        enabled: true,
        filepath: "./.eslintrc-auto-import.json",
        globalsPropValue: "readonly"
      }
    })
  ]
})
