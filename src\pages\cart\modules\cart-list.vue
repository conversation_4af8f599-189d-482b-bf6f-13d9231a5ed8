<template>
  <view class="page">
    <scroll-view
      v-if="pageHeight"
      scroll-y
      :scroll-into-view="scrollId"
      :scroll-top="scrollTop"
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refresh"
      @scroll="scrollHandle"
    >
      <view class="page-padding-top" />
      <template v-if="!refreshing">
        <v-loading-block v-if="loading" />
        <v-empty
          v-else-if="productList.length === 0"
          src="/static/image/empty-cart.png"
        >
          暂无商品
        </v-empty>
        <template v-else>
          <view
            v-for="(product, productIndex) in productList"
            :id="'product-item-' + productIndex"
            :key="product.id"
          >
            <ProductItem
              :product="product"
              @refresh="refresh"
              @select="$emit('select', product.id, product.number)"
            />
          </view>
        </template>
        <view v-if="!loading" class="cart-list-button">
          <view
            class="cart-list-button-item"
            :class="{ disabled: !productList.length }"
            @click="exportList"
          >
            导出购物车清单
          </view>
          <view class="cart-list-button-item" @click="importList">
            导入购物车清单
          </view>
        </view>
      </template>
      <view v-if="!paddingBottom" class="count-pad-blank" />
      <view v-else class="count-pad-blank" />
    </scroll-view>
    <view
      v-if="pageHeight"
      class="count-pad flex-center-between box-shadow"
      :class="{ 'padding-bottom': paddingBottom }"
    >
      <view class="select-all flex-center" @click="selectAll">
        <v-checkbox
          :checked="checkboxStatus === 'all'"
          :indeterminate="checkboxStatus === 'indeterminate'"
        />
        <view class="text text-22 text-light">
          全选
        </view>
      </view>
      <view
        class="count-pad-content flex-1 flex-col flex--center"
        @click.stop="showDiscount"
      >
        <view class="count-box flex-baseline">
          <view class="total-label text-24">
            合计
          </view>
          <v-price :price="totalPrice - discountPrice" size="36rpx" />
        </view>
        <view v-if="mostValueCoupon" class="coupon-box flex-center">
          <view class="coupont-label">
            共优惠
          </view>
          <v-price :price="discountPrice" size="20rpx" />
          <view class="icon">
            <v-icon size="14rpx" src="/static/icons/cart/right-icon.png" />
          </view>
        </view>
      </view>
      <v-button
        type="primary"
        height="100rpx"
        width="220rpx"
        font-size="30rpx"
        :disabled="selectedProductList.length === 0"
        @click="submit"
      >
        去结算
      </v-button>
    </view>
    <discount-popover
      ref="discountPopoverRef"
      :total-price="totalPrice"
      :coupon="mostValueCoupon"
      :padding-bottom="paddingBottom"
    />
  </view>
</template>

<script setup lang="ts">
import { num2str, str2num } from "@/utils/type-transfer"
import ProductItem from "./product-item.vue"
import DiscountPopover from "./discount-popover.vue"

defineProps({
  pageHeight: {
    type: Number,
    required: true
  },
  paddingBottom: {
    type: Boolean,
    default: false
  }
})

interface Emits {
  (event: "select", productId: string, number: number): void
}
defineEmits<Emits>()

const discountPopoverRef = ref() as Ref<InstanceType<typeof DiscountPopover>>
const showDiscount = () => {
  if (!mostValueCoupon.value) return
  discountPopoverRef.value.showPopover()
}

const pageStore = $store.page()
const selectedIdList = computed(() => pageStore.selectedIdList)
const productList = computed(() => pageStore.productList)
const selectedProductList = computed(() =>
  productList.value.filter((item) => selectedIdList.value.includes(item.id))
)

const checkboxStatus = computed(() => {
  if (selectedProductList.value.length === 0) return "none"
  if (productList.value.length === selectedProductList.value.length) {
    return "all"
  }
  return "indeterminate"
})

const totalPrice = computed(() => {
  let total = 0
  selectedProductList.value.forEach(
    (item) => (total += item.number * item.unitPrice * item.unitAmount)
  )
  return total
})

const exportList = () => {
  if (!productList.value.length) return
  let data = "cart-data:"
  data += productList.value.map((item) => `${item.id}:${item.number}`).join(";")
  uni.setClipboardData({
    data,
    success: () => {
      uni.showToast({ title: "购物车清单复制成功", icon: "none" })
    },
    fail: (err) => {
      console.log({ err })
      uni.showToast({ title: "购物车清单复制失败", icon: "none" })
    }
  })
}
const getListData = (str: string) => {
  if (!str || !str.startsWith("cart-data:")) return
  try {
    const itemList = str?.replace?.("cart-data:", "").split(";") ?? []
    return itemList
      .filter((item) => /^\d+:\d+$/.test(item))
      .map((item) => item.split(":"))
      .map((item) => ({ id: item[0], number: parseInt(item[1]) }))
  } catch {}
}
const loadListData = async (list: { id: string; number: number }[]) => {
  uni.showLoading({ mask: true })
  const valid: { id: string; number: number }[] = []
  let isValid = true
  for (let i = 0; i < list.length; i += 1) {
    if (list[i].number === 0) continue
    if (!isValid) break
    const current =
      productList.value.find((item) => item.id === list[i].id)?.number ?? 0
    const response = await $api.productDetail({ productInfoId: list[i].id })
    switch (response.__error) {
      case undefined: {
        const unitAmount = str2num(response.data.ncPackage)
        const stockNum = str2num(response.data.shelvesStock)
        const stockCount = isNaN(stockNum) ? 0 : stockNum
        const total = Math.floor(Math.max(stockCount, 0) / unitAmount)
        if (total >= current + list[i].number) {
          valid.push(list[i])
        } else {
          isValid = false
          uni.hideLoading()
          uni.showToast({ title: "导入的商品库存不足", icon: "none" })
        }
        break
      }
      default:
        uni.hideLoading()
        isValid = false
    }
  }
  if (!isValid) return
  for (let i = 0; i < valid.length; i += 1) {
    const response = await $api.addTheGoodsToTheCart({
      productInfoId: valid[i].id,
      quantity: valid[i].number.toString(),
      quantityRetail: "0"
    })
    switch (response.__error) {
      case undefined:
        pageStore.addProductSelect(valid[i].id)
    }
  }
  uni.hideLoading()
  uni.showToast({ title: "购物车清单导入成功", icon: "none" })
}
const importList = async () => {
  const result = await new Promise<boolean>((resolve) =>
    uni.showModal({
      title: "提示",
      content: "是否读取剪贴板的购物车清单数据?",
      success: (res) => resolve(res.confirm)
    })
  )
  if (!result) return false
  uni.getClipboardData({
    success: async (e) => {
      const data = getListData(e?.data)
      if (data?.length) {
        uni.showToast({ title: "购物车清单复制成功", icon: "none" })
        await loadListData(data)
        refreshCartList()
      } else {
        uni.showToast({ title: "未检测到购物车清单", icon: "none" })
      }
    },
    fail: (err) => {
      console.log({ err })
      uni.showToast({ title: "购物车清单复制失败", icon: "none" })
    }
  })
}

const selectAll = () => {
  discountPopoverRef.value.hidePopover()
  pageStore.toggleSelectAll()
}

const checkCartSumPrice = async () => {
  while (productList.value.some((item) => item.changingNumber)) {
    const result = await new Promise<boolean>((resolve) => {
      uni.$once("cartProductNumberChanged", (result: boolean) =>
        resolve(result)
      )
    })
    if (!result) return
  }
  const shoppingCartChooseList = selectedProductList.value.map((item) => ({
    productInfoId: item.id,
    quantity: num2str(item.serverNumber),
    quantityRetail: "0"
  }))

  // // 刷新购物车
  // const result = await refreshCartList()
  // if (!result) return
  // // 检查实际与缓存是否相同
  // const oldList = JSON.stringify(shoppingCartChooseList)
  // const newList = JSON.stringify(
  //   selectedProductList.value.map((item) => ({
  //     productInfoId: item.id,
  //     quantity: num2str(item.serverNumber),
  //     quantityRetail: "0"
  //   }))
  // )
  // if (oldList !== newList) {
  //   uni.showModal({
  //     title: "提示",
  //     content: "购物车发生了变化，请确认后结算",
  //     showCancel: false
  //   })
  //   return
  // }

  if (!shoppingCartChooseList.length) return
  const response = await $api.selectProductSumPrice({
    shoppingCartChooseList
  })
  switch (response.__error) {
    case undefined: {
      let price = 0
      let quantity = 0
      selectedProductList.value.forEach((item) => {
        price += item.number * item.unitPrice * item.unitAmount
        quantity += item.serverNumber
      })
      if (str2num(price.toFixed(2)) !== str2num(response.data.sumPrice)) {
        uni.showModal({
          title: "提示",
          content: "购物车价格错误",
          showCancel: false
        })
        return false
      }
      if (quantity !== str2num(response.data.sumQuantity)) {
        uni.showModal({
          title: "提示",
          content: "商品数量错误",
          showCancel: false
        })
        return false
      }
      return shoppingCartChooseList
    }
    default:
      return false
  }
}

onHide(() => uni.$off("cartProductNumberChanged"))

const submit = async () => {
  uni.showLoading({ title: "处理中", mask: true })
  const list = await checkCartSumPrice()
  if (!list) return uni.hideLoading()
  const response = await $api.orderConfirm({
    shoppingCartList: list.map((product) => ({
      productInfoId: product.productInfoId,
      quantity: product.quantity,
      quantityRetail: product.quantityRetail
    }))
  })
  uni.hideLoading()
  switch (response.__error) {
    case undefined: {
      // 直接用选中商品列表
      const list: ProductCartItem[] = selectedProductList.value.map(
        (product) => ({
          id: product.id,
          image: product.image,
          name: product.name,
          unitPrice: product.unitPrice,
          unitAmount: product.unitAmount,
          displayPrice: product.displayPrice,
          displayAmount: product.displayAmount,
          number: product.number,
          productInfoSpec: product.productInfoSpec,
          serverNumber: product.number,
          changingNumber: false,
          isOnSale: true,
          shelvesStock: product.shelvesStock,
          isDeleted: false
        })
      )
      pageStore.setConfirmOrder(
        response.data.orderToken,
        response.data.deliveryAddress,
        response.data.deliveryName,
        response.data.deliveryTel,
        response.data.enterpriseAbbr,
        response.data.sumPrice,
        list
      )
      uni.navigateTo({ url: "/package-order/order/order-confirm" })
      break
    }
    case "接口错误":
      refreshCartList()
  }
}

/** 是否加载中 */
const loading = ref(false)
/** 是否下拉刷新中 */
const refreshing = ref(false)
/** 刷新 */
const refresh = async () => {
  refreshing.value = true
  refreshCartList()
}
/** 刷新购物车 */
const refreshCartList = async () => {
  if (loading.value) return
  loading.value = true
  const result = await pageStore.refreshProductList()
  loading.value = false
  refreshing.value = false
  scrollToActive()
  return result
}
onShow(() => refreshCartList())

/** 控制一级滚动位置 */
const scrollId = ref("")
const scrollToIndex = async (index: number) => {
  scrollId.value = ""
  await new Promise((resolve) => nextTick(() => resolve(true)))
  scrollId.value = `product-item-${index}`
}

/** 控制滚动位置 */
let currentOffset = 0
const scrollTop = ref(0)
const scrollToTop = async () => {
  scrollTop.value = currentOffset
  await new Promise((resolve) => nextTick(() => resolve(true)))
  scrollTop.value = 0
}
const scrollHandle = (event: ScrollEvent) => {
  currentOffset = event.detail.scrollTop
}
/** 滚动到第一个激活的 */
const scrollToActive = () => {
  if (selectedProductList.value.length) {
    const firstSelected = selectedProductList.value[0]
    const index = productList.value.findIndex(
      (item) => item.id === firstSelected.id
    )
    scrollToIndex(index)
  } else {
    scrollToTop()
  }
}

/** 优惠券列表 */
const couponList = ref<CouponItem[]>([])
/** 可用的优惠券 */
const useAbleCouponList = computed(() => {
  if (!couponList.value) return []
  return couponList.value.filter((item) => {
    const priceLimit = parseInt(item.couponPriceLimit)
    const discount = parseFloat(item.couponValue)
    if (!discount) return false
    return priceLimit <= totalPrice.value
  })
})
/** 最优优惠券列表 */
const sortedCouponList = computed(() => {
  if (!couponList.value) return []
  const tempList = useAbleCouponList.value.concat([])
  const list = tempList.sort((a, b) => {
    // 面额较大在前
    const aValue = parseFloat(a.couponValue)
    const bValue = parseFloat(b.couponValue)
    if (aValue !== bValue) return bValue - aValue
    // 过期时间较后的在前
    const aTime = a.overdueTime
    const bTime = b.overdueTime
    if (aTime !== bTime) return aTime - bTime
    // 有区域限制的在前
    const aLimit = Boolean(a.couponAreaLimit)
    const bLimit = Boolean(b.couponAreaLimit)
    if (aLimit !== bLimit) return !aLimit && bLimit ? 1 : -1
    // 按照id排序
    const aId = parseInt(a.couponMemberRelationId)
    const bId = parseInt(b.couponMemberRelationId)
    return aId - bId
  })
  // 只取前100项
  return list?.slice(0, 100)
})
/** 最佳优惠券 */
const mostValueCoupon = computed(() => {
  if (!sortedCouponList.value?.length) return
  return sortedCouponList.value[0]
})
/** 优惠金额 */
const discountPrice = computed(() => {
  if (!mostValueCoupon.value) return 0
  return parseFloat(mostValueCoupon.value.couponValue)
})
/** 获取优惠券 */
const getCouponList = async () => {
  const response = await $api.getAllCouponListByUserId({})
  switch (response.__error) {
    case undefined: {
      couponList.value = response.data
    }
  }
}
onShow(() => {
  getCouponList()
})

defineExpose({ refresh: refreshCartList })
</script>

<style lang="scss" scoped>
.page {
  position: relative;
  .count-pad-blank {
    height: 124rpx;
  }
  .count-pad-blank {
    height: 124rpx;
    padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
  .count-pad {
    position: absolute;
    bottom: 24rpx;
    left: 24rpx;
    right: 24rpx;
    padding-left: 18rpx;
    height: 100rpx;
    border-radius: 100vw;
    background-color: white;
    z-index: 1000;
    &.padding-bottom {
      bottom: calc(30rpx + constant(safe-area-inset-bottom));
      bottom: calc(30rpx + env(safe-area-inset-bottom));
    }
    .select-all {
      margin-right: 24rpx;
      line-height: 36rpx;
      .text {
        margin-left: 10rpx;
      }
    }
    .count-pad-content {
      height: 100%;
      .count-box {
        margin-bottom: 8rpx;
        .total-label {
          margin-right: 14rpx;
        }
      }
      .coupon-box {
        font-size: 20rpx;
        margin-top: -10rpx;
        .coupont-label {
          color: #999999;
          margin-right: 14rpx;
        }
        .icon {
          margin-left: 8rpx;
        }
      }
    }
  }
}
.blank-padding-bottom {
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom));
}

.cart-list-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40rpx;
}
.cart-list-button-item {
  line-height: 2;
  color: rgba(0, 91, 171, 0.8);
  &.disabled {
    color: #999;
  }
}
</style>
