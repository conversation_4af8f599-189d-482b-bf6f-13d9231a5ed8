<template>
  <view>
    <view class="page-header">
      <v-navbar title="支付结果" back />
    </view>
    <scroll-view
      scroll-y
      :style="{ height: `${pageHeight}px` }"
      enable-back-to-top
    >
      <view class="page-padding-top" />
      <view class="icon flex-center-center">
        <v-image height="90rpx" width="90rpx" src="/static/image/success.svg" />
      </view>
      <view class="main text-bold text-center">
        支付成功
      </view>
      <view class="sub flex-center-evenly">
        <v-button
          border="1px solid #005BAC"
          color="#005BAC"
          height="82rpx"
          width="300rpx"
          @click="switchTabToIndex"
        >
          返回首页
        </v-button>
        <v-button
          type="primary"
          height="82rpx"
          width="300rpx"
          @click="reLaunchToDetails"
        >
          订单详情
        </v-button>
      </view>
      <view class="page-padding-bottom" />
    </scroll-view>
    <v-toast />
  </view>
</template>

<script setup lang="ts">
const { pageHeight } = usePageLayout()

const switchTabToIndex = () => {
  uni.switchTab({ url: "/pages/index/index-page" })
}

let orderNo: string
const reLaunchToDetails = () => {
  if (!orderNo) return
  uni.reLaunch({ url: `/package-order/order/order-details?orderno=${orderNo}` })
}

onLoad((query) => {
  const page = getCurrentPages()
  const current = page[page.length - 1] as unknown as Record<string, Record<string, unknown>>
  console.log("当前路径:")
  console.log(current?.$page?.fullPath)

  orderNo = query?.orderno ?? ""
  if (!orderNo) {
    uni.switchTab({ url: "/pages/index/index-page" })
  }
})
</script>

<style lang="scss" scoped>
.icon {
  padding-top: 70rpx;
  padding-bottom: 20rpx;
}
.main {
  padding: 20rpx 0;
  font-size: 40rpx;
}
.sub {
  padding: 30rpx 24rpx;
}
</style>
